import "core-js";
import Vue from "vue";
import VueI18n from "vue-i18n";
import App from "./src/App";
import "./src/main.scss";
import Vuex from "vuex";
import messages from "./message";
import VueCompositionAPI from '@vue/composition-api'
Vue.use(VueCompositionAPI)
Vue.use(VueI18n);
Vue.use(Vuex);
import Input from 'element-packages/input'
import Form from 'element-packages/form'
import FormItem from 'element-packages/form-item'
import Button from 'element-packages/button'
import ButtonGroup from 'element-packages/button-group'
import Alert from 'element-packages/alert'
import Dialog from 'element-packages/dialog'


const components = [
  FormItem,
  Alert,
  Form,
  Button,
  ButtonGroup,
  Input,
  Dialog
]

components.map(component => {
  Vue.component(component.name, component)
})


const store = new Vuex.Store({
  modules: {
    group: {
      namespaced: true,
      state: {
        tags: {},
        caps: {},
        isOnlineBillingOrg: false,
        groupName: "",
        groupId: "",
        telephonyConfs: []
      },
      mutations: {
        setGroup(state, { tags, caps, isOnlineBillingOrg, groupName, id, contextPath }) {
          state.tags = tags;
          state.caps = caps;
          state.isOnlineBillingOrg = isOnlineBillingOrg;
          state.groupName = groupName;
          state.groupId = id;
          state.contextPath = contextPath
        },
        setTelephonyConf(state, value = []){
          state.telephonyConfs = value;
        }
      },
      getters: {
        brandingLogoLeft(state) {
          return state.contextPath + (state.tags.Main_Color_Rectangle_Logo_Left || '');
        },
        brandingLogoCenter(state) {
          return state.contextPath + (state.tags.Main_Color_Rectangle_Logo || '');
        },
        groupBasicInfo(state) {
          return {
            id: state.groupId,
            name: state.groupName,
          };
        },
        groupTags(state) {
          return state.tags;
        },
        groupCaps(state) {
          return state.caps;
        },
        isOnlineBillingOrg(state) {
          return state.isOnlineBillingOrg;
        },
        groupTelephonyConfs(state) {
          return state.telephonyConfs;
        },
      },
    },
  },
});

export const i18n = new VueI18n({
  locale: getLocale(),
  fallbackLocale: "en",
  messages,
});

function getLocale() {
  let locale;
  let key = "mx:CurrentLanguage";
  locale = localStorage.getItem(key);
  // if there isn't localStorage value, get from navigater
  if (!locale) {
    locale = (navigator.languages && navigator.languages.length
      ? navigator.languages[0]
      : navigator.language || navigator.userLanguage || "en-US"
    ).toLowerCase();
  }
  // process special case, as our server use 'en','es','fr','ja','pt','zh','zh-tw',
  // we should convert such as 'en-US' to 'en', 'fr-CA' to 'fr' etc.
  if (locale) {
    let items = locale.split("-");
    if (items[0] === "zh") {
      // when only 'zh' or 'zh-CN', set locale to 'zh'
      // other cases, set locale to 'zh-tw'
      if (!items[1] || /cn/gi.test(items[1])) {
        locale = "zh";
      } else {
        locale = "zh-tw";
      }
    } else {
      locale = items[0];
    }
  }
  return locale;
}

let vm = new Vue({
  el: "#app",
  i18n,
  store,
  components: { App },
  template: "<App/>",
});
