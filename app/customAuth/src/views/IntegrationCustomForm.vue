<template>
  <div class="form-container">
    <img 
      :src="appLogo || DefaultIntegrationApp" 
      class="logo"/>
    <div>
      <h1 class="mx-text-t2">{{ $t('connect_to_app') }} {{ appBasicInfo.name }}</h1>
      <p class="subtitle mx-text-c2">{{ appBasicInfo.description }}</p>
    </div>
    <transition name="fade-out">
      <div v-if="isError" class="error-box">
        <el-alert type="warning" :show-icon="true" :closable="false" :center="false"  :title="errorBody.title" :description= 'errorBody.subtitle' />      
      </div>
    </transition>
    <el-form :model="formData" ref="formRef">
      <el-form-item
        v-for="(field, index) in formFields"
        :key="index"
        :label="field.label"
        :prop="field.key"
        :rules="[{ required: field.required, message: `${field.label} is required`, trigger: 'blur' }]">
        <el-input v-model="formData[field.key]" />
        <FormElementSupporting v-if="field.tip" :content="field.tip"></FormElementSupporting>
      </el-form-item>

      <!-- Submit Button -->
      <el-button :disabled="isLoading" class="submit-button" type="primary" @click="handleSubmit">{{ $t('connect') }}</el-button>
    </el-form>
  </div>
</template>
<script>
import DefaultIntegrationApp from '@views/theme/src/images/base_action/DefaultIntegrationApp.png'
import FormElementSupporting from '@views/form/components/FormElementSupporting.vue'
import { MxIntegrationSDK } from 'integrationSDK';
export default {
  components:{FormElementSupporting},
  props: {
    appId: {
      type: String,
      default: '',
      required: true,
    },
    appBasicInfo: {
      type: Object,
      default: ()=>{
        name: '';
        description: '';
      }
    },
    appLogo: {
      type: String,
      default: ''
    },
    formFields: {
      type: Array,
      required: true,
    }
  },
  data() {
    return {
      formData: {},
      isError: false,
      errorBody: {
        title: '',
        subtitle: ''
      },
      isLoading: false,
      DefaultIntegrationApp
    };
  },
  created() {
    this.initializeFormData();
  },
  methods: {
    initializeFormData() {
      this.formFields.forEach((field) => {
        this.$set(this.formData, field.key, '');
      });
    },
    resetError () {
      this.isError = false
      Object.assign(this.errorBody, {
        title: '',
        subtitle: '',
      })
    },
    handleSubmit () {
      this.isLoading = true
      this.$refs.formRef.validate(async (valid) => {
        if (valid) {
          try{
            const authResult = await MxIntegrationSDK.validateCustomAccount(this.appId,this.formData);
            if (authResult.error || authResult.data.error) {
              // Handle error from third-party app API || user defined error
              const errBody = authResult.error || authResult.data.error|| {};
              this.transformError({
                title: errBody?.title || this.$t('something_went_wrong_try_again'),
                subtitle: errBody?.description || '',
              });
              return;
            } else {
              // Success
              this.resetError();
              this.isLoading = false
              this.$emit('submit', authResult);
            }
          }catch (error) {
            // integration error
            this.transformError({
              title: this.$t('something_went_wrong_try_again'),
              subtitle: '',
            });
            return;
          }
        } else {
          // Validation error
          this.transformError({
            title: this.$t('something_went_wrong_try_again'),
            subtitle: '',
          });
          return;
        }
      });
    },    
    transformError (errBody = {}) {
      const title = errBody.title
      const subtitle = errBody.subtitle
      this.isError = true
      this.isLoading = false
      Object.assign(this.errorBody, {
        title,
        subtitle
      })
      setTimeout(() => {
        this.isError = false
        this.errorBody = {
          title: '',
          subtitle: ''
        };
      }, 2000);
    },
  }
};
</script>

<style lang="scss" scoped>
@import '../../../../views/theme/src/common/color.scss';

/* Container styling */
.form-container {
  background-color: $mx-color-var-bg-tertiary;
  width: 550px;
  margin: 0 auto;
  padding: 40px 75px;
  border-radius: 12px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

/* Logo styling */
.form-container .logo {
  width: 128px;
  height: 128px;
  margin: 0 auto;
  border-radius: 12px;
}

/* Title and subtitle */
.form-container h1,.form-container p.subtitle {
  margin: 0px;
}

/* Input field styles */
.form-container ::v-deep label {
  margin: 0px;
  width: 100%;
  text-align: left;
  font-size: 14px;
  line-height: 20px;
  font-weight: 590;
  color: $mx-color-var-text-secondary;
}
.form-container ::v-deep .el-form-item.is-required:not(.is-no-asterisk) > .el-form-item__label::before {
  display: none!important;
}
.form-container ::v-deep .el-form-item.is-required:not(.is-no-asterisk) > .el-form-item__label::after {
  content: "*";
  color: $mx-color-var-text-secondary;
  margin-left: 4px;
}

.form-container ::v-deep .el-form-item__error{
  padding-top: 0px!important;
  margin-left: 0px!important;
}

.form-container .help-text {
  color: $mx-color-var-text-secondary;
  line-height: 20px;
  margin: 0px;
  text-align: left;
}

.submit-button {
  width: 100%;
}
/* Fade out transition */
.fade-out-leave-active {
  transition: opacity 0.5s ease;
}
.fade-out-leave-to {
  opacity: 0;
}

::v-deep .el-alert__content {
  text-align: left;
}
</style>
