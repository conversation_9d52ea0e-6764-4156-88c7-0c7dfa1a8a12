import './storageAccessDenied'
const WEB_VUE_LOGGER = MxLogger.create('Vue (web)')
import Vue from 'vue'

if (process.env.NODE_ENV !== 'development')  {
	Vue.config.errorHandler = function (err, vm, info) {
		// handle error
		// `info` is a Vue-specific error info, e.g. which lifecycle hook
		// the error was found in. Only available in 2.2.0+
		console.log(err.stack, info)
		WEB_VUE_LOGGER.error(err.stack, info)
	}

	Vue.config.warnHandler = (msg, vm, trace) => {
		// `trace` is the component hierarchy trace
		// This only works during development and is ignored in production.
		WEB_VUE_LOGGER.warn(msg, trace);
	}
}

import './src/composition-api'

import { createPinia, PiniaVuePlugin } from 'pinia'

Vue.use(PiniaVuePlugin)
const pinia = createPinia()



import {BrowserUtils} from '@commonUtils'
// if (BrowserUtils.isIE) {
//   import(/* webpackChunkName: 'core.js' */ 'core-js')
// }


import router from './router'
import i18n from '@views/i18n/mepIndex'
import '@views/theme/src/mep.scss'
import '@views/theme/src/fonts/dist/moxtra-icon.css';
import BaseModal from '@views/common/components/modals/BaseModal.vue'
import PlainModal from '@views/common/components/modals/PlainModal.vue'
import MxFloatingInput from '@views/common/components/input/MxFloatingInput.vue'
import directives from '@views/common/directives'
import VmProgress from 'vue-multiple-progress'
import fullscreen  from 'vue-fullscreen'
import Pagination from 'element-packages/pagination';
import Dialog from 'element-packages/dialog';
import Autocomplete from 'element-packages/autocomplete';
import Dropdown from 'element-packages/dropdown';
import DropdownMenu from 'element-packages/dropdown-menu';
import DropdownItem from 'element-packages/dropdown-item';
import Menu from 'element-packages/menu';
import Submenu from 'element-packages/submenu';
import MenuItem from 'element-packages/menu-item';
import Tree from 'element-packages/tree';
import Input from 'element-packages/input';
import InputNumber from 'element-packages/input-number';
import Radio from 'element-packages/radio';
import RadioGroup from 'element-packages/radio-group';
import Checkbox from 'element-packages/checkbox';
import CheckboxGroup from 'element-packages/checkbox-group';
import Switch from 'element-packages/switch';
import Select from 'element-packages/select';
import Slider from 'element-packages/slider';
import OptionGroup from 'element-packages/option-group';
import Option from 'element-packages/option';
import Button from 'element-packages/button';
import ButtonGroup from 'element-packages/button-group';
import Table from 'element-packages/table';
import TableColumn from 'element-packages/table-column';
import DatePicker from 'element-packages/date-picker';
import Popover from 'element-packages/popover';
import Tooltip from 'element-packages/tooltip';
import Breadcrumb from 'element-packages/breadcrumb';
import BreadcrumbItem from 'element-packages/breadcrumb-item';
import Form from 'element-packages/form';
import FormItem from 'element-packages/form-item';
import Tabs from 'element-packages/tabs';
import TabPane from 'element-packages/tab-pane';
import Alert from 'element-packages/alert';
import Loading from 'element-packages/loading';
import Row from 'element-packages/row';
import Col from 'element-packages/col';
import Upload from 'element-packages/upload';
import Progress from 'element-packages/progress';
import Badge from 'element-packages/badge';
import Collapse from 'element-packages/collapse';
import CollapseItem from 'element-packages/collapse-item';
import Container from 'element-packages/container';
import Header from 'element-packages/header';
import Aside from 'element-packages/aside';
import Main from 'element-packages/main';
import Tag from 'element-packages/tag';
import Drawer from 'element-packages/drawer';
import MxUserAvatar from '@views/common/components/userAvatar/MxUserAvatar';

import App from './src/App'
import store from '@appStore'
import {EventBusPlugin} from '@views/common/plugins/eventBus'
import {MxConfirmPlugin, MxMessagePlugin, MxOptionConfirmPlugin, MxAlertPlugin} from '@views/common/plugins/popup'
import ClickPlugin from '@views/common/plugins/clickEvent'
import TimerPlugin from '@views/common/plugins/timer'

import FlexGrid from '@views/common/components/grid'
import '@views/theme/src/images/menus'
import '@views/theme/src/images/svgIcon'
import highlight from '@views/demoBlock/utils/highlight'
import {initAriaFocusIndicator} from '@views/common/accessibility'
import checkBuildTxt from '../../views/common/components/checkBuildTxt'

import VueLazyload from 'vue-lazyload'
import {initTestHelper} from '../../views/common';
// import {MXSetting} from './src/cslSettings/mxSetting'

Vue.use(VueLazyload)
import {initPopupContainer, createApplication} from '@views/common/useComponent';
import ShowChatAPIWrapper from './src/QIankun.vue'

//import MxImage from '../../views/common/components/imageHOC'

const components = [
	Alert,
	Autocomplete,
	Collapse,
	CollapseItem,
	FormItem,
	Form,
	Header,
	ButtonGroup,
	Input,
	Button,
	Checkbox,
	Container,
	Aside,
	VmProgress,
	Menu,
	Main,
	MenuItem,
	Radio,
	Table,
	TableColumn,
	RadioGroup,
	Col,
	Row,
	Dialog,
	DatePicker,
	Upload,
	Select,
	OptionGroup,
	Option,
	Switch,
	Slider,
	Popover,
	Progress,
	Tooltip,
	Badge,
	Tree,
	Tag,
	Tabs,
	TabPane,
	Dropdown,
	DropdownMenu,
	DropdownItem,
	Pagination,
	Submenu,
	CheckboxGroup,
	InputNumber,
	Breadcrumb,
	BreadcrumbItem,
	Drawer,
	MxUserAvatar
]

const customComponents = [
	BaseModal,
	PlainModal,
	MxFloatingInput
]

const requireComponent = require.context(
	'@views/common/cards',
	true,
	/\w+\.vue$/
)
requireComponent.keys().forEach(fileName => {
	const componentConfig = requireComponent(fileName)
	const componentName = fileName.replace(/^.+?(\w+)\.vue$/, '$1')
	Vue.component(componentName, componentConfig.default || componentConfig)
})


components.map(component => {
	Vue.component(component.name, component)
})

customComponents.map(component => {
	Vue.component(component.name, component)
})

const plugins = [
	FlexGrid,
	EventBusPlugin,
	MxConfirmPlugin,
	MxOptionConfirmPlugin,
	MxMessagePlugin,
	MxAlertPlugin,
	directives,
	ClickPlugin,
	TimerPlugin
]
plugins.map(plugin => {
	Vue.use(plugin)
})
// Vue.use(preview)
// Vue.use(binder)
Vue.use(Loading)
Vue.use(fullscreen)
Vue.use(highlight)
//Vue.use(MxImage)

Vue.prototype.$renderVueComponent = (Component,data)=>{
  const container = document.createElement('div');
  window.mxBodyContainer.appendChild(container)
  return new Vue({
    i18n,
    el:container,
    render:h => {
      return h(Component,data)
    }
  })
}

// below is used to fix drag and drop will open a new tab issue in Firefox
if (BrowserUtils.isFirefox) {
  document.body.ondrop = function (event) {
    event.preventDefault()
    event.stopPropagation()
  }
}

const scrollDetectorElement = document.querySelector('.scroll-detector')
if (scrollDetectorElement) {
  if(scrollDetectorElement.clientWidth !== scrollDetectorElement.offsetWidth) {
	document.getElementById('scroll-for-windows').innerHTML = `
	*::-webkit-scrollbar {
\t\t  width: 8px;
\t\t  height: 6px;
\t  }	
	`
  }
}

const browserName = (BrowserUtils.browser.name || '').toLowerCase();
document.body.className += ' bw-' + browserName
document.body.className += ` bw-${browserName}-${BrowserUtils.browserVersion}`

if (BrowserUtils.isMobile) {
  document.body.className += ' bw-mobile'
} else if (BrowserUtils.isTablet) {
	document.body.className += ' bw-tablet'
}


const ctxReg = /\/web\/?$/gi
const pathName = location.pathname
if (pathName && ctxReg.test(pathName)) {
	const contextPath = pathName.replace(ctxReg, '').replace(/\/\//gi, '/')
	if (contextPath && contextPath !== '/') {
		const reqPath = location.origin.replace(/https:\/\//gi, '') + contextPath
		const serviceUrl = `https://${reqPath}`
		const socketUrl = `wss://${reqPath}`
		store.dispatch('identity/initializeISDK', {serviceUrl, socketUrl, contextPath})
	}
}

let preInMeetingObj = {}
window.addEventListener('storage', ()=>{
	try {
		let key = localStorage.getItem('isInMeeting')
		if (key) {
			let obj = JSON.parse(key)
			if (obj) {
				store.commit('user/setInMeetingFlag', obj)

				// MV-8430 Detect which meeting has just started
				for (let key in obj) {
					// filter out timestamp changed session_key
					if (preInMeetingObj[key] && obj[key].timestamp - preInMeetingObj[key].timestamp > 0) {
						store.dispatch('meet/flushMeetLog', key)
					}
				}
			}
			preInMeetingObj = obj
		}
	} catch (e) {

	}
})
initAriaFocusIndicator()
// MXSetting.init(store)

export function createApp (props){
	store.dispatch('identity/loginWithAccessToken', props.accessToken).then(()=>{
		store.dispatch('websdk/initWithoutIframe', props.config)
		const vm = createApplication({
			container: props.container.querySelector('#moxtra-app'),
			router,
			i18n,
			pinia,
			store,
			App: ShowChatAPIWrapper,
			props: {
				binderId: props.binderId,
			}
		})
		window.mxBodyContainer = props.container
		window.vm = vm
	})
}
