<template>
  <section v-if="isReady">
    <MxUploaderManager />
    <FlowWorkspaceDetail
      v-if="isShowFlowDetail"
      :board-id="binderId"
      :need-show-welcome="true"
      welcome-page-style="internal-view" />
    <MxBinderDetail
      v-else
      :binder-id="binderId"
      height="100%" />
  </section>
</template>

<script>
import {mapActions, mapGetters, mapState} from 'vuex'
import { loadLanguageAsync, getLocale } from '@views/i18n/mepIndex'
import {ObjectUtils} from '@commonUtils'
import FlowWorkspaceDetail from '@views/flowWorkspace/src/FlowWorkspaceDetail.vue'
import { useBoardCommonActionsStore } from '@views/stores/boardCommonActions'
import { mapActions as piniaMapActions, mapState as piniaMapState } from 'pinia'
import MxBinderDetail from '@views/binderDetail/src/MxBinderDetail'
import MxUploaderManager from '@views/common/components/uploader/MxUploadManager'
import websdkController from '@controller/websdk/WebsdkController'

export default {
  name: 'App',
  components: {FlowWorkspaceDetail, MxBinderDetail, MxUploaderManager},
  props: {
    binderId: {
      type: String,
      default: ''
    },
  },
  data () {
    return {
      isShowFlowDetail: false,
      isReady: false,
    }
  },
  computed: {
    ...mapGetters('user', ['currentUser']),
    ...mapGetters('group', ['groupBasicInfo']),
    ...mapGetters('application', ['networkState']),
    ...mapState('identity', ['workspaceLink'])
  },
  watch: {
    groupBasicInfo (info, oldInfo) {
      if (info && info.tags && oldInfo) {
        const term_client = ObjectUtils.getByPath(this.groupBasicInfo, 'tags.B_Term_Set_Client')
        const term_internal = ObjectUtils.getByPath(this.groupBasicInfo, 'tags.B_Term_Set_Internal')
        const term_workspace = ObjectUtils.getByPath(this.groupBasicInfo, 'tags.B_Term_Set_Workspace')


        if (!term_client && !term_internal && !term_workspace) {
          this.$bus.$emit('LOAD_TERM_OBJECT_READY')
          return
        }
        const term_client_old = ObjectUtils.getByPath(oldInfo, 'tags.B_Term_Set_Client')
        const term_internal_old = ObjectUtils.getByPath(oldInfo, 'tags.B_Term_Set_Internal')
        const term_workspace_old = ObjectUtils.getByPath(oldInfo, 'tags.B_Term_Set_Workspace')

        if (term_client !== term_client_old || term_internal !== term_internal_old || term_workspace !== term_workspace_old) {
          const termObj = (term_client || term_internal || term_workspace) ? { term_client, term_internal , term_workspace} : null
          termObj && loadLanguageAsync(getLocale(), termObj).then(()=>{
            this.$bus.$emit('LOAD_TERM_OBJECT_READY')
          })
        }
      }
    },
    '$route': {
      handler (newVal, oldVal) {
        this.setRouterHistory({prev: oldVal.path, curr: newVal.path})
      }
    },
    watch: {
      binderId (newVal, oldVal) {
        alert(oldVal, newVal)
        this.isFlowWorkspace(newVal).then((res) => {
          this.isReady = true
          this.isShowFlowDetail = res
        })
      }
    }
  },
  created () {
    this.isFlowWorkspace(this.binderId).then((res) => {
      this.isReady = true
      this.isShowFlowDetail = res
    })
    window.tempLog = []
    window.tempLog.push({
      event: 'App Inits',
      userAgent: window.navigator.userAgent
    })
    const locale = getLocale()
    loadLanguageAsync(locale)
    this.initUserState()
    this.watchNetworkState()
  },
  methods: {
    ...mapActions('identity', ['initUserState', 'requestJoinBoard']),
    ...mapActions('application', ['setRouterHistory']),
    ...piniaMapActions(useBoardCommonActionsStore, ['isFlowWorkspace']),
    watchNetworkState () {
      window.addEventListener('online', () => {
        this.messageInstance && this.messageInstance.close()
      })
      window.addEventListener('offline', () => {
        const message = this.$t('no_internet_auto_try_reconnect')
        this.messageInstance = this.$mxMessage({
          type: 'warning',
          message: message,
          duration: 0
        })
      })
    }
  }
}
</script>

<style>
  body, .body {
    height: 100%;
  }

  #app {
    height: 100%;
    /*font-family: 'Avenir', Helvetica, Arial, sans-serif;*/
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    /*text-align: center;*/
    /*color: #2c3e50;*/
    /*margin-top: 60px;*/
  }
  .navbar {
      display: flex;
      background-color: #e9e9e9;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
      padding: 0 2rem;
    }

    .logo {
      display: flex;
      align-items: center;
      padding: 0 1rem;
      font-weight: bold;
      font-size: 1.2rem;
      color: #333;
    }

    .nav-tabs {
      display: flex;
      list-style: none;
    }

    .nav-tabs li {
      margin: 0;
    }

    .nav-tabs a {
      display: block;
      padding: 1rem 1.5rem;
      text-decoration: none;
      color: #555;
      font-weight: 500;
      border-right: 1px solid #ddd;
      border-left: 1px solid #ddd;
      margin-right: -1px;
      transition: background-color 0.2s ease;
    }

    .nav-tabs li:first-child a {
      border-left: 1px solid #ddd;
    }

    .nav-tabs a:hover {
      background-color: #f0f0f0;
    }

    .nav-tabs a.active {
      background-color: #fff;
      color: #333;
      border-bottom: none;
      position: relative;
      padding-bottom: calc(1rem + 3px);
      margin-bottom: -3px;
    }

    .secondary-nav {
      margin-left: auto;
      display: flex;
      align-items: center;
    }

    .secondary-nav a {
      margin-left: 1rem;
      color: #555;
      text-decoration: none;
    }

    .secondary-nav a:hover {
      color: #333;
      text-decoration: underline;
    }
</style>
