
import 'core-js'
import { arrayAtPolyfill } from '@views/common/utils/polyfill'
import { IframeRemoteCall } from "@commonUtils/IframeRemoteCall"
import { ajaxPost } from '/app/tinyProj/common/request.js'
import domApi from '@views/common/utils/dom'

let urlSearch = location.search

const getContextPath = () => {
  const ctxReg = /\/web\/?$/gi
  const pathName = location.pathname
  if (pathName && ctxReg.test(pathName)) {
    const contextPath = pathName.replace(ctxReg, '').replace(/\/\//gi, '/')
    if (contextPath && contextPath !== '/') {
      if (sessionStorage.getItem('mx:contextPath') !== contextPath) {
        sessionStorage.setItem('mx:contextPath', contextPath)
        return domApi.registerServiceWorker().then(()=>{
          location.reload()
          return Promise.resolve()
        })
      } else {
        return domApi.registerServiceWorker()
      }
    } else {
      return Promise.resolve()
    }
  } else {
    return Promise.resolve()
  }
}

//below are hooks for micro app. Do not touch it in normal web app / mepsdk 

export async function bootstrap () {
  console.log('micro app bootstraped');
}

export async function mount (props) {
	__webpack_public_path__ = window.__INJECTED_PUBLIC_PATH_BY_QIANKUN__;
  window.qianKunProps = props
  import('./qiankun.js').then(({createApp}) => {
    createApp(props)
  })
}

export async function unmount () {
  console.log('micro app unmounted');
  window.vm.$destroy()
}

export async function update (props){
  console.log('micro app updated', props);
  window.vm.updateAppProps(props)
}

if (!window.__POWERED_BY_QIANKUN__) {
  const optionPost = {"type":"GROUP_REQUEST_READ","object":{},"params":[{"name":"OUTPUT_FILTER_STRING","string_value":"/group/tags"},{"name":"OUTPUT_FILTER_STRING","string_value":"/group/group_caps"},{"name":"OUTPUT_FILTER_STRING","string_value":"/group/local"},{"name":"OUTPUT_FILTER_STRING","string_value":"/group/integrations"},{"name":"OUTPUT_FILTER_STRING","string_value":"/group/partner"},{"name":"OUTPUT_FILTER_STRING","string_value":"/group/roles"},{"name":"OUTPUT_FILTER_STRING","string_value":"/group/routing_config"}]}
  if (urlSearch.indexOf('type=acdsdk') >= 0 || window.location.href.indexOf('type=acdsdk') >= 0) {
    //it is actually cardsdk, but we will keep this as it is already being used
    import('./index.js')
  } else {
    getContextPath().then(() => {
      ajaxPost('/board', optionPost, (res)=>{
        const webVersion = res.object.group?.web_version
        const { pathname,href } = window.location
        let matchedPath = pathname && pathname.split('/')
        const domainList = href.split(pathname)
        if(webVersion && webVersion !== 'web' && matchedPath[matchedPath.length - 2] ==='web'){
          matchedPath[matchedPath.length - 2] = `web/${webVersion}`
          window.location.href = domainList[0]+ matchedPath.join('/') + domainList[1]
        }else{
          // pathname: /web/webversion/XXX
          let currentWebversion = matchedPath[2] ? matchedPath[2] : matchedPath[1]
          let WebVersionInSetting = webVersion || 'web'
          if( matchedPath[1] === 'web' && currentWebversion && WebVersionInSetting && WebVersionInSetting !== currentWebversion){
            if(WebVersionInSetting === 'web'){
              matchedPath.splice(2, 1)
            }else{
              matchedPath[2] = WebVersionInSetting
            }
            window.location.href = domainList[0]+ matchedPath.join('/') + domainList[1]
          }else{
            import('./index.js').then(_ => {
              appDocumentReady()
            })
          }
        }
      },(err)=>{
        import('./index.js').then(_ => {
          appDocumentReady()
        })
      })
    })
  }
  const appDocumentReady = () => {
    const inIframe = () => {
      try {
          return window.self !== window.top;
      } catch (e) {
          return true;
      }
    }
    if (inIframe()) {
      const irc = new IframeRemoteCall(window, window.parent)
      irc.call('app_ready')
    }
    arrayAtPolyfill()
  }
}


