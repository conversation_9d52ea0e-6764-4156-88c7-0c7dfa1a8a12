import 'core-js'
import VueCompositionAP<PERSON> from '@vue/composition-api'

Vue.use(VueCompositionAPI)
import {BrowserUtils} from '@commonUtils'
import Vue from 'vue'
import i18n from '@views/i18n/mepIndex'
import '@views/theme/src/fonts/dist/moxtra-icon.css'
import './src/main.scss'
import BaseModal from '@views/common/components/modals/BaseModal.vue'
import MxFloatingInput from '@views/common/components/input/MxFloatingInput.vue'
import directives from '@views/common/directives'
import VmProgress from 'vue-multiple-progress'
import Dialog from 'element-packages/dialog'
import Autocomplete from 'element-packages/autocomplete'
import Dropdown from 'element-packages/dropdown'
import DropdownMenu from 'element-packages/dropdown-menu'
import DropdownItem from 'element-packages/dropdown-item'
import Input from 'element-packages/input'
import InputNumber from 'element-packages/input-number'
import Radio from 'element-packages/radio'
import RadioGroup from 'element-packages/radio-group'
import Checkbox from 'element-packages/checkbox'
import CheckboxGroup from 'element-packages/checkbox-group'
import Switch from 'element-packages/switch'
import Select from 'element-packages/select'
import Option from 'element-packages/option'
import Button from 'element-packages/button'
import ButtonGroup from 'element-packages/button-group'
import Table from 'element-packages/table'
import TableColumn from 'element-packages/table-column'
import DatePicker from 'element-packages/date-picker'
import Popover from 'element-packages/popover'
import Tooltip from 'element-packages/tooltip'
import Breadcrumb from 'element-packages/breadcrumb'
import BreadcrumbItem from 'element-packages/breadcrumb-item'
import Form from 'element-packages/form'
import FormItem from 'element-packages/form-item'
import Tabs from 'element-packages/tabs'
import TabPane from 'element-packages/tab-pane'
import Alert from 'element-packages/alert'
import Row from 'element-packages/row'
import Col from 'element-packages/col'
import Upload from 'element-packages/upload'
import Loading from 'element-packages/loading';

import Progress from 'element-packages/progress'
import Badge from 'element-packages/badge'
import Collapse from 'element-packages/collapse'
import CollapseItem from 'element-packages/collapse-item'
import Container from 'element-packages/container'
import Header from 'element-packages/header'
import Aside from 'element-packages/aside'
import Main from 'element-packages/main'
import MxUserAvatar from '@views/common/components/userAvatar/MxUserAvatar'
import App from './src/App'
import store from '@appStore'
import {EventBusPlugin} from '@views/common/plugins/eventBus'
import {MxConfirmPlugin, MxMessagePlugin, MxOptionConfirmPlugin, MxAlertPlugin} from '@views/common/plugins/popup'
import ClickPlugin from '@views/common/plugins/clickEvent'
import TimerPlugin from '@views/common/plugins/timer'

import FlexGrid from '@views/common/components/grid'
import '@views/theme/src/images/menus'
import VueLazyload from 'vue-lazyload'
import { createPinia, PiniaVuePlugin } from 'pinia'

Vue.use(PiniaVuePlugin)
Vue.use(Loading)
const pinia = createPinia()
Vue.use(VueLazyload)
import {createApplication} from '@views/common/useComponent'

import FormHeading from '@views/contentLibrary/plugins/form/common//FormHeading'
import FormSingleLineText from '@views/contentLibrary/plugins/form/common//FormSingleLineText'
import FormMultSelection from '@views/contentLibrary/plugins/form/common//FormMultSelection'
import FormSingleSelection from '@views/contentLibrary/plugins/form/common//FormSingleSelection'
import FormMultiLineText from '@views/contentLibrary/plugins/form/common//FormMultiLineText'
import FormUserName from '@views/contentLibrary/plugins/form/common//FormUserName'
import FormEmailAddress from '@views/contentLibrary/plugins/form/common//FormEmailAddress'
import FormNumber from '@views/contentLibrary/plugins/form/common//FormNumber'
import FormDate from '@views/contentLibrary/plugins/form/common//FormDate'
import FormDropdownList from '@views/contentLibrary/plugins/form/common//FormDropdownList'
import FormLineSeperator from '@views/contentLibrary/plugins/form/common//FormLineSeperator'
import FormAddress from '@views/contentLibrary/plugins/form/common//FormAddress'
import FormPhoneNumber from '@views/contentLibrary/plugins/form/common//FormPhoneNumber'
import FormCurrency from '@views/contentLibrary/plugins/form/common/FormCurrency'
import FormImage from '@views/contentLibrary/plugins/form/common/FormImage.vue'
import FormSignature from '@views/contentLibrary/plugins/form/common/FormSignature.vue'
import {setupStore} from "./src/piniaStore";
import {setupBranding} from "./src/common/branding";
import {setupLangLocally} from "../../../views/i18n/mepIndex";
import {checkVersion} from "../common/group";

// To adapt older versions of android webview
if (!Array.prototype.at) {
  Array.prototype.at = function (n) {
    if (typeof n !== 'number') {
      throw new TypeError('The argument must be a number')
    }
    n = Math.trunc(n) || 0
    if (n < 0) { 
      n += this.length
    }
    if (n < 0 || n >= this.length) {
      return undefined
    } else {
      return this[n]
    }
  }
}

const components = [
  Alert,
  Autocomplete,
  Collapse,
  CollapseItem,
  FormItem,
  Form,
  Header,
  ButtonGroup,
  Input,
  Button,
  Checkbox,
  Container,
  Aside,
  VmProgress,
  Main,
  Radio,
  Table,
  TableColumn,
  RadioGroup,
  Col,
  Row,
  Dialog,
  DatePicker,
  Upload,
  Select,
  Option,
  Switch,
  Popover,
  Progress,
  Tooltip,
  Badge,
  Tabs,
  TabPane,
  Dropdown,
  DropdownMenu,
  DropdownItem,
  CheckboxGroup,
  InputNumber,
  Breadcrumb,
  BreadcrumbItem,
  MxUserAvatar,
  FormHeading,
  FormSingleLineText,
  FormMultSelection,
  FormSingleSelection,
  FormMultiLineText,
  FormUserName,
  FormEmailAddress,
  FormNumber,
  FormDate,
  FormDropdownList,
  FormLineSeperator,
  FormAddress,
  FormPhoneNumber,
  FormCurrency,
  FormSignature,
  FormImage

]

const customComponents = [
  BaseModal,
  MxFloatingInput
]

const requireComponent = require.context(
  '@views/common/cards',
  true,
  /\w+\.vue$/
)
requireComponent.keys().forEach(fileName => {
  const componentConfig = requireComponent(fileName)
  const componentName = fileName.replace(/^.+?(\w+)\.vue$/, '$1')
  Vue.component(componentName, componentConfig.default || componentConfig)
})


components.map(component => {
  Vue.component(component.name, component)
})

customComponents.map(component => {
  Vue.component(component.name, component)
})

const plugins = [
  FlexGrid,
  EventBusPlugin,
  MxConfirmPlugin,
  MxOptionConfirmPlugin,
  MxMessagePlugin,
  MxAlertPlugin,
  directives,
  ClickPlugin,
  TimerPlugin
]
plugins.map(plugin => {
  Vue.use(plugin)
})

Vue.prototype.$renderVueComponent = (Component, data) => {
  let container = document.createElement('div')
  document.body.appendChild(container)
  return new Vue({
    i18n,
    el: container,
    render: h => {
      return h(Component, data)
    }
  })
}

// below is used to fix drag and drop will open a new tab issue in Firefox
if (BrowserUtils.isFirefox) {
  document.body.ondrop = function (event) {
    event.preventDefault()
    event.stopPropagation()
  }
}

const browserName = (BrowserUtils.browser.name || '').toLowerCase()
document.body.className += ' bw-' + browserName
document.body.className += ` bw-${browserName}-${BrowserUtils.browserVersion}`

if (BrowserUtils.isMobile || BrowserUtils.isAndroid || BrowserUtils.isIOS) {
  document.body.className += ' bw-mobile'
}
async function setupApp() {
  await checkVersion().then(async (res) =>{
     setupBranding(res)
    await setupLangLocally()

    // const app = createApp(App, {i18n});
    const pinia =  setupStore()
    const app = createApplication({
      container: '#app',
      i18n,
      store,
      pinia,
      App
    })
    window.mxBodyContainer = document.body
    window.vm = app
  }).catch(info =>{
    console.debug('jump to correct version',info.correctURL)
    window.location.href = info.correctURL
  })
}
setupApp()
