import { MxLogger } from '@commonUtils/MxLogger'

const WEB_VUE_LOGGER = MxLogger.create('Vue (web jwtRedirect)')
import Vue from 'vue'

Vue.config.errorHandler = function (err, vm, info) {
  // handle error
  // `info` is a Vue-specific error info, e.g. which lifecycle hook
  // the error was found in. Only available in 2.2.0+
  console.log(err.stack, info)
  WEB_VUE_LOGGER.error(err.stack, info)
}

Vue.config.warnHandler = (msg, vm, trace) => {
  // `trace` is the component hierarchy trace
  // This only works during development and is ignored in production.
  WEB_VUE_LOGGER.warn(msg, trace);
}

import i18n from '@views/i18n/mepIndex'
import App from './src/App'
import store from '@appStore'
import VueLazyload from 'vue-lazyload'

Vue.use(VueLazyload)
import {createApplication} from '@views/common/useComponent';

const vm = createApplication({
	container: '#app',
	i18n,
	store,
	App
})

window.mxBodyContainer = document.body

window.vm = vm
