import 'core-js';
import Vue from 'vue';
import VueI18n from 'vue-i18n';
import App from './src/App';
import './src/main.scss';
import Vuex from 'vuex';
import messages from './message';
import Input from 'element-packages/input';
import Select from 'element-packages/select';
import Option from 'element-packages/option';
import Form from 'element-packages/form';
import FormItem from 'element-packages/form-item';
import Row from 'element-packages/row';
import Col from 'element-packages/col';
import Button from 'element-packages/button';
import Alert from 'element-packages/alert';
import MxFloatingInput from '../../../views/common/components/input/MxFloatingInput';
import ta from '../../../views/common/directives/ta/ta'
import {BrowserUtils} from '../../../commonUtils/browser';
import FlexRow from '../../../views/common/components/grid/FlexRow';


Vue.use(VueI18n);
Vue.use(Vuex);
Vue.component(Input.name, Input)
Vue.component(Select.name, Select)
Vue.component(Form.name, Form)
Vue.component(FormItem.name, FormItem)
Vue.component(Button.name, Button)
Vue.component(Option.name, Option)
Vue.component(Row.name, Row)
Vue.component(Col.name, Col)
Vue.component(Alert.name, Alert)
Vue.component('MxFloatingInput', MxFloatingInput)
Vue.component(FlexRow.name, FlexRow)
Vue.directive('mx-ta', ta)

const store = new Vuex.Store({
  modules: {
    group: {
      namespaced: true,
      getters: {
        isPhoneNumberEnabled(state) {
          return true
        },
        isPhoneNumberPrimary(state) {
          return false
        },
        brandingLogoLeft () {
          return ''
        },
        brandingLogoCenter () {
          return ''
        },
        groupBasicInfo () {
          return {
            name: 'Moxo'
          }
        },
        groupTags () {
          return {}
        },
        groupCaps () {
          return {}
        },
        isOnlineBillingOrg () {
          return false
        },
        getUserBasicInfoByQRToken () {
          return ''
        }
      },
    },
    portal: {
      namespaced: true,
      state: {
        domains: []
      },
      mutations: {
        setDomains (state, domains) {
          state.domains = domains
        }
      }
    }
  },
});

const i18n = new VueI18n({
  locale: getLocale(),
  fallbackLocale: 'en',
  messages,
});

function getLocale() {
  let locale;
  let key = 'mx:CurrentLanguage';
  locale = localStorage.getItem(key);
  // if there isn't localStorage value, get from navigater
  if (!locale) {
    locale = (navigator.languages && navigator.languages.length
      ? navigator.languages[0]
      : navigator.language || navigator.userLanguage || 'en-US'
    ).toLowerCase();
  }
  // process special case, as our server use 'en','es','fr','ja','pt','zh','zh-tw',
  // we should convert such as 'en-US' to 'en', 'fr-CA' to 'fr' etc.
  if (locale) {
    let items = locale.split('-');
    if (items[0] === 'zh') {
      // when only 'zh' or 'zh-CN', set locale to 'zh'
      // other cases, set locale to 'zh-tw'
      if (!items[1] || /cn/gi.test(items[1])) {
        locale = 'zh';
      } else {
        locale = 'zh-tw';
      }
    } else {
      locale = items[0];
    }
  }
  return locale;
}

const browserName = (BrowserUtils.browser.name || '').toLowerCase();
document.body.className += ' bw-' + browserName
document.body.className += ` bw-${browserName}-${BrowserUtils.browserVersion}`

if (BrowserUtils.isMobile) {
  document.body.className += ' bw-mobile'
}

let vm = new Vue({
  el: '#app',
  i18n,
  store,
  components: { App },
  render () {
    return (
      <App/>
    )
  }
});

window.mxBodyContainer = document.body
