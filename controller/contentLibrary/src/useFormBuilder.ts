import {
  cFormModelToFormModel,
  createFormTemplate,
  createSingleInstance,
  updateFormCard,
  updateFormTemplate
} from '@controller/contentLibrary'
import {computed, reactive, ref} from '@vue/composition-api'
import {uploadImageToTransaction} from '@controller/contentLibrary/src/form';
import get from 'lodash/get'
import {MxISDK, Defines} from 'isdk';
import {ThreadUtils} from "@controller/utils/thread";
import {CFormModel} from "@controller/defines/CFormModel";
import {upgradeDataToSupportDefaultValue} from "@controller/contentLibrary/src/defaultValueSupport";
import {BrowserUtils, FunctionUtil, MxConsts, StringUtils} from '@commonUtils/index';
import { useI18n } from 'vue-i18n-composable'
import { conditionViewModelToServerData, elementConditionToConditionViewModel } from '@model/form/transform/conditions'
import { getCacheOrInstantBoard } from '@newController/utils';
import {FormPreprocessor} from '@model/form/factory/FormPreprocessor';
import {FormElementType} from '@model/form/defines/shared';
import {FormCurrentVersion} from '@model/form/data/common';
import {formCardToFormModel, toValidUniqueName} from '@views/form/common/uniqueName';
import { nameMaxLength } from '@views/common/appConst.js'

export const FormBuilderSymbol = Symbol('useFormBuilderKey')
export const FormBuilderCtlKey = Symbol('FormBuilderCtl')
export const FormBuilderOption = Symbol('formBuilderOption')
export function formBuilderFactory () {
  const formInfo = reactive({
    id: '',
    folderId: '',
    title: '',
    sub_title: '',
    template_name: '',
    template_description: '',
    pages: [],
    currentCard: {},
    dueDateTimestamp: 0,
    dueInTimeframe: MxConsts.DueTimeFrameType.DUE_TIME_FRAME_DAYS,
    excludeWeekends: false,
    transactionSequence: 0,
    creator: '',
    noSteps: false,
    hasCustomFolder: false,
    customFolderName: '',
    version: '',
    enableDDR: false
  })
  const preprocessor = new FormPreprocessor()
  const { t } = useI18n()
  let createdTempBoard = null
  const validationError = ref({})
  const customError = ref({})
  const imageUploadPanelError = ref('')
  const currentCard: any = computed(() => {
    return formInfo.currentCard || formInfo.pages[0]
  })
  const currForm = ref({})
  const currentCardSchema = computed(() => {
    return currentCard.value.$props
  })
  const currentCardSpecSchema = computed(() => {
    return currentCard.value.$specProps
  })
  const currentField = computed(() => {
    var v = currentCard.value.$values
    if (v && !v.fieldSpecific.condition) {
      v.fieldSpecific.condition = {
        "enable": false,
        "visible": true,
        "rule": true,
        "rules": []
      }
    }
    return v
  })
  const currentCardUi = computed(() => {
    return currentCard.value.$ui
  })

  const conditionalLogicList = computed(() => {
    const conditionsList = []
    formInfo.pages.forEach((page, index) => {
      const pageCondition = page.$values.fieldSpecific.condition
      if (pageCondition.enable) {
        const transformedPageCondition = elementConditionToConditionViewModel(pageCondition, page.$values.id, allElementIdMap.value)
        conditionsList.push(transformedPageCondition)
      }
      page.$values.fields.forEach((field) => {
        const condition = field.$values.fieldSpecific.condition
        if (condition.enable) {
          const transformedFieldCondition = elementConditionToConditionViewModel(condition, field.$values.id, allElementIdMap.value)
          conditionsList.push(transformedFieldCondition)
        }
      })
    })
    return conditionsList
  })
  const allElementIdMap = computed(() => {
    let map = {}
    formInfo.pages.forEach((page, index) => {
      const pageText = t('page_no', {number: index + 1})
      map[page.$values.id] = {
        label: pageText,
        type: page.$values.type
      }
      page.$values.fields.forEach((field) => {
        map[field.$values.id] = {
          label: field.$values.label || field.$values.type,
          type: field.$values.type,
        }
      })
    })
    return map
  })
  const showConditionsPanel = ref(false)
  const toggleConditionsPanel = (show: boolean, fieldId?: string) => {
    if (show) {
      setCurrentCard(null)
    }
    showConditionsPanel.value = show
  }
  const updateConditionToFormPage = (elementId, condition) => {
    formInfo.pages.some(page => {
      if (page.$values.id === elementId) {
        page.$values.fieldSpecific.condition = condition
        return true
      } else {
        return page.$values.fields.some(field => {
          if (field.$values.id === elementId) {
            field.$values.fieldSpecific.condition = condition
            return true
          } else {
            return false
          }
        })
      }
    })
  }
  const removeConditionFromLogicList = (condition) => {
    const defaultConditionModel = {
      enable: false,
      visible: true,
      rule: true,
      rules: []
    }
    updateConditionToFormPage(condition.elementId, defaultConditionModel)
  }
  const saveCondition = (condition) => {
    const conditionModel = conditionViewModelToServerData(condition)
    updateConditionToFormPage(condition.elementId, conditionModel)
  }
  const addPage = (page) => {
    formInfo.pages.push(page)
  }

  const removeField = (field) => {
    formInfo.pages.forEach(page => {
      // change page referenceField
      let pageRules = page.$values.fieldSpecific?.condition?.rules || []
      if (pageRules.length) {
        pageRules.forEach(v => {
          if(v.referenceField === field.$values.id) {
            v.referenceField = ''
          }
        })
      }
      // change field referenceField
      page.$values.fields.forEach((f, i) => {
        let rules = f.$values.fieldSpecific?.condition?.rules || []
        if (rules.length) {
          rules.forEach(v => {
            if (v.referenceField === field.$values.id) {
              v.referenceField = ''
            }
          })
        }
      })
      // remove field
      const index = page.$values.fields.findIndex(p => p.$values.id === field.$values.id)
      if (index >= 0) {
        page.$values.fields.splice(index, 1)
        delete customError.value[field.$values.id]
        delete validationError.value[field.$values.id]
        customError.value = {...customError.value}
        validationError.value = {...validationError.value}
        if (field.$values.id === (currentCard?.value?.$values?.id)) {
          setCurrentCard(null)
        }
      }
    })
  }
  const deletePage = (page) => {
    const index = formInfo.pages.findIndex(p => p.$values.id === page.$values.id)
    const currPage = formInfo.pages[index]
    let closeSetting = false

    for (let i = currPage.$values.fields.length - 1; i >= 0; i--) {
      removeField(currPage.$values.fields[i])
    }

    if (currPage.$values.fields.findIndex(p => p.$values.id === currentCard?.value?.$values?.id) >= 0) {
      closeSetting = true
    } else if (currPage.$values.id === currentCard?.value?.$values?.id) {
      closeSetting = true
    }
    formInfo.pages.splice(index, 1)
    if (closeSetting) {

      setCurrentCard(null)
    }
  }
  const copyField = (card, id) => {
    formInfo.pages.some(page => {
      const index = page.$values.fields.findIndex(p => p.$values.id === id)
      if (index >= 0) {
        page.$values.fields.splice(index + 1, 0, card)
        if (validationError.value[id]) {
          validationError.value[card.$values.id] = validationError.value[id]
        }
        if (customError.value[id]) {
          customError.value[card.$values.id] = customError.value[id]
        }
        card.$values.uniqueName = toValidUniqueName(card.$values.uniqueName, card.$values.id, formCardToFormModel(formInfo))
        setCurrentCard(card)
        return true
      }
      return false
    })
  }
  const duplicatePage = (page, id) => {
    const index = formInfo.pages.findIndex(p => p.$values.id === id)
    if (index >= 0) {
      formInfo.pages.splice(index + 1, 0, page)
      const originalPage = formInfo.pages[index]
      const oFields = originalPage.$values.fields, nFields = formInfo.pages[index + 1].$values.fields
      for (let i = 0; i < oFields.length; i++) {
        if (validationError.value[oFields[i].$values.id]) {
          validationError.value[nFields[i].$values.id] = validationError.value[oFields[i].$values.id]
        }
        if (customError.value[oFields[i].$values.id]) {
          customError.value[nFields[i].$values.id] = customError.value[oFields[i].$values.id]
        }
      }

    }
  }
  const moveField = (arr, fromIndex, toIndex, nextArr) => {
    const element = arr[fromIndex]
    arr.splice(fromIndex, 1)
    if (nextArr) {
      if (toIndex === -1) {
        nextArr.push(element)
      } else {
        nextArr.unshift(element)
      }
    } else {
      arr.splice(toIndex, 0, element)
    }
  }
  const findIndexArr = (card) => {
    return formInfo.pages.reduce((acc, page, pageIndex) => {
      if (card.id === page.$values.id) {
        acc['index'] = pageIndex
        acc['arr'] = formInfo.pages
        acc['pageIndex'] = pageIndex
      } else {
        const fieldIndex = page.$values.fields.findIndex(field => field.$values.id === card.id)
        if (fieldIndex >= 0) {
          acc['index'] = fieldIndex
          acc['arr'] = page.$values.fields
          acc['pageIndex'] = pageIndex
        }
      }
      return acc
    }, {})
  }
  const moveFieldUp = (card) => {
    const {index, arr, pageIndex} = findIndexArr(card)
    let nextArr = null
    if (index === 0) {
      nextArr = formInfo.pages[pageIndex - 1].$values.fields
    }
    moveField(arr, index, index - 1, nextArr)
  }
  const moveFieldDown = (card) => {
    const {index, arr, pageIndex} = findIndexArr(card)
    let nextArr = null
    if (arr.length === index + 1) {
      nextArr = formInfo.pages[pageIndex + 1].$values.fields
    }
    moveField(arr, index, index + 1, nextArr)
  }
  const mergePageAbove = (page, isDisabled) => {
    if (!isDisabled) {
      const {index, arr} = findIndexArr(page)
      const element = arr[index]
      formInfo.pages[index - 1].$values.fields.push(...element.$values.fields)
      setCurrentCard(formInfo.pages[index - 1])
      arr.splice(index, 1)
    }
  }
  const mergePageDown = (page, isDisabled) => {
    if (!isDisabled) {
      const {index, arr} = findIndexArr(page)
      const element = arr[index]
      formInfo.pages[index + 1].$values.fields.unshift(...element.$values.fields)
      setCurrentCard(formInfo.pages[index + 1])
      arr.splice(index, 1)
    }
  }
  const uploadResource = (file, uploadCallback?: Function) => {
    let boardId = formInfo.folderId
    let sequence = formInfo.transactionSequence
    if(createdTempBoard) {
      boardId = createdTempBoard.boardId
      sequence = createdTempBoard.transactionSequence
    }
    return uploadImageToTransaction(boardId, sequence, file, '', uploadCallback)
  }
  const saveImageRecources = async () => {
    try {
      const isDataURL = (value) =>{
        const regex = /^data:.+;base64,.+/;
        return regex.test(value);
      } 
      const haveCachedImage = (field) => {
        return field?.$values?.image && !field?.$values?.fieldSpecific?.imageUUID && isDataURL(field?.$values?.image) ? field?.$values?.image : '';
      };
      const uploadPromises = [];
      formInfo.pages?.forEach(page => {
        if (page?.$values?.fields) {
          page.$values.fields.forEach(field => {
            const cachedImage = haveCachedImage(field);
            if (cachedImage) {
              try {
                const blob = BrowserUtils.base64ToBlob(cachedImage);
                if (!blob || blob.size === 0) {
                  throw new Error('Invalid blob: Blob is empty.');
                }
                const uploadPromise = uploadResource(blob)
                  .then(({ url, client_uuid }) => {
                    field.$values.image = url;
                    field.$values.fieldSpecific.imageUUID = client_uuid;
                  })
                  .catch(error => {
                    throw new Error('Image upload failed.');
                  });
                uploadPromises.push(uploadPromise);
              } catch (error) {
                throw error;
              }
            }
          });
        }
      });
      await Promise.all(uploadPromises);
    } catch (error) {
      throw new Error('Failed to save image resources.');
    }
    return
  };

  const saveFormCard = () =>{
    const formCard: CFormModel = {
      transactionSequence: formInfo.transactionSequence,
      boardId: formInfo.folderId,
      pages: formInfo.pages.map(page => {
        page.$values.fields.forEach(field => {
          const model =field.$values
          if([FormElementType.UserName,FormElementType.EmailAddress].includes(model.type)) {
            if(model.fieldSpecific.enableDefaultValue && !model.fieldSpecific.defaultFromProfile){
              if(model.type == FormElementType.UserName){
                const {firstName, lastName} = model.defaultValue
                if(!firstName && !lastName){
                  model.fieldSpecific.enableDefaultValue = false
                }
              }else if(!model.defaultValue){
                model.fieldSpecific.enableDefaultValue = false
              }
            }
          }
        })
        return page.toJSON()
      }),
      creator:formInfo.creator,
      title: formInfo.title,
      description: formInfo.sub_title,
      templateName: formInfo.template_name,
      templateDescription: formInfo.template_description,
      version: formInfo.version || FormCurrentVersion
    }
    return updateFormCard(formCard)
  }
  const saveTemplate = (basicInfo, formData) => {
    let req
    if(createdTempBoard) {
      const folderId = basicInfo.folderId
      basicInfo.copyToFolderId =  folderId
      formInfo.transactionSequence = createdTempBoard.transactionSequence
      basicInfo.id = createdTempBoard.transactionSequence
      basicInfo.folderId = createdTempBoard.boardId
      basicInfo.signer = MxISDK.getCurrentUser().id
    }
    const formResult = {
      type: FormElementType.Form,
      isEmptyForm: false,
      version: formInfo.version || FormCurrentVersion,
      data : formData
    }
    if (formInfo.transactionSequence && formInfo.folderId) {
      let opts
      if(formInfo.noSteps){
        opts = {
          appendSteps: true
        }
      }

      req = updateFormTemplate(basicInfo, formResult, opts)
    } else {
      req = createFormTemplate(basicInfo, formResult)
    }
    return req.then((board)=>{
      return ThreadUtils.computeTransactionBaseObject(board.transactions[0], board, MxISDK.getCurrentUser().basicInfo)
    });
  }

  /**
   * create a temp board for create template case
   * @param name
   * @param subTitle
   */
  const prepareTempBoard = (name?: string, subTitle?: string) =>{
    return new Promise(async (resolve, reject) =>{
      createFormTemplate({
        title: name || '',
        template_name: '',
        template_description: '',
        folderId: '',
        expireDate: null,
        sub_title: subTitle || '',
        signer: MxISDK.getCurrentUser().id
      },null, true, {istemp: true}).then(board =>{
        const transactionSequence = get(board, 'transactions[0].sequence')
        formInfo.folderId = board.id
        formInfo.noSteps = false
        formInfo.transactionSequence = transactionSequence
        if(name){
          formInfo.title = name
        }
        if(subTitle){
          formInfo.sub_title = subTitle
        }
        createdTempBoard = {
          boardId: board.id,
          transactionSequence
        }
        currForm.value = createdTempBoard
        resolve(board)
      }).catch(reject)
    })
  }
  /**
   * crate template board and fill the transaction as a template
   * @param transaction
   * @param createFormCard
   */
  const preFillTransaction = (transaction, createFormCard) =>{
    return prepareTempBoard(transaction.title, transaction.subTitle).then(board =>{
      if (transaction?.raw) {
        formInfo.pages.splice(0)
        const form = JSON.parse(transaction.raw)
        preprocessor.beforeFormToViewModel(form,{isCompleted: transaction.status === Defines.TransactionStatus.TRANSACTION_STATUS_COMPLETED})
        form.data.pages.forEach(page => {
          const formPage = createFormCard(page.type, page)
          addPage(formPage)
        })
        formInfo.version = form.version
      }

      formInfo.hasCustomFolder = transaction.hasCustomFolder
      formInfo.customFolderName = transaction.customFolderName

    }).then(()=>{
      return currForm.value
    })
  }
  /**
   * load transaction for edit form template
   * @param transaction
   * @param createFormCard
   */
  const loadTransaction = (transaction, createFormCard,{userTimezone,isUseDefaultVaule,isCompleted}) =>{
    return new Promise(async (resolve, reject) => {

      let _board: Defines.Board = null
      if(transaction.workflow) {
        const mxBoard = getCacheOrInstantBoard(transaction.folderId)
        const workflow = await mxBoard.readWorkflow(transaction.workflow)
        console.log('workflow', workflow)
        const _workflow = await mxBoard.readWorkflow(transaction.workflow)

        // for transform to workflowViewModel (this transform will result in a circular dependency)
        if(_workflow) {
          _board = {
            id: mxBoard.id,
            workflows: _workflow ? [_workflow] : [],
            users: []
          }
        }
      }

      const {title, subTitle, expireDate, dueInTimeframe, excludeWeekends, template_name, template_description, id, folderId,noSteps, hasCustomFolder,customFolderName} = transaction
      formInfo.title = title
      formInfo.sub_title = subTitle
      formInfo.template_name = template_name
      formInfo.template_description = template_description
      formInfo.dueDateTimestamp = expireDate || 0
      formInfo.dueInTimeframe = dueInTimeframe
      formInfo.excludeWeekends = excludeWeekends
      formInfo.transactionSequence = id
      formInfo.folderId = folderId
      formInfo.noSteps = noSteps
      formInfo.hasCustomFolder = hasCustomFolder
      formInfo.customFolderName = customFolderName

      currForm.value = {
        boardId: folderId,
        transactionSequence: id
      }

      if (transaction?.raw) {
        formInfo.pages.splice(0)
        const form = JSON.parse(transaction.raw)
         upgradeDataToSupportDefaultValue(form.data.pages)
        preprocessor.beforeFormToViewModel(form, {isCompleted:false})
        formInfo.version = form.version
        form.data.pages.forEach(page => {
          const formPage = createFormCard(page.type, page)
          addPage(formPage)
        })
      }
      resolve({currForm: currForm.value, board:_board})
    })
  }
  const loadTemplate =  (template: CFormModel, createFormCard, {isUseDefaultVaule,userTimezone,isCompleted}, enableDDR?: boolean) => {
    return new Promise(async (resolve, reject) => {
    const {title, description, boardId,dueDate, dueInTimeframe, excludeWeekends, templateName, templateDescription,transactionSequence} = template
    formInfo.title = title
    formInfo.noSteps= false
    formInfo.enableDDR = enableDDR || false
    if(!boardId) {
      // create template we need create a temp board and transaction for upload image.
      const signer = MxISDK.getCurrentUser().id
      const board = await createFormTemplate({title: title, template_name: templateName || '', template_description: '', folderId: '', expireDate: null, sub_title: '',signer}, null, true, { istemp:true})
      if(board) {
        const transactionObj = get(board,'transactions[0]')
        formInfo.folderId = board.id
        formInfo.transactionSequence = transactionObj.sequence
        createdTempBoard =  {
          boardId: board.id,
          transactionSequence:transactionObj.sequence
        }
        currForm.value = createdTempBoard
        //add a empty page for create case
        const formPage = createFormCard('Page')
        addPage(formPage)
      }
      resolve(currForm.value)
      return;
    }else{
      formInfo.folderId = boardId
      currForm.value = {
        boardId: boardId,
        transactionSequence: transactionSequence
      }
    }
    formInfo.pages.splice(0)

    formInfo.sub_title = description
    formInfo.template_name = templateName
    formInfo.template_description = templateDescription
    formInfo.dueDateTimestamp = dueDate || 0
    formInfo.dueInTimeframe = dueInTimeframe
    formInfo.excludeWeekends = excludeWeekends
    formInfo.transactionSequence = transactionSequence

      upgradeDataToSupportDefaultValue(template.pages)
      const form = cFormModelToFormModel(template)
     preprocessor.beforeFormToViewModel(form,{isCompleted:false})
      formInfo.version = form.version


    template.pages.forEach(page => {
      const formPage = createFormCard(page.type, page, enableDDR)
      addPage(formPage)
    })
      resolve(currForm.value)
    })
  }

  const clearTempDate = () =>{
    if(createdTempBoard) {
      MxISDK.getCurrentUser().deleteBoard(createdTempBoard.boardId)
    }
  }

  const compareRule = (isAll, type, r1, r2) => {
    var v1 = r1.expectedValue
    var v2 = r2.expectedValue
    var o1 = r1.operator
    var o2 = r2.operator
    if (!v1 || !v2 || !o1 || !o2) {
      return false
    }
    if (v1 === v2 && o1 === o2) {
      return true
    }
    if (isAll) {
      if (type === 'SingleSelection' || type === 'DropdownList') {
        // total 3 cases
        if (v1 === v2 && o1 !== o2 || v1 !== v2 && o1 === '=' && o2 === '=') {
          return true
        }
      } else if (type === 'MultiSelection') {
        if (v1 === v2 && o1 !== o2) {
          return true
        }
      } else if (type === 'Number' || type === 'Currency' || type === 'Date') {
        // total 16 cases
        if (v1 == v2) {
          if (o1 !== '=' && o2 === '=' ||
              o1 === '=' && o2 !== '=' ||
              o1 === '<' && o2 === '>' ||
              o1 === '>' && o2 === '<'
          ) {
            return true
          }
        }
        if (v1 > v2) {
          if ((o1 === '=' || o1 === '>') && (o2 === '=' || o2 === '<')) {
            return true
          }
        }
        if (v1 < v2) {
          if ((o1 === '=' || o1 === '<') && (o2 === '=' || o2 === '>')) {
            return true
          }
        }
      }
    } else {
      // total 2 cases
      if (v1 === v2 && (o1 === '=' && o2 === '<>' || o2 === '=' && o1 === '<>')) {
        return true
      }
    }
    return false
  }
  const pushError = (fieldId, error) => {
    validationError.value[fieldId] = error
    validationError.value = {
      ...validationError.value
    }
  }
  const setError = (errors) => {
    validationError.value = errors
    console.debug('set error', errors)
  }

  const validateFieldDefaultValue = (field) => {
    let isValid = true
    if (field && field.$values) {
      const {id, type, fieldSpecific, defaultValue} = field.$values
      if (type === FormElementType.EmailAddress) {
        if (fieldSpecific.enableDefaultValue && defaultValue) {
          if (!FunctionUtil.isEmail(defaultValue)) {
            if (formInfo.enableDDR) {
              if(!StringUtils.DDRRegex.test(defaultValue)) {
                // Ignore validation process if the default value is DDR source
                pushError(id, 'invalid_email_address')
                isValid = false
              }
            } else {
              pushError(id, 'invalid_email_address')
              isValid = false
            }
          }
        }
      } else if  (type === FormElementType.UserName) {
        if (fieldSpecific.enableDefaultValue && defaultValue) {
          const {firstName, lastName} = field.$values.defaultValue || {}
          const firstNameLength = formInfo.enableDDR ? StringUtils.escapedDDRLength(firstName) : firstName?.length
          const lastNameLength = formInfo.enableDDR ? StringUtils.escapedDDRLength(lastName) : lastName?.length
          // Check if UserName sub-fields exceed their max-length limit
          if (firstNameLength > nameMaxLength || lastNameLength > nameMaxLength) {
            pushError(id, 'Invalid') // Note: the error message here is just for indicating the field has error
            isValid = false
          }
        }
      } else if (type === FormElementType.Address) {
        const {addressLineOne, addressLineTwo, city, state, zipcode} = field.$values.defaultValue || {}
        if (addressLineOne?.length || addressLineTwo?.length || city?.length || state?.length || zipcode?.length) {
          const addressLineOneLength = formInfo.enableDDR ? StringUtils.escapedDDRLength(addressLineOne) : addressLineOne?.length
          const addressLineTwoLength = formInfo.enableDDR ? StringUtils.escapedDDRLength(addressLineTwo) : addressLineTwo?.length
          const cityLength = formInfo.enableDDR ? StringUtils.escapedDDRLength(city) : city?.length
          const stateLength = formInfo.enableDDR ? StringUtils.escapedDDRLength(state) : state?.length
          const zipcodeLength = formInfo.enableDDR ? StringUtils.escapedDDRLength(zipcode) : zipcode?.length
          // Check if Address sub-fields exceed their max-length limit
          if (addressLineOneLength > 300 || addressLineTwoLength > 300 || cityLength > 200 || stateLength > 200 || zipcodeLength > 10) {
            pushError(id, 'Invalid') // Note: the error message here is just for indicating the field has error
            isValid =false
          }
        }
      }
    }
    return isValid
  }

  const validateSettings = ()=>{
    const field = formInfo.currentCard
    const isValid = validateFieldDefaultValue(field)
    return isValid
  }

  const validateFieldsDefaultValue = () => {
    let isValid = true
    formInfo.pages.forEach((page) => {
      page.$values.fields.forEach((field) => {
        if (!validateFieldDefaultValue(field)) {
          isValid = false
        }
      })
    })
    return isValid
  }

  const setCurrentCard = (field) => {
    validateSettings()

    if (field && field.$values.type !== 'PageBreak') {
      formInfo.currentCard = field
      toggleConditionsPanel(false)
    } else {
      formInfo.currentCard = {}
    }
  }
  const clearError = (fieldId) => {
    let errs = {}
    if(fieldId){
       errs = {
        ...validationError.value
      }
      delete errs[fieldId]
    }
    validationError.value = {
      ...errs
    }
  }
  // Store error emitted from image upload panel within the form canvas
  const clearImageUploadError = () => {
    imageUploadPanelError.value = ''
  }

  const setImageUploadError = (err) => {
    imageUploadPanelError.value = err
  }

  return {
    currForm,
    preFillTransaction,
    validateSettings,
    prepareTempBoard,
    saveFormCard,
    clearTempDate,
    loadTemplate,
    loadTransaction,
    uploadResource,
    saveTemplate,
    addPage,
    duplicatePage,
    deletePage,
    removeField,
    copyField,
    moveFieldUp,
    moveFieldDown,
    mergePageAbove,
    mergePageDown,
    setCurrentCard,
    formInfo,
    currentCard,
    currentField,
    currentCardSchema,
    currentCardSpecSchema,
    currentCardUi,
    validationError,
    customError,
    compareRule,
    showConditionsPanel,
    toggleConditionsPanel,
    conditionalLogicList,
    allElementIdMap,
    saveCondition,
    removeConditionFromLogicList,
    clearError,
    pushError,
    setError,
    setImageUploadError,
    clearImageUploadError,
    imageUploadPanelError,
    saveImageRecources,
    validateFieldsDefaultValue
  }
}

export const useFormBuilder = createSingleInstance(FormBuilderSymbol, formBuilderFactory)
