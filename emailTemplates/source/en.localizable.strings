"One_binder_member" = "1 Member";
"Email_has_been_changed_to" = "An administrator from [[#HAS_OrgConfig_BrandName]][[OrgConfig_BrandName]][[/HAS_OrgConfig_BrandName]][[#NO_OrgConfig_BrandName]][[GROUP_NAME]][[/NO_OrgConfig_BrandName]] has changed the email address of your account to <span style="font-style:italic; color:#606369;">[[NEW_EMAIL]]</span>.";
"Use_the_same_email_to_receive_invitation" = "Be sure to use the same email address where you received your invitation.";
"Click_below_to_finish_signup" = "Click below to finish signing up.";
"Click_below_to_get_started" = "Click below to get started.";
"Click_to_try_again_for_invitation" = "Click the link from your original invitation to confirm your email address, or go back to the app and enter the email address to which the invitation was sent.";
"Complete_setup" = "Complete Setup";
"Confirm_email" = "Confirm Email";
"Confirm_your_email_access_on_app" = "Confirm Your Email";
"Confirm_your_email_address" = "Confirm your email address";
"Dial" = "Dial";
"Download_Moxtra_Mobile_app" = "Download the app or create your account online to get started.";
"Email_has_been_confirmed" = "Email Address Has Already Been Confirmed";
"Get_Started_2" = "Get Started";
"Click_below_to_login_with_existing_account" = "You've already created an account on [[OrgConfig_BrandName]]. Click below to log in and access your account.";
"Click_below_to_login_with_existing_account_for_org_name" = "You've already created an account on the [[GROUP_NAME]] Portal. Click below to log in and access your account.";
"Join_Now" = "Join Now";
"log_in" = "Log In";
"Meet_ID" = "Meet ID";
"Meet_canceled" = "Meeting Canceled: [[SESSION_TOPIC_UNENCODED]] @ [[SESSION_SST_MONTH_DAY_YEAR]] ([[SESSION_SST_TIME]] - [[SESSION_SET_TIME]] [[SESSION_TIMEZONE_ABBR]])";
"Meet_canceled_multi_date" = "Meeting Canceled: [[SESSION_TOPIC_UNENCODED]] @ [[SESSION_SST_MONTH_DAY_YEAR]] [[SESSION_SST_TIME]]";
"Meet_scheduled_to_host" = "Meeting Scheduled: [[SESSION_TOPIC_UNENCODED]] @ [[SESSION_SST_MONTH_DAY_YEAR]] ([[SESSION_SST_TIME]] - [[SESSION_SET_TIME]] [[SESSION_TIMEZONE_ABBR]])";
"Meet_scheduled_to_host_multi_date" = "Meeting Scheduled: [[SESSION_TOPIC_UNENCODED]] @ [[SESSION_SST_MONTH_DAY_YEAR]] [[SESSION_SST_TIME]]";
"Meet_updated" = "Meeting Updated: [[SESSION_TOPIC_UNENCODED]] @ [[SESSION_SST_MONTH_DAY_YEAR]] ([[SESSION_SST_TIME]] - [[SESSION_SET_TIME]] [[SESSION_TIMEZONE_ABBR]])";
"Meet_updated_multi_date" = "Meeting Updated: [[SESSION_TOPIC_UNENCODED]] @ [[SESSION_SST_MONTH_DAY_YEAR]] [[SESSION_SST_TIME]]";
"Member" = "Member";
"Members" = "Members";
"Reset_password" = "Reset Password";
"Your_account_has_been_updated" = "Your Account Has Been Updated";
"Create_account" = "Create Account";
"Reset_your_password" = "Reset your password";
"Honor_title" = "Thanks";
"What_you_will_get_as_partner_admin" = "This gives you access to the Partner Admin Portal, where you can manage your client orgs.";
"Get_ready_for_your_app" = "To complete the setup, all you need to do is confirm your account and start inviting users.";
"To_join_the_audio_using_your_phone" = "To join the audio using your phone:";
"try_again" = "Try Again";
"Email_not_found" = "Unable to Confirm Email Address";
"View_details" = "View Details";
"We_cannot_find_invitation_for_this_email_in_app" = "We couldn't find an invitation for this email address";
"User_require_reset_password" = "We've received a request to reset your password. To continue, click the button below.";
"When" = "When:";
"Your_account_is_ready" = "You already have an account";
"You_can_join_the_audio_directly_from_device_phone" = "You can join the audio directly from your device or from your phone.";
"You_have_canceled_meeting" = "You've canceled the meeting.";
"Your_partner_admin_is_ready_go" = "Your Partner Account Is Ready to Go!";
"Invitation_to_first_admin" = "Your Portal is Ready to Go!";
"Your_account_is_created_successfully" = "Your account was successfully created!";
"Your_app_is_ready_to_go" = "Your app is ready to go!";
"Members_have_not_joined" = "[[#HAS_ONE_INVITEE]]Member has[[/HAS_ONE_INVITEE]][[#HAS_TWO_INVITEE]]Members have[[/HAS_TWO_INVITEE]][[#HAS_MULTI_INVITEE]]Members have[[/HAS_MULTI_INVITEE]] not joined";
"Company_name" = "[[#HAS_OrgConfig_BrandName]][[OrgConfig_BrandName]][[/HAS_OrgConfig_BrandName]][[#NO_OrgConfig_BrandName]][[GROUP_NAME]][[/NO_OrgConfig_BrandName]]";
"Company_name_for_pt" = "[[#HAS_OrgConfig_BrandName]][[OrgConfig_BrandName_UNENCODED]][[/HAS_OrgConfig_BrandName]][[#NO_OrgConfig_BrandName]][[GROUP_NAME_UNENCODED]][[/NO_OrgConfig_BrandName]]";
"Moxo_made_you_a_partner_admin" = "You have been made a <strong>partner administrator</strong>.";
"Mutil_binder_members" = "[[TOTAL_MEMBERS]] Members";
"This_email_was_intended_for" = "This email was intended for [[TO]].";
"Contact_us" = "Contact Us";
"Contact_us_for_new_invitation" = "Please click the contact us button below to receive a new invitation.";
"Re_invitation_request" = "Resend Invitation Request";
"Hello" = "Hello";
"Request_for_re_invite_account" = "I'd like to create my account, but my invitation email has expired. Could you please send me a new one?";
"No_password_set_for_this_account" = "There is no password set up for this account yet. This may have happened if you did not accept your account invitation.";
"INVITER_NAME_is_trying_to_send_you_a_message_on_line" = "<strong>[[INVITER_NAME]]</strong> is trying to send you a message on Line";
"INVITER_NAME_UNENCODED_is_trying_to_send_you_a_message_on_line" = "[[INVITER_NAME_UNENCODED]] is trying to send you a message on Line";
"INVITER_NAME_has_created_a_secure_conversation_to_connect_line" = "[[INVITER_NAME]] has created a secure workspace to connect with you on Line. To get started, follow the steps below.";
"Add_Our_Official_Line_Account" = "Add Our Official Line Account";
"Scan_the_following_QR_Code_in_line_to_connect_with_us" = "Scan the following QR code in Line to connect with us.";
"Send_the_following_verification_code_in_a_message_line" = "Send the following verification code in a message to our Line account.";
"INVITER_NAME_is_trying_to_send_you_a_message_on_wechat" = "<strong>[[INVITER_NAME]]</strong> is trying to send you a message on WeChat";
"INVITER_NAME_UNENCODED_is_trying_to_send_you_a_message_on_wechat" = "[[INVITER_NAME_UNENCODED]] is trying to send you a message on WeChat";
"INVITER_NAME_has_created_a_secure_conversation_to_connect" = "[[INVITER_NAME]] has created a secure workspace to connect with you on WeChat. To get started, follow the steps below.";
"Your_secure_conversation_with_INVITER_NAME_was_disconnected" = "Your secure workspace with [[INVITER_NAME]] has been disconnected.";
"Add_Our_Official_WeChat_Account" = "Add Our Official WeChat Account";
"Scan_the_following_QR_Code_in_wechat_to_connect_with_us" = "Scan the following QR code in WeChat to connect with us.";
"Provide_The_Verification_Code" = "Provide the Verification Code";
"Send_the_following_verification_code_in_a_message" = "Send the following verification code in a message to our WeChat account.";
"INVITER_NAME_UNENCODED_is_trying_to_send_you_a_message_on_WhatsApp" = "[[INVITER_NAME_UNENCODED]] is trying to send you a message on WhatsApp";
"INVITER_NAME_is_trying_to_send_you_a_message_on_WhatsApp" = "<strong>[[INVITER_NAME]]</strong> is trying to send you a message on WhatsApp.";
"INVITER_NAME_has_created_a_secure_conversation_to_connect_with_you" = "[[INVITER_NAME]] has created a secure workspace to connect with you on WhatsApp. To get started, follow the steps below.";
"To_reconnect_follow_the_instructions_below" = "To reconnect, follow the instructions below.";
"New_message_submission" = "New message submission";
"A_new_message_was_submitted_through_the_contact_us_page" = "A new message was submitted through the "Contact Us" page on [[OrgConfig_BrandName]].";
"A_new_message_was_submitted_through_the_contact_us_page_for_org_name" = "A new message was submitted through the "Contact Us" page on your [[GROUP_NAME]] Portal.";
"Click_the_button_below_to_verify_your_email_address" = "Click the button below to verify the email address used for your account on [[#HAS_OrgConfig_BrandName]][[OrgConfig_BrandName]][[/HAS_OrgConfig_BrandName]][[#NO_OrgConfig_BrandName]][[GROUP_NAME]][[/NO_OrgConfig_BrandName]].";
"Click_the_button_below_to_verify_your_email_address_for_pt" = "Click the button below to verify the email address used for your account on [[#HAS_OrgConfig_BrandName]][[OrgConfig_BrandName_UNENCODED]][[/HAS_OrgConfig_BrandName]][[#NO_OrgConfig_BrandName]][[GROUP_NAME_UNENCODED]][[/NO_OrgConfig_BrandName]].";
"Click_the_button_below_to_verify_the_email_address" = "Click the button below to verify the email address used for [[#HAS_OrgConfig_BrandName]][[OrgConfig_BrandName]][[/HAS_OrgConfig_BrandName]][[#NO_OrgConfig_BrandName]][[GROUP_NAME]][[/NO_OrgConfig_BrandName]].";
"Click_the_button_below_to_verify_the_email_address_for_pt" = "Click the button below to verify the email address used for [[#HAS_OrgConfig_BrandName]][[OrgConfig_BrandName_UNENCODED]][[/HAS_OrgConfig_BrandName]][[#NO_OrgConfig_BrandName]][[GROUP_NAME_UNENCODED]][[/NO_OrgConfig_BrandName]].";
"Verify_Email_Address" = "Verify Email Address";
"To_verify_your_account" = "To verify your account, please enter the following code:";
"Verifying_your_email_ensures_you_correctly_receive" = "Verifying your email ensures that you receive important account notifications, such as password resets.";
"Join_your_team_on_the_brand_name_app" = "[[#HAS_OrgConfig_BrandName]][[OrgConfig_BrandName_UNENCODED]][[/HAS_OrgConfig_BrandName]][[#NO_OrgConfig_BrandName]][[GROUP_NAME_UNENCODED]][[/NO_OrgConfig_BrandName]] - Join your team";
"Join_your_team_now_for_pt" = "Join your team from [[#HAS_OrgConfig_BrandName]][[OrgConfig_BrandName_UNENCODED]][[/HAS_OrgConfig_BrandName]][[#NO_OrgConfig_BrandName]][[GROUP_NAME_UNENCODED]][[/NO_OrgConfig_BrandName]] now";
"Join_your_team_now" = "Join your team from [[#HAS_OrgConfig_BrandName]][[OrgConfig_BrandName]][[/HAS_OrgConfig_BrandName]][[#NO_OrgConfig_BrandName]][[GROUP_NAME]][[/NO_OrgConfig_BrandName]] now";
"We_have_sent_an_email_to_your_new_address" = "We've sent an email to your new address with a verification link.";
"Verify_your_new_email_address" = "Verify your new email address";
"Please_click_the_button_below_to_verify" = "Please click the button below to verify the new address.";
"Verify_Address" = "Verify Address";
"Your_Email_Address_Has_Been_Updated_on_app" = "[[#HAS_OrgConfig_BrandName]][[OrgConfig_BrandName_UNENCODED]][[/HAS_OrgConfig_BrandName]][[#NO_OrgConfig_BrandName]][[GROUP_NAME_UNENCODED]][[/NO_OrgConfig_BrandName]] - Your email address has been updated";
"Sent_on_behalf_of_domain" = "Sent on behalf of <a href="https://[[OrgConfig_BrandDomain]]/" style="text-decoration: none; color: #1F2126;">[[OrgConfig_BrandDomain]]</a>";
"Your_email_address_was_updated_for_brand_name" = "Your email address has been updated";
"Unable_to_reset_your_password_for_brand_name" = "Unable to reset your password on [[#HAS_OrgConfig_BrandName]][[OrgConfig_BrandName]][[/HAS_OrgConfig_BrandName]][[#NO_OrgConfig_BrandName]][[GROUP_NAME]][[/NO_OrgConfig_BrandName]]";
"Reset_Your_Brand_Name_on_app" = "[[#HAS_OrgConfig_BrandName]][[OrgConfig_BrandName_UNENCODED]][[/HAS_OrgConfig_BrandName]][[#NO_OrgConfig_BrandName]][[GROUP_NAME_UNENCODED]][[/NO_OrgConfig_BrandName]] - Reset your password";
"Webapp_Verification_Code" = "Verification Code";
"Your_Webapp_Verification_Code_for_Brand_Name" = "Your Verification Code";
"Inviter_from_brand_name_has_invited_you_chat_on_line" = "<strong>[[INVITER_NAME]]</strong> at [[#HAS_OrgConfig_BrandName]][[OrgConfig_BrandName]][[/HAS_OrgConfig_BrandName]][[#NO_OrgConfig_BrandName]][[GROUP_NAME]][[/NO_OrgConfig_BrandName]] has invited you to chat on Line.";
"Inviter_from_brand_name_invited_you_to_chat_on_line" = "[[INVITER_NAME_UNENCODED]] at [[#HAS_OrgConfig_BrandName]][[OrgConfig_BrandName_UNENCODED]][[/HAS_OrgConfig_BrandName]][[#NO_OrgConfig_BrandName]][[GROUP_NAME_UNENCODED]][[/NO_OrgConfig_BrandName]] has invited you to chat on Line";
"Inviter_from_brand_name_is_trying_to_send_you_message_on_line" = "<strong>[[INVITER_NAME]]</strong> from [[GROUP_NAME]] is trying to send you a message on Line.";
"Inviter_from_brand_name_unencoded_is_trying_to_send_you_message_on_line" = "[[INVITER_NAME]] from [[GROUP_NAME_UNENCODED]] is trying to send you a message on Line";
"Inviter_from_brand_name_has_invited_you_chat_on_wechat" = "<strong>[[INVITER_NAME]]</strong> at [[#HAS_OrgConfig_BrandName]][[OrgConfig_BrandName]][[/HAS_OrgConfig_BrandName]][[#NO_OrgConfig_BrandName]][[GROUP_NAME]][[/NO_OrgConfig_BrandName]] has invited you to chat on WeChat.";
"Inviter_from_brand_name_invited_you_to_chat_on_wechat" = "[[INVITER_NAME_UNENCODED]] at [[#HAS_OrgConfig_BrandName]][[OrgConfig_BrandName_UNENCODED]][[/HAS_OrgConfig_BrandName]][[#NO_OrgConfig_BrandName]][[GROUP_NAME_UNENCODED]][[/NO_OrgConfig_BrandName]] has invited you to chat on WeChat";
"Inviter_from_brand_name_is_trying_to_send_you_message_on_wechat" = "<strong>[[INVITER_NAME]]</strong> from [[GROUP_NAME]] is trying to send you a message on WeChat.";
"Inviter_from_brand_name_unencoded_is_trying_to_send_you_message_on_wechat" = "[[INVITER_NAME]] from [[GROUP_NAME_UNENCODED]] is trying to send you a message on WeChat";
"Inviter_from_brand_name_has_invited_you_chat_on_WhatsApp" = "<strong>[[INVITER_NAME]]</strong> at [[#HAS_OrgConfig_BrandName]][[OrgConfig_BrandName]][[/HAS_OrgConfig_BrandName]][[#NO_OrgConfig_BrandName]][[GROUP_NAME]][[/NO_OrgConfig_BrandName]] has invited you to interact securely on WhatsApp.";
"Inviter_from_brand_name_invited_you_to_chat_on_WhatsApp" = "[[INVITER_NAME_UNENCODED]] at [[#HAS_OrgConfig_BrandName]][[OrgConfig_BrandName_UNENCODED]][[/HAS_OrgConfig_BrandName]][[#NO_OrgConfig_BrandName]][[GROUP_NAME_UNENCODED]][[/NO_OrgConfig_BrandName]] has invited you to interact securely on WhatsApp.";
"Inviter_from_brand_name_is_trying_to_send_you_message_on_WhatsApp" = "<strong>[[INVITER_NAME]]</strong> at [[GROUP_NAME]] is trying to send you a message on WhatsApp.";
"Inviter_from_brand_name_unencoded_is_trying_to_send_you_message_on_WhatsApp" = "[[INVITER_NAME_UNENCODED]] at [[GROUP_NAME_UNENCODED]] is trying to send you a message on WhatsApp";
"Inviter_invited_you_to_join_your_team" = "[[INVITER_NAME]] has invited you to join your team.";
"Inviter_invited_you_to_join_your_team_for_pt" = "[[INVITER_NAME_UNENCODED]] has invited you to join your team.";
"You_have_invited_to_brand_name_create_your_account_to_start" = "You've been invited to join [[#HAS_OrgConfig_BrandName]][[OrgConfig_BrandName_UNENCODED]][[/HAS_OrgConfig_BrandName]][[#NO_OrgConfig_BrandName]][[GROUP_NAME_UNENCODED]][[/NO_OrgConfig_BrandName]] on their portal. Create your account to get started.";
"Inviter_invited_you_to_workspace_for_pt" = "[[INVITER_NAME_UNENCODED]] has invited you to a workspace.";
"Inviter_invited_you_to_workspace_for_subject" = "[[INVITER_NAME_UNENCODED]] has invited you to a workspace";
"Inviter_invited_you_to_workspace" = "[[INVITER_NAME]] has invited you to a workspace.";
"Invitation_from_company_name" = "Invitation from [[#HAS_OrgConfig_BrandName]][[OrgConfig_BrandName_UNENCODED]][[/HAS_OrgConfig_BrandName]][[#NO_OrgConfig_BrandName]][[GROUP_NAME_UNENCODED]][[/NO_OrgConfig_BrandName]]";
"Your_verification_code_is" = "[[[Sms_Prefix]]] Your verification code is";
"Host_has_invited_you_to_meet" = "[[[Sms_Prefix]]]: [[HOST_NAME_UNENCODED]] has invited you to a meeting. View details and join here: [[#IS_MOXO_MEET]]https://[[OrgConfig_BrandDomain]]/[[#IS_UNIVERSAL]]ua?id=[[/IS_UNIVERSAL]][[#IS_NOT_UNIVERSAL]]wa?id=[[/IS_NOT_UNIVERSAL]][[SHORT_URL]][[/IS_MOXO_MEET]][[#IS_NOT_MOXO_MEET]][[INTEGRATION_MEET_LINK]][[/IS_NOT_MOXO_MEET]][[#IS_PRIVATE_MEET]] Private meeting: users must be invited and logged in to join[[/IS_PRIVATE_MEET]]";
"Host_has_invited_you_to_meet_with_dial_in" = "[[[Sms_Prefix]]]: [[HOST_NAME_UNENCODED]] has invited you to a meeting. View details and join here: [[#IS_MOXO_MEET]]https://[[OrgConfig_BrandDomain]]/[[#IS_UNIVERSAL]]ua?id=[[/IS_UNIVERSAL]][[#IS_NOT_UNIVERSAL]]wa?id=[[/IS_NOT_UNIVERSAL]][[SHORT_URL]][[/IS_MOXO_MEET]][[#IS_NOT_MOXO_MEET]][[INTEGRATION_MEET_LINK]][[/IS_NOT_MOXO_MEET]][[#HAS_SESSION_PASSWORD]][[#NOT_PRIVATE_MEET]] Meeting Password: [[SESSION_PASSWORD]][[/NOT_PRIVATE_MEET]][[/HAS_SESSION_PASSWORD]] Meeting ID: [[#IS_MOXO_MEET]][[SESSION_KEY]] Participant Number: [[PARTICIPANT_NUMBER]] [[#DIAL_ITEM]]Meeting dial-in: [[DIAL_NUMBER]] [[DIAL_COUNTRY]] [[/DIAL_ITEM]]Local dial-in numbers: https://[[OrgConfig_BrandDomain]]/dial-in[[/IS_MOXO_MEET]][[#IS_NOT_MOXO_MEET]][[INTEGRATION_MEET_ID]][[/IS_NOT_MOXO_MEET]][[#IS_PRIVATE_MEET]] Private meeting: users must be invited and logged in to join[[/IS_PRIVATE_MEET]]";
"Host_has_invited_you_to_meet_with_password_no_dial_in" = "[[[Sms_Prefix]]]: [[HOST_NAME_UNENCODED]] has invited you to a meeting. View details and join here: [[#IS_MOXO_MEET]]https://[[OrgConfig_BrandDomain]]/[[#IS_UNIVERSAL]]ua?id=[[/IS_UNIVERSAL]][[#IS_NOT_UNIVERSAL]]wa?id=[[/IS_NOT_UNIVERSAL]][[SHORT_URL]][[/IS_MOXO_MEET]][[#IS_NOT_MOXO_MEET]][[INTEGRATION_MEET_LINK]][[/IS_NOT_MOXO_MEET]][[#NOT_PRIVATE_MEET]] Meeting Password: [[SESSION_PASSWORD]][[/NOT_PRIVATE_MEET]]";
"First_RM_has_invited_you_to_app" = "[[[Sms_Prefix]]] [[FIRST_RM_NAME_UNENCODED]] at [[GROUP_NAME_UNENCODED]] has invited you to join them on [[OrgConfig_BrandName_UNENCODED]]. Click the link to join: https://[[OrgConfig_BrandDomain]]/[[#IS_UNIVERSAL]]ua?id=[[/IS_UNIVERSAL]][[#IS_NOT_UNIVERSAL]]wa?id=[[/IS_NOT_UNIVERSAL]][[SHORT_URL]]";
"First_RM_has_invited_you_to_app_for_org_name" = "[[[Sms_Prefix]]] [[FIRST_RM_NAME_UNENCODED]] at [[GROUP_NAME_UNENCODED]] has invited you to join them on the [[GROUP_NAME_UNENCODED]] Portal. Click the link to join: https://[[OrgConfig_BrandDomain]]/[[#IS_UNIVERSAL]]ua?id=[[/IS_UNIVERSAL]][[#IS_NOT_UNIVERSAL]]wa?id=[[/IS_NOT_UNIVERSAL]][[SHORT_URL]]";
"First_RM_and_Second_RM_have_invited_you_to_app" = "[[[Sms_Prefix]]] [[FIRST_RM_NAME_UNENCODED]] and [[SECOND_RM_NAME_UNENCODED]] have invited you to join them on [[OrgConfig_BrandName_UNENCODED]]. Click the link to join: https://[[OrgConfig_BrandDomain]]/[[#IS_UNIVERSAL]]ua?id=[[/IS_UNIVERSAL]][[#IS_NOT_UNIVERSAL]]wa?id=[[/IS_NOT_UNIVERSAL]][[SHORT_URL]]";
"First_RM_and_Second_RM_have_invited_you_to_app_for_org_name" = "[[[Sms_Prefix]]] [[FIRST_RM_NAME_UNENCODED]] and [[SECOND_RM_NAME_UNENCODED]] have invited you to join them on the [[GROUP_NAME_UNENCODED]] Portal. Click the link to join: https://[[OrgConfig_BrandDomain]]/[[#IS_UNIVERSAL]]ua?id=[[/IS_UNIVERSAL]][[#IS_NOT_UNIVERSAL]]wa?id=[[/IS_NOT_UNIVERSAL]][[SHORT_URL]]";
"First_RM_and_others_have_invited_you_to_app" = "[[[Sms_Prefix]]] [[FIRST_RM_NAME_UNENCODED]] and [[RELATION_COUNT]] others have invited you to join them on [[OrgConfig_BrandName_UNENCODED]]. Click the link to join: https://[[OrgConfig_BrandDomain]]/[[#IS_UNIVERSAL]]ua?id=[[/IS_UNIVERSAL]][[#IS_NOT_UNIVERSAL]]wa?id=[[/IS_NOT_UNIVERSAL]][[SHORT_URL]]";
"First_RM_and_others_have_invited_you_to_app_for_org_name" = "[[[Sms_Prefix]]] [[FIRST_RM_NAME_UNENCODED]] and [[RELATION_COUNT]] others have invited you to join them on the [[GROUP_NAME_UNENCODED]] Portal. Click the link to join: https://[[OrgConfig_BrandDomain]]/[[#IS_UNIVERSAL]]ua?id=[[/IS_UNIVERSAL]][[#IS_NOT_UNIVERSAL]]wa?id=[[/IS_NOT_UNIVERSAL]][[SHORT_URL]]";
"Your_account_has_been_updated_to_use_this_phone_number" = "[[[Sms_Prefix]]] Your account has been updated to use this phone number.";
"The_phone_number_for_your_account_has_been_changed" = "[[[Sms_Prefix]]] The phone number for your account has been changed.";
"Inviter_has_invited_you_to_brand_name" = "[[[Sms_Prefix]]] [[INVITER_NAME_UNENCODED]] at [[GROUP_NAME_UNENCODED]] has invited you to join them on [[OrgConfig_BrandName_UNENCODED]]. Click the link to join: https://[[OrgConfig_BrandDomain]]/[[#IS_UNIVERSAL]]ua?id=[[/IS_UNIVERSAL]][[#IS_NOT_UNIVERSAL]]wa?id=[[/IS_NOT_UNIVERSAL]][[SHORT_URL]]";
"Inviter_has_invited_you_to_brand_name_for_org_name" = "[[[Sms_Prefix]]] [[INVITER_NAME_UNENCODED]] at [[GROUP_NAME_UNENCODED]] has invited you to join them on the [[GROUP_NAME_UNENCODED]] Portal. Click the link to join: https://[[OrgConfig_BrandDomain]]/[[#IS_UNIVERSAL]]ua?id=[[/IS_UNIVERSAL]][[#IS_NOT_UNIVERSAL]]wa?id=[[/IS_NOT_UNIVERSAL]][[SHORT_URL]]";
"Verify_Your_Email_Address" = "Verify your email address";
"Your_Plan_is_Active" = "Your Plan is Active!";
"Your_plan_is_now_active" = "Your plan is now active!";
"Thank_you_for_upgrading" = "Thank you for upgrading! Your plan is now active and will be automatically renewed on [[RENEWED_TIME]].";
"We_will_send_a_copy_of_your_receipt" = "We'll also send a copy of your receipt to this email address. You can access your billing and plan information at any time via the admin portal on the web.";
"If_mail_sent_by_mistake_pls_click_here_contact_us" = "If it was sent to you by mistake, please <a href="https://support.moxo.com" style="text-decoration: none; color: #1F2126;">click here to contact us</a>.";
"Your_plan_was_cancelled" = "Your plan has been cancelled.";
"We_are_sad_to_see_you_go" = "We're sad to see you go and want to hear how we could've supported you better. Do not hesitate to reach out to us below.";
"Reactivate" = "Reactivate";
"Your_Moxtra_Trial_Has_Expired" = "Your Trial Has Expired";
"Your_free_trial_has_expired" = "Your free trial has ended.";
"Your_14_day_trial_has_concluded" = "Your 14-day trial for <strong>[[#HAS_OrgConfig_BrandName]][[OrgConfig_BrandName]][[/HAS_OrgConfig_BrandName]][[#NO_OrgConfig_BrandName]][[GROUP_NAME]][[/NO_OrgConfig_BrandName]]</strong> has now ended.";
"Your_14_day_trial_has_concluded_for_pt" = "Your 14-day trial for [[#HAS_OrgConfig_BrandName]][[OrgConfig_BrandName_UNENCODED]][[/HAS_OrgConfig_BrandName]][[#NO_OrgConfig_BrandName]][[GROUP_NAME_UNENCODED]][[/NO_OrgConfig_BrandName]] has now ended.";
"Upgrade" = "Upgrade";
"Upgrade_Now" = "Upgrade Now";
"Moxtra_Trial_Expiring_Tomorrow" = "Trial Expiring Tomorrow";
"Your_free_trial_expires_tomorrow" = "Your free trial ends tomorrow.";
"Your_trial_concludes_tomorrow" = "Your trial for <strong>[[#HAS_OrgConfig_BrandName]][[OrgConfig_BrandName]][[/HAS_OrgConfig_BrandName]][[#NO_OrgConfig_BrandName]][[GROUP_NAME]][[/NO_OrgConfig_BrandName]]</strong> ends tomorrow.";
"Your_trial_concludes_tomorrow_for_pt" = "Your trial for [[#HAS_OrgConfig_BrandName]][[OrgConfig_BrandName_UNENCODED]][[/HAS_OrgConfig_BrandName]][[#NO_OrgConfig_BrandName]][[GROUP_NAME_UNENCODED]][[/NO_OrgConfig_BrandName]] ends tomorrow.";
"Ensure_continued_portal_access_for_your_clients" = "Upgrade now to ensure continued portal access for your clients and team members.";
"Your_free_trial_expires_next_week" = "Your free trial expires next week.";
"Your_trial_for_brand_name_ends_in_seven_days" = "Your trial for <strong>[[#HAS_OrgConfig_BrandName]][[OrgConfig_BrandName]][[/HAS_OrgConfig_BrandName]][[#NO_OrgConfig_BrandName]][[GROUP_NAME]][[/NO_OrgConfig_BrandName]]</strong> ends in 7 days.";
"Your_trial_for_brand_name_ends_in_seven_days_for_pt" = "Your trial for [[#HAS_OrgConfig_BrandName]][[OrgConfig_BrandName_UNENCODED]][[/HAS_OrgConfig_BrandName]][[#NO_OrgConfig_BrandName]][[GROUP_NAME_UNENCODED]][[/NO_OrgConfig_BrandName]] ends in 7 days.";
"Upgrade_now_to_continue_leveraging_your_portal" = "Upgrade now to continue leveraging your portal and driving projects to completion with your clients, partners, and team members.";
"Moxtra_Trial_Expiring_In_one_Week" = "Trial Expiring in 1 Week";
"Your_plan_has_expired" = "Your plan has expired.";
"We_were_unable_to_process_your_payment" = "We were unable to process your payment as scheduled. Your Portal has been deactivated, and users can no longer log in.";
"To_reset_your_brand_name_password_on_app" = "To reset your password, please enter the following code:";
"An_admin_is_asking_you_to_reset_your_password" = "[[[Sms_Prefix]]] An admin is asking you to reset your password. Click the link to continue. https://[[OrgConfig_BrandDomain]]/[[#IS_UNIVERSAL]]ua?id=[[/IS_UNIVERSAL]][[#IS_NOT_UNIVERSAL]]wa?id=[[/IS_NOT_UNIVERSAL]][[SHORT_URL]]";
"Meeting_Password" = "Meeting Password:";
"Add_Our_Official_WhatsApp_Account" = "Add Our Official WhatsApp Account";
"Click_the_button_below_to_connect_with_us" = "Click the button below to connect with us.";
"Add_Account" = "Add Account";
"Send_verification_code_to_whatsapp_account" = "Send the following verification code in a message to our WhatsApp account.";
"Meeting_ID" = "Meeting ID:";
"Participant_Number" = "Participant Number:";
"Meeting_link" = "Meeting link:";
"Meeting_dial_in" = "Meeting dial-in:";
"Local_dial_in_numbers" = "Local dial-in numbers:";
"Download_the_Moxtra_mobile_app" = "[[[Sms_Prefix]]] Download the [[OrgConfig_BrandName]] mobile app: https://[[OrgConfig_BrandDomain]]/web/#/downloadLink";
"Download_the_Moxtra_mobile_app_for_org_name" = "[[[Sms_Prefix]]] Download the [[GROUP_NAME]] Portal mobile app: https://[[OrgConfig_BrandDomain]]/web/#/downloadLink";
"Use_following_portal_url_when_loggin_in" =  "Use the following Portal URL when logging in: https://[[OrgConfig_BrandDomain]]";
"Here_are_links_to_access_org_on_app" = "Here are links to access your mobile app:";
"Welcome_to_company" = "You're in! Welcome to [[#HAS_OrgConfig_BrandName]][[OrgConfig_BrandName_UNENCODED]][[/HAS_OrgConfig_BrandName]][[#NO_OrgConfig_BrandName]][[GROUP_NAME_UNENCODED]][[/NO_OrgConfig_BrandName]]";
"If_it_was_sent_by_mistake_pls_click_here_contact_us" = "If it was sent to you by mistake, please click here to contact us (https://support.moxo.com).";
"Sent_on_behalf_of_domain_without_a" = "Sent on behalf of [[OrgConfig_BrandDomain]] (https://[[OrgConfig_BrandDomain]]/)";
"Email_has_been_changed_to_without_label" = "An administrator from [[#HAS_OrgConfig_BrandName]][[OrgConfig_BrandName]][[/HAS_OrgConfig_BrandName]][[#NO_OrgConfig_BrandName]][[GROUP_NAME]][[/NO_OrgConfig_BrandName]] has changed the email address of your account to [[NEW_EMAIL]].";
"Moxo_made_you_a_partner_admin_without_label" = "You have been made a partner administrator.";
"Inviter_from_brand_name_is_trying_to_send_you_message_on_wechat_without_label" = "[[INVITER_NAME_UNENCODED]] from [[GROUP_NAME_UNENCODED]] is trying to send you a message on WeChat.";
"INVITER_NAME_is_trying_to_send_you_a_message_on_wechat_without_label" = "[[INVITER_NAME_UNENCODED]] is trying to send you a message on WeChat";
"Inviter_from_brand_name_is_trying_to_send_you_message_on_line_without_label" = "[[INVITER_NAME_UNENCODED]] from [[GROUP_NAME_UNENCODED]] is trying to send you a message on Line.";
"INVITER_NAME_is_trying_to_send_you_a_message_on_line_without_label" = "[[INVITER_NAME_UNENCODED]] is trying to send you a message on Line";
"Inviter_from_brand_name_is_trying_to_send_you_message_on_WhatsApp_without_label" = "[[INVITER_NAME_UNENCODED]] at [[GROUP_NAME_UNENCODED]] is trying to send you a message on WhatsApp.";
"INVITER_NAME_is_trying_to_send_you_a_message_on_WhatsApp_without_label" = "[[INVITER_NAME_UNENCODED]] is trying to send you a message on WhatsApp.";
"Your_Moxtra_Plan_Was_Cancelled" = "Your Moxo Plan Has Been Cancelled";
"We_have_received_your_cancellation_and_stopped_your_plan" = "We've received your cancellation and stopped your plan. Your Portal has been deactivated, and users can no longer log in.";
"Payment_Information_Updated" = "Payment Information Updated";
"Please_contact_us_below_to_update_your_payment" = "Please contact us using the link below to update your payment.";
"Your_Portal_was_successfully_created" = "Your portal has been created successfully!";
"Here_is_the_link_to_your_Portal_on_Moxtra" = "Here is the link to your Portal:";
"Download_mobile_app_to_get_notifications" = "Download our mobile app to get real-time notifications:";
"You_can_access_your_Portal_on_mobile_below" = "You can access your Portal on mobile using the link below:";
"Click_below_to_login_with_existing_account_for_pt" = "You've already created an account on [[OrgConfig_BrandName_UNENCODED]]. Click below to log in and access your account.";
"Click_below_to_login_with_existing_account_for_org_name_for_pt" = "You've already created an account on the [[GROUP_NAME_UNENCODED]] Portal. Click below to log in and access your account.";
"A_new_message_was_submitted_through_the_contact_us_page_for_pt" = "A new message was submitted through the "Contact Us" page on [[OrgConfig_BrandName_UNENCODED]].";
"A_new_message_was_submitted_through_the_contact_us_page_for_org_name_for_pt" = "A new message was submitted through the "Contact Us" page on your [[GROUP_NAME_UNENCODED]] Portal.";
"Unable_to_reset_your_password_for_brand_name_for_pt" = "Unable to reset your password on [[#HAS_OrgConfig_BrandName]][[OrgConfig_BrandName_UNENCODED]][[/HAS_OrgConfig_BrandName]][[#NO_OrgConfig_BrandName]][[GROUP_NAME_UNENCODED]][[/NO_OrgConfig_BrandName]]";
"INVITER_NAME_has_created_a_secure_conversation_to_connect_for_pt" = "[[INVITER_NAME_UNENCODED]] has created a secure workspace to connect with you on WeChat. To get started, follow the steps below.";
"INVITER_NAME_has_created_a_secure_conversation_to_connect_for_pt_line" = "[[INVITER_NAME_UNENCODED]] has created a secure workspace to connect with you on Line. To get started, follow the steps below.";
"Your_secure_conversation_with_INVITER_NAME_was_disconnected_for_pt" = "Your secure workspace with [[INVITER_NAME_UNENCODED]] has been disconnected.";
"INVITER_NAME_has_created_a_secure_conversation_to_connect_with_you_for_pt" = "[[INVITER_NAME_UNENCODED]] has created a secure workspace to connect with you on WhatsApp. To get started, follow the steps below.";
"Subscription_Ends_in_ten_Days" = "Subscription Ends in 10 Days";
"Subscription_ends_in_ten_days" = "Subscription ends in 10 days.";
"When_your_subscription_ends" = "When your subscription ends, users will no longer have access to your portal.";
"New_activity_in_your_Portal" = "New activity in your portal";
"You_have_one_update_from_brand_name" = "You have [[NEW_UPDATES_COUNT]] update from [[OrgConfig_BrandName]].";
"You_have_number_updates_from_brand_name" = "You have [[NEW_UPDATES_COUNT]] updates from [[OrgConfig_BrandName]].";
"You_have_one_update_from_the_org_Portal" = "You have [[NEW_UPDATES_COUNT]] update from the [[GROUP_NAME]] Portal.";
"You_have_number_updates_from_the_org_Portal" = "You have [[NEW_UPDATES_COUNT]] updates from the [[GROUP_NAME]] Portal.";
"Here_is_what_you_have_missed" = "Here’s what you’ve missed:";
"one_New_Message" = "<span style="font-size: 23px;color: #000;font-weight: 700;">[[UNREAD_MESSAGE_COUNT]]</span> New Message";
"number_New_Messages" = "<span style="font-size: 23px;color: #000;font-weight: 700;">[[UNREAD_MESSAGE_COUNT]]</span> New Messages";
"one_Active_Conversation" = "<span style="font-size: 23px;color: #000;font-weight: 700;">[[UNREAD_BINDER_COUNT]]</span> Active Workspace";
"number_Active_Conversations" = "<span style="font-size: 23px;color: #000;font-weight: 700;">[[UNREAD_BINDER_COUNT]]</span> Active Workspaces";
"View_Now" = "View Now";
"Certificate_of_completion" = "Certificate of Completion";
"Doc_id" = "Doc ID";
"Assignees" = "Assignees";
"File_name" = "File name";
"Signatures" = "Signatures";
"Initials" = "Initials";
"Document_pages" = "Document Pages";
"Certificate_pages" = "Certificate Pages";
"Signature_originator" = "Signature Originator";
"Name" = "Name";
"Time" = "Time";
"Email" = "Email";
"Ip_address" = "IP Address";
"Phone_number" = "Phone Number";
"Source" = "Source";
"Account_type" = "Account Type";
"Organization" = "Organization";
"UniqueId" = "UniqueId";
"Phone" = "Phone";
"IOS" = "iOS";
"Web" = "Web";
"Android" = "Android";
"Signer" = "Signer";
"Viewed" = "Viewed";
"Signed" = "Signed";
"Signature_adoption" = "Signature Adoption";
"Drawn_on_device" = "Drawn on Device";
"Preselected" = "Pre-selected Style";
"Signature_none" = "N/A (non-signature fields only)";
"Signer_events" = "Signer Events";
"N_A" = "N/A";
"Unknown" = "Unknown";
"To_retrieve_your_Portal_URL" = "To retrieve your Portal URL, please enter the following code:";
"Code_is_your_Moxtra_verification_code" = "[Moxo] [[CODE_1]][[CODE_2]][[CODE_3]][[CODE_4]][[CODE_5]][[CODE_6]] is your Moxo verification code.";
"Host_has_started_a_meeting" = "[[[Sms_Prefix]]]: [[HOST_NAME_UNENCODED]] has started a meeting and is inviting you to join: [[#IS_MOXO_MEET]]https://[[OrgConfig_BrandDomain]]/[[#IS_UNIVERSAL]]ua?id=[[/IS_UNIVERSAL]][[#IS_NOT_UNIVERSAL]]wa?id=[[/IS_NOT_UNIVERSAL]][[SHORT_URL]][[/IS_MOXO_MEET]][[#IS_NOT_MOXO_MEET]][[INTEGRATION_MEET_LINK]][[/IS_NOT_MOXO_MEET]]";
"Host_has_started_a_meeting_with_password_no_dial_in" = "[[[Sms_Prefix]]]: [[HOST_NAME_UNENCODED]] has started a meeting and is inviting you to join: [[#IS_MOXO_MEET]]https://[[OrgConfig_BrandDomain]]/[[#IS_UNIVERSAL]]ua?id=[[/IS_UNIVERSAL]][[#IS_NOT_UNIVERSAL]]wa?id=[[/IS_NOT_UNIVERSAL]][[SHORT_URL]][[/IS_MOXO_MEET]][[#IS_NOT_MOXO_MEET]][[INTEGRATION_MEET_LINK]][[/IS_NOT_MOXO_MEET]][[#NOT_PRIVATE_MEET]] Meeting Password: [[SESSION_PASSWORD]][[/NOT_PRIVATE_MEET]]";
"Host_has_started_a_meeting_with_dial_in" = "[[[Sms_Prefix]]]: [[HOST_NAME_UNENCODED]] has started a meeting and is inviting you to join: [[#IS_MOXO_MEET]]https://[[OrgConfig_BrandDomain]]/[[#IS_UNIVERSAL]]ua?id=[[/IS_UNIVERSAL]][[#IS_NOT_UNIVERSAL]]wa?id=[[/IS_NOT_UNIVERSAL]][[SHORT_URL]][[/IS_MOXO_MEET]][[#IS_NOT_MOXO_MEET]][[INTEGRATION_MEET_LINK]][[/IS_NOT_MOXO_MEET]][[#HAS_SESSION_PASSWORD]][[#NOT_PRIVATE_MEET]] Meeting Password: [[SESSION_PASSWORD]][[/NOT_PRIVATE_MEET]][[/HAS_SESSION_PASSWORD]] Meeting ID: [[#IS_MOXO_MEET]][[SESSION_KEY]] Participant Number: [[PARTICIPANT_NUMBER]] [[#DIAL_ITEM]]Meeting dial-in: [[DIAL_NUMBER]] [[DIAL_COUNTRY]] [[/DIAL_ITEM]]Local dial-in numbers: https://[[OrgConfig_BrandDomain]]/dial-in[[/IS_MOXO_MEET]][[#IS_NOT_MOXO_MEET]][[INTEGRATION_MEET_ID]][[/IS_NOT_MOXO_MEET]]";
"Inviter_added_you_to_flow_conversation" = "[[[Sms_Prefix]]] [[INVITER_NAME]] has invited you to a flow workspace. Click the link to join: https://[[OrgConfig_BrandDomain]]/[[#IS_UNIVERSAL]]ua?id=[[/IS_UNIVERSAL]][[#IS_NOT_UNIVERSAL]]wa?id=[[/IS_NOT_UNIVERSAL]][[SHORT_URL]]";
"Inviter_added_you_to_group_conversation" = "[[[Sms_Prefix]]] [[INVITER_NAME]] has invited you to a group workspace. Click the link to join: https://[[OrgConfig_BrandDomain]]/[[#IS_UNIVERSAL]]ua?id=[[/IS_UNIVERSAL]][[#IS_NOT_UNIVERSAL]]wa?id=[[/IS_NOT_UNIVERSAL]][[SHORT_URL]]";
"brand_name_meeting_scheduling_error" = "[[OrgConfig_BrandName]] meeting scheduling error";
"brand_name_meeting_scheduling_error_for_pt" = "[[OrgConfig_BrandName_UNENCODED]] meeting scheduling error";
"group_name_meeting_scheduling_error" = "[[GROUP_NAME]] meeting scheduling error";
"group_name_meeting_scheduling_error_for_pt" = "[[GROUP_NAME_UNENCODED]] meeting scheduling error";
"We_noticed_you_encountered_an_error" = "We noticed an error while trying to schedule a [[OrgConfig_BrandName]] meeting via our [[EXT_CALENDAR_TYPE]] Calendar add-on on [[SESSION_SCHEDULE_TIME]].";
"We_noticed_you_encountered_an_error_for_pt" = "We noticed an error while trying to schedule a [[OrgConfig_BrandName_UNENCODED]] meeting via our [[EXT_CALENDAR_TYPE]] Calendar add-on on [[SESSION_SCHEDULE_TIME]].";
"We_noticed_you_encountered_an_error_for_org_name" = "We noticed an error while trying to schedule a [[GROUP_NAME]] meeting via our [[EXT_CALENDAR_TYPE]] Calendar add-on on [[SESSION_SCHEDULE_TIME]].";
"We_noticed_you_encountered_an_error_for_org_name_for_pt" = "We noticed an error while trying to schedule a [[GROUP_NAME_UNENCODED]] meeting via our [[EXT_CALENDAR_TYPE]] Calendar add-on on [[SESSION_SCHEDULE_TIME]].";
"Here_are_the_details_about_what_happened" = "Here are the details about what happened:";
"New_action_assigned_to_you" = "New action assigned to you";
"You_were_assigned_to_a_new_action_on_brand_name" = "You have been assigned a new action by [[INVITER_NAME]]. Click below to review the details and take action.";
"You_were_assigned_to_a_new_action_on_brand_name_for_pt" = "You have been assigned a new action by [[INVITER_NAME_UNENCODED]]. Click below to review the details and take action.";
"You_were_assigned_to_a_new_action_for_sms" = "[[[Sms_Prefix]]] You have been assigned to a new action. Click the link to review the details and take action. https://[[OrgConfig_BrandDomain]]/[[#IS_UNIVERSAL]]ua?id=[[/IS_UNIVERSAL]][[#IS_NOT_UNIVERSAL]]wa?id=[[/IS_NOT_UNIVERSAL]][[SHORT_URL]]";
"in_conversation_name" = "in: [[BOARD_NAME]]";
"Approval" = "Approval";
"Acknowledgement" = "Acknowledgement";
"File_Request" = "File Request";
"To_Do" = "To-Do";
"esign" = "E-Sign";
"Review_and_Act" = "Review & Act";
"Due_date" = "Due on [[ACTION_DUE_DATE]]";
"Action_due_today" = "Action Due Today";
"Action_due_soon" = "Action Due Soon";
"Action_over_due" = "Action Overdue";
"You_have_an_action_that_is_due_today" = "[[[Sms_Prefix]]] You have an action that is due today. Please review and act as soon as possible. https://[[OrgConfig_BrandDomain]]/[[#IS_UNIVERSAL]]ua?id=[[/IS_UNIVERSAL]][[#IS_NOT_UNIVERSAL]]wa?id=[[/IS_NOT_UNIVERSAL]][[SHORT_URL]]";
"You_have_an_action_that_is_overdue_with_link" = "[[[Sms_Prefix]]] You have an action that is overdue. Please review and act as soon as possible. https://[[OrgConfig_BrandDomain]]/[[#IS_UNIVERSAL]]ua?id=[[/IS_UNIVERSAL]][[#IS_NOT_UNIVERSAL]]wa?id=[[/IS_NOT_UNIVERSAL]][[SHORT_URL]]";
"Assignee_has_an_action_that_is_overdue" = "[[[Sms_Prefix]]] [[#HAS_ONE_ASSIGNEE]][[ASSIGNEE_NAME_UNENCODED]] has[[/HAS_ONE_ASSIGNEE]][[#HAS_TWO_ASSIGNEES]][[ASSIGNEE_NAME_1_UNENCODED]] and [[ASSIGNEE_NAME_2_UNENCODED]] have[[/HAS_TWO_ASSIGNEES]][[#HAS_MULTI_ASSIGNEES]][[ASSIGNEE_NAME_UNENCODED]] and others have[[/HAS_MULTI_ASSIGNEES]] an action that is overdue. Please review and follow up to ensure progress. https://[[OrgConfig_BrandDomain]]/[[#IS_UNIVERSAL]]ua?id=[[/IS_UNIVERSAL]][[#IS_NOT_UNIVERSAL]]wa?id=[[/IS_NOT_UNIVERSAL]][[SHORT_URL]]";
"Due_Today" = "Due Today";
"One_Step" = "1 Step";
"Number_Steps" = "[[FLOW_STEP_COUNT]] Steps";
"Transaction" = "Transaction";
"Meet_request" = "Time Booking";
"Set_your_password" = "Set your password";
"Verify" = "Verify";
"Form" = "Form";
"PdfForm" = "PDF Form";
"meet_topic" = "topic";
"time" = "time";
"Daily_from_time" = "Daily from [[SESSION_SST_TIME]]";
"Daily_from_time_from_date" = "Daily from [[SESSION_SST_TIME]], [[SESSION_SST_YEAR_MONTH_DAY]] to [[END_DATE]]";
"Every_days_from_time" = "Every [[DAILY_SPAN]] days from [[SESSION_SST_TIME]]";
"Every_days_from_time_from_date" = "Every [[DAILY_SPAN]] days from [[SESSION_SST_TIME]] from [[SESSION_SST_YEAR_MONTH_DAY]] to [[END_DATE]]";
"Weekly_from_time" = "Weekly from [[SESSION_SST_TIME]] on [[SELECTED_DATE]]";
"Weekly_from_time_from_date" = "Weekly from [[SESSION_SST_TIME]] on [[SELECTED_DATE]], [[SESSION_SST_YEAR_MONTH_DAY]] to [[END_DATE]]";
"Every_weeks_from_time" = "Every [[WEEKLY_SPAN]] weeks from [[SESSION_SST_TIME]] on [[SELECTED_DATE]]";
"Every_weeks_from_time_from_date" = "Every [[WEEKLY_SPAN]] weeks from [[SESSION_SST_TIME]] on [[SELECTED_DATE]] from [[SESSION_SST_YEAR_MONTH_DAY]] to [[END_DATE]]";
"Monthly_from_time" = "Monthly from [[SESSION_SST_TIME]] on day [[SELECTED_DATE]]";
"Monthly_from_time_from_date" = "Monthly from [[SESSION_SST_TIME]] on day [[SELECTED_DATE]] from [[SESSION_SST_YEAR_MONTH_DAY]] to [[END_DATE]]";
"Host_has_changed_meeting_secure_setting" = "[[[Sms_Prefix]]]: [[HOST_NAME_UNENCODED]] has changed the meeting's security settings. View details and join here: [[#IS_MOXO_MEET]]https://[[OrgConfig_BrandDomain]]/[[#IS_UNIVERSAL]]ua?id=[[/IS_UNIVERSAL]][[#IS_NOT_UNIVERSAL]]wa?id=[[/IS_NOT_UNIVERSAL]][[SHORT_URL]][[/IS_MOXO_MEET]][[#IS_NOT_MOXO_MEET]][[INTEGRATION_MEET_LINK]][[/IS_NOT_MOXO_MEET]][[#HAS_SESSION_PASSWORD]][[#NOT_PRIVATE_MEET]] Meeting Password: [[SESSION_PASSWORD]][[/NOT_PRIVATE_MEET]][[/HAS_SESSION_PASSWORD]][[#NO_DEFAULT_NUMBER]][[#IS_PRIVATE_MEET]] Private meeting: users must be invited and logged in to join[[/IS_PRIVATE_MEET]][[/NO_DEFAULT_NUMBER]][[#HAS_DEFAULT_NUMBER]] Meeting ID: [[#IS_MOXO_MEET]][[SESSION_KEY]] Participant Number: [[PARTICIPANT_NUMBER]] [[#DIAL_ITEM]]Meeting dial-in: [[DIAL_NUMBER]] [[DIAL_COUNTRY]] [[/DIAL_ITEM]]Local dial-in numbers: https://[[OrgConfig_BrandDomain]]/dial-in[[/IS_MOXO_MEET]][[#IS_NOT_MOXO_MEET]][[INTEGRATION_MEET_ID]][[/IS_NOT_MOXO_MEET]][[#IS_PRIVATE_MEET]] Private meeting: users must be invited and logged in to join[[/IS_PRIVATE_MEET]][[/HAS_DEFAULT_NUMBER]]";
"Here_is_your_new_link_to_access_flow_conversation" = "Here is your new link to access the flow workspace "[[BOARD_NAME]]"";
"please_do_not_share_this_email" = "For your security, please do not share this email or link with others.";
"Here_is_your_new_link_to_access_flow_conversation_for_sms" = "[[[Sms_Prefix]]] Here is your new link to access the flow workspace “[[BOARD_NAME_UNENCODED]]”: https://[[OrgConfig_BrandDomain]]/[[#IS_UNIVERSAL]]ua?id=[[/IS_UNIVERSAL]][[#IS_NOT_UNIVERSAL]]wa?id=[[/IS_NOT_UNIVERSAL]][[SHORT_URL]]";
"Here_is_your_new_link_to_access_conversation_for_sms" = "[[[Sms_Prefix]]] Here is your new link to access the workspace “[[BOARD_NAME_UNENCODED]]”: https://[[OrgConfig_BrandDomain]]/[[#IS_UNIVERSAL]]ua?id=[[/IS_UNIVERSAL]][[#IS_NOT_UNIVERSAL]]wa?id=[[/IS_NOT_UNIVERSAL]][[SHORT_URL]]";
"You_secure_access_link_for_binder" = "Your secure access link for "[[BOARD_NAME_UNENCODED]]" on [[#HAS_OrgConfig_BrandName]][[OrgConfig_BrandName_UNENCODED]][[/HAS_OrgConfig_BrandName]][[#NO_OrgConfig_BrandName]][[GROUP_NAME_UNENCODED]][[/NO_OrgConfig_BrandName]]";
"Streamline_your_business_services_with_your_interaction_portal" = "Streamline your business services with your own interaction portal.";
"New_messages_in_binder" = "New messages in "[[BOARD_NAME_UNENCODED]]"";
"You_have_new_messages_in_binder" = "You have new messages in “[[BOARD_NAME]]”.";
"View_Messages" = "View Messages";
"You_have_new_messages_in_binder_for_sms" = "[[[Sms_Prefix]]] You have new messages in “[[BOARD_NAME_UNENCODED]]”. Click here to view the messages:";
"approve_webapp" = "Approve Web App";
"approve_webppp_to_cluster_success" = "Your application (name: [[WEBAPP_NAME]]) has been approved at [[CLUSTER]]. Please go to the developer portal to check the details.";
"DocuSign" = "DocuSign";
"Created_new_workspace_failed_for_subject" = "Failed to create new workspace";
"Created_new_workspace_failed_for_topic" = "Failed to Create New Workspace";
"You_have_reached_the_limit_of_plan" = "You have reached the limit of your current plan. Contact the administrator to upgrade your plan.";
"New_action_awaited_your_preparation" = "New action awaits your preparation";
"Review" = "Review";
"This_action_requires_your_preparation" = "This action requires your preparation. Click below to review the details and get it ready for assignees.";
"This_action_requires_your_preparation_for_sms" = "[[[Sms_Prefix]]] An action requires your preparation. Click the link to review the details and get it ready for assignees: https://[[OrgConfig_BrandDomain]]/[[#IS_UNIVERSAL]]ua?id=[[/IS_UNIVERSAL]][[#IS_NOT_UNIVERSAL]]wa?id=[[/IS_NOT_UNIVERSAL]][[SHORT_URL]]";
"Decision" = "Decision";
"Set_password" = "Set Password";
"No_Account_Password" = "No Account Password";
"Not_have_password_click_set_password" = "Your account doesn't have a password yet. Click below to set a password.";
"Join_your_team" = "Join Your Team";
"Inviter_from_brand_name_invited_you_to_join_your_team" = "[[INVITER_NAME]] from [[#HAS_OrgConfig_BrandName]][[OrgConfig_BrandName_UNENCODED]][[/HAS_OrgConfig_BrandName]][[#NO_OrgConfig_BrandName]][[GROUP_NAME_UNENCODED]][[/NO_OrgConfig_BrandName]] has invited you to join your team.";
"Inviter_from_brand_name_invited_you_to_join_your_team_for_pt" = "[[INVITER_NAME_UNENCODED]] from [[#HAS_OrgConfig_BrandName]][[OrgConfig_BrandName_UNENCODED]][[/HAS_OrgConfig_BrandName]][[#NO_OrgConfig_BrandName]][[GROUP_NAME_UNENCODED]][[/NO_OrgConfig_BrandName]] has invited you to join your team.";
"Join_Team" = "Join Team";
"Welcome" = "Welcome!";
"Success_created_your_account" = "You have successfully created your account.";
"Web_access_link" = "Web access link:";
"Download_mobile_app_take_action_any_time_any_where" = "Download the mobile app on your phone and receive instant notifications. Everything works seamlessly across your devices, so you can take action anytime, from any place.";
"Invitation_from_company" = "Invitation from [[#HAS_OrgConfig_BrandName]][[OrgConfig_BrandName]][[/HAS_OrgConfig_BrandName]][[#NO_OrgConfig_BrandName]][[GROUP_NAME]][[/NO_OrgConfig_BrandName]]";
"Inviter_from_brand_name_invited_you_to_join_protal" = "[[INVITER_NAME]] has invited you to join [[#HAS_OrgConfig_BrandName]][[OrgConfig_BrandName_UNENCODED]][[/HAS_OrgConfig_BrandName]][[#NO_OrgConfig_BrandName]][[GROUP_NAME_UNENCODED]][[/NO_OrgConfig_BrandName]] on their portal. Create your account to get started.";
"Your_account_email_has_changed" = "Your Account Email Has Changed";
"Verify_Email_Address" = "Verify Email Address";
"Password_updated" = "Password Updated";
"You_have_successfully_changed_account_password" = "You have successfully changed your account password.";
"To_verify_your_account_on_app" = "To verify your account, please enter the following code:";
"Join_workspace" = "Join Workspace";
"Inviter_from_company_invate_you_to_workspace" = "[[INVITER_NAME]] from [[#HAS_OrgConfig_BrandName]][[OrgConfig_BrandName]][[/HAS_OrgConfig_BrandName]][[#NO_OrgConfig_BrandName]][[GROUP_NAME]][[/NO_OrgConfig_BrandName]] has invited you to a workspace.";
"Inviter_from_company_invate_you_to_workspace_for_pt" = "[[INVITER_NAME_UNENCODED]] from [[#HAS_OrgConfig_BrandName]][[OrgConfig_BrandName_UNENCODED]][[/HAS_OrgConfig_BrandName]][[#NO_OrgConfig_BrandName]][[GROUP_NAME_UNENCODED]][[/NO_OrgConfig_BrandName]] has invited you to a workspace.";
"New_Action" = "New Action";
"Assign_new_action_for_you" = "You have been assigned a new action.";
"Click_review_detail_and_take_action" = "Click below to review the details and take action.";
"New_Messages" = "New Messages";
"Receive_message_in_workspace" = "You have received new messages in the following workspace.";
"Click_below_view_messages" = "Click below to view the messages.";
"New_Access_Link" = "New Access Link";
"New_link_access_to_workspace" = "Here is the new link to access your workspace.";
"Welcome_message" = "welcome message";
"Do_not_share_this_email" = "Do not share this email";
"Meeting_scheduled" = "Meeting Scheduled";
"Meeting_invite" = "Meeting Invite";
"Meeting_canceled" = "Meeting Canceled";
"Inviter_from_brand_name_canceled_meeting" = "[[INVITER_NAME]] has canceled the meeting.";
"Inviter_from_brand_name_canceled_meeting_for_pt" = "[[INVITER_NAME_UNENCODED]] has canceled the meeting.";
"Inviter_from_brand_name_invited_you_to_meeting" = "[[INVITER_NAME]] invited you to a meeting.";
"Inviter_from_brand_name_invited_you_to_meeting_for_pt" = "[[INVITER_NAME_UNENCODED]] invited you to a meeting.";
"You_have_scheduled_meeting_use_calender_file_to_update" = "You have scheduled the meeting. Please use the attached calendar file (.ics) to update your calendar.";
"You_have_action_due_today" = "You have an action that is due today.";
"You_have_an_action_that_is_due_today_for_review" = "You have an action that is due today. Please review and act as soon as possible.";
"You_have_an_action_that_will_due_soon" = "You have an action that will be due soon.";
"You_have_an_action_that_is_overdue" = "You have an action that is overdue. Please review and act as soon as possible.";
"Assignee_has_an_action_that_is_overdue_for_review" = "[[#HAS_ONE_ASSIGNEE]][[ASSIGNEE_NAME]] has[[/HAS_ONE_ASSIGNEE]][[#HAS_TWO_ASSIGNEES]][[ASSIGNEE_NAME_1]] and [[ASSIGNEE_NAME_2]] have[[/HAS_TWO_ASSIGNEES]][[#HAS_MULTI_ASSIGNEES]][[ASSIGNEE_NAME]] and others have[[/HAS_MULTI_ASSIGNEES]] an action that is overdue. Please review and follow up to ensure progress.";
"Remind_owner_member_not_join_workspace_subject" = "Reminder: [[#HAS_ONE_INVITEE]]Member has[[/HAS_ONE_INVITEE]][[#HAS_TWO_INVITEE]]Members have[[/HAS_TWO_INVITEE]][[#HAS_MULTI_INVITEE]]Members have[[/HAS_MULTI_INVITEE]] not joined";
"Remind_member_to_join_workspace_subject" = "Reminder: You're invited to join a workspace in [[#HAS_OrgConfig_BrandName]][[OrgConfig_BrandName]][[/HAS_OrgConfig_BrandName]][[#NO_OrgConfig_BrandName]][[GROUP_NAME]][[/NO_OrgConfig_BrandName]]";
"Remind_member_to_join_workspace" = "Just a quick reminder that [[BOARD_OWNER]] is waiting for you in the following workspace.";
"Remind_owner_members_not_join_workspace" = "Reminder: [[#HAS_ONE_INVITEE]][[INVITEE_NAME_1]] has[[/HAS_ONE_INVITEE]][[#HAS_TWO_INVITEE]][[INVITEE_NAME_1]], [[INVITEE_NAME_2]] have[[/HAS_TWO_INVITEE]][[#HAS_MULTI_INVITEE]][[INVITEE_NAME_1]], [[INVITEE_NAME_2]], and [[EXTRA_INVITEE_COUNT]] others have[[/HAS_MULTI_INVITEE]] not joined the workspace yet.";
"Meeting_updated" = "Meeting Updated";
"You_have_update_meeting" = "You have updated the meeting:";
"Inviter_from_brand_name_update_meeting" = "[[INVITER_NAME]] has updated the meeting:";
"Inviter_from_brand_name_update_meeting_for_pt" = "[[INVITER_NAME_UNENCODED]] has updated the meeting:";
"Please_use_calendar_file_to_update_calendar" = "Please use the attached calendar file (.ics) to update your calendar.";
"Previous_email_has_been_changed_to" = "An administrator at [[#HAS_OrgConfig_BrandName]][[OrgConfig_BrandName]][[/HAS_OrgConfig_BrandName]][[#NO_OrgConfig_BrandName]][[GROUP_NAME]][[/NO_OrgConfig_BrandName]] has changed the email address on your account to <span style="font-style:italic; color:#606369;">[[NEW_EMAIL]]</span>.";
"Previous_email_has_been_changed_to_for_pt" = "An administrator at [[#HAS_OrgConfig_BrandName]][[OrgConfig_BrandName_UNENCODED]][[/HAS_OrgConfig_BrandName]][[#NO_OrgConfig_BrandName]][[GROUP_NAME_UNENCODED]][[/NO_OrgConfig_BrandName]] has changed the email address on your account to <span style="font-style:italic; color:#606369;">[[NEW_EMAIL]]</span>.";
"Reset_your_password_title" = "Reset Your Password";
"Not_share_this_email" = "DO NOT SHARE THIS EMAIL";
"It_contains_secure_link_please_not_share_email" = "This contains a secure link or verification code. Please do not share this email, link, or code with others.";
"Open_Workspace" = "Open Workspace";
"Meeting_Agenda" = "Meeting Agenda:";
"Freemium_due_date" = "Due [[ACTION_DUE_DATE]]";
"Freemium_admin_require_reset_password" = "An administrator at [[#HAS_OrgConfig_BrandName]][[OrgConfig_BrandName]][[/HAS_OrgConfig_BrandName]][[#NO_OrgConfig_BrandName]][[GROUP_NAME]][[/NO_OrgConfig_BrandName]] is asking you to reset your password to ensure the security of your account.";
"Freemium_admin_require_reset_password_for_pt" = "An administrator at [[#HAS_OrgConfig_BrandName]][[OrgConfig_BrandName_UNENCODED]][[/HAS_OrgConfig_BrandName]][[#NO_OrgConfig_BrandName]][[GROUP_NAME_UNENCODED]][[/NO_OrgConfig_BrandName]] is asking you to reset your password to ensure the security of your account.";
"Inviter_invited_you_to_workspace_from_brand_name" = "[[INVITER_NAME_UNENCODED]] has invited you to a workspace for your project from [[#HAS_OrgConfig_BrandName]][[OrgConfig_BrandName_UNENCODED]][[/HAS_OrgConfig_BrandName]][[#NO_OrgConfig_BrandName]][[GROUP_NAME_UNENCODED]][[/NO_OrgConfig_BrandName]]";
"Meet_invite" = "New Meeting Invite: [[SESSION_TOPIC_UNENCODED]] @ [[SESSION_SST_MONTH_DAY_YEAR]] ([[SESSION_SST_TIME]] - [[SESSION_SET_TIME]] [[SESSION_TIMEZONE_ABBR]])";
"Inviter_invited_you_to_brand_name" = "[[INVITER_NAME_UNENCODED]] has invited you to [[#HAS_OrgConfig_BrandName]][[OrgConfig_BrandName_UNENCODED]][[/HAS_OrgConfig_BrandName]][[#NO_OrgConfig_BrandName]][[GROUP_NAME_UNENCODED]][[/NO_OrgConfig_BrandName]]";
"Meet_invitaion_to_participant_multi_date" = "Meeting Invite: [[SESSION_TOPIC_UNENCODED]] @ [[SESSION_SST_MONTH_DAY_YEAR]] [[SESSION_SST_TIME]]";
"Admin_at_brand_name_reset_account_password" = "Admin at [[#HAS_OrgConfig_BrandName]][[OrgConfig_BrandName_UNENCODED]][[/HAS_OrgConfig_BrandName]][[#NO_OrgConfig_BrandName]][[GROUP_NAME_UNENCODED]][[/NO_OrgConfig_BrandName]] has reset your account password";
"Admin_at_brand_name_updated_account_email" = "Admin at [[#HAS_OrgConfig_BrandName]][[OrgConfig_BrandName_UNENCODED]][[/HAS_OrgConfig_BrandName]][[#NO_OrgConfig_BrandName]][[GROUP_NAME_UNENCODED]][[/NO_OrgConfig_BrandName]] has updated your account email address";
"Your_account_password_was_updated" = "Your Account Password Has Been Updated";
"security" = "security";
"Meet_specific_time" = "[[SESSION_SST_YEAR_MONTH_DAY]] ([[SESSION_SST_TIME]] - [[SESSION_SET_TIME]] [[SESSION_TIMEZONE_ABBR]])";
"Every_day" = "Every day";
"Every_days" = "Every [[DAILY_SPAN]] days";
"Weekly_on_selected_date" = "Every week on [[SELECTED_DATE]]";
"Every_weeks_on_selected_date" = "Every [[WEEKLY_SPAN]] weeks on [[SELECTED_DATE]]";
"Every_month_on_selected_date" = "Every month on [[SELECTED_DATE]]";
"Repeat_end_date" = "End Date: [[END_DATE]]";
"Jumio" = "Jumio";
"Integration" = "Integration";
"Launch_Web_App" = "Web App";
"Moxo" = "Moxo";
"Freemium_your_payment_information_has_been_updated" = "Your payment information has been updated. Future payments will use the new information provided.";
"Your_plan_has_been_downgraded" = "Your Plan Has Been Downgraded";
"Your_subscription_plan_has_downgraded" = "Since we could not process your payment, your subscription plan has been downgraded.";
"Pls_update_your_payment_info_via" = "To upgrade your plan, please update your payment info via the admin portal on the web.";
"Your_plan_has_been_updated" = "Your Plan Has Been Updated";
"Your_plan_will_be_updated" = "Your Plan Will Be Updated";
"You_can_access_billing_and_plan_info_on_the_web" = "You can access your billing and plan information at any time via the admin portal on the web.";
"Received_request_to_change_plan_new_plan_will_begin" = "We have received your request to change your plan. The new plan will begin on [[RENEWED_TIME]].";
"You_can_access_your_billing_info_change_plan" = "You can access your billing information and change your plan at any time via the admin portal on the web.";
"Free_plan" = "Free Plan";
"Start_from" = "Start from:";
"Your_plan" = "Your plan:";
"This_action_awaits_your_review" = "This action awaits your review.";
"Click_below_review_the_details" = "Click below review the details and help it move forward.";
"This_action_requires_your_review_for_sms" = "[[[Sms_Prefix]]] An action requires your review. Click the link to review the details and help move it forward: https://[[OrgConfig_BrandDomain]]/[[#IS_UNIVERSAL]]ua?id=[[/IS_UNIVERSAL]][[#IS_NOT_UNIVERSAL]]wa?id=[[/IS_NOT_UNIVERSAL]][[SHORT_URL]]";
"Host_has_invited_you_to_offline_meet" = "[[[Sms_Prefix]]] [[HOST_NAME_UNENCODED]] from [[#HAS_OrgConfig_BrandName]][[OrgConfig_BrandName_UNENCODED]][[/HAS_OrgConfig_BrandName]][[#NO_OrgConfig_BrandName]][[GROUP_NAME_UNENCODED]][[/NO_OrgConfig_BrandName]] has invited you to a meeting (“[[SESSION_TOPIC_UNENCODED]]”).[[#HAS_LOCATION]] Location: [[SESSION_LOCATION_UNENCODED]][[/HAS_LOCATION]]";
"Workspace_Due_Today" = "Workspace Due Today";
"You_have_workspace_due_today" = "You have a workspace that is due today.";
"Workspace_Overdue" = "Workspace Overdue";
"You_have_workspace_overdue" = "You have a workspace that is overdue.";
"Workspace_Due_Soon" = "Workspace Due Soon";
"You_have_workspace_due_soon" = "You have a workspace that is due soon.";
"View_Workspace" = "View Workspace";
"Due_on_board_due_date" = "Due on [[BOARD_DUE_DATE]]";
"Verification_code" = "Verification code";
"Your_org_was_successfully_created" = "Your organization has been created successfully";
"Here_is_the_link_to_your_org" = "Here is the link to your organization:";
"Hi_to" = "Hi [[TO]],";
"User_invited_to_the_org" = "<strong>[[INVITER_NAME]]</strong> has invited you to join the [[GROUP_NAME]] organization.";
"User_invited_to_the_org_for_pt" = "[[INVITER_NAME_UNENCODED]] has invited you to join the [[GROUP_NAME_UNENCODED]] organization.";
"Streamline_your_business_with_portal" = "Streamline your business services with our interaction portal.";
"Create_account_online_to_start" = "Create your account online to get started.";
"Join_organization" = "Join organization";
"Join_now" = "Join now";
"New_action" = "New action";
"Workspace_due_today" = "Workspace due today";
"Action_due_today_in_lower" = "Action due today";
"Invited_to_join_org" = "You’re invited to join an organization";
"Review_and_act" = "Review & act";
"View_workspace" = "View workspace";
"Due_today" = "Due today";
"New_access_link" = "New access link";
"Open_workspace" = "Open workspace";
"Your_account_password_was_updated_in_lower" = "Your account password has been updated";
"Password_updated_in_lower" = "Password updated";
"You_secure_access_link_for_action" = "Your secure access link on [[GROUP_NAME_UNENCODED]]";
"Freemium_you_secure_access_link_for_binder" = "Your secure access link for "[[BOARD_NAME_UNENCODED]]" on [[GROUP_NAME_UNENCODED]]";
"New_link_access_to_action" = "Here is the new link to access your action.";
"In_conversation_name" = "In: [[BOARD_NAME]]";

<!-- BOF PUSH NOTIFICATION -->
"ACDEA" = "View";
"ACDEM" = "Live support session ended";
"ACDJA" = "View";
"ACDJM" = "You are connected with [[arg1]]";
"ACDPA" = "View";
"ACDPM" = "You have a live support request";
"ACDTA" = "View";
"ACDTM" = "Live Support";
"BAA" = "View";
"BAM" = "[[arg1]]: Voice Message";
"BCA" = "View";
"BCM" = "[[arg1]]: [[arg2]]";
"BDA" = "View";
"BDM" = "[[arg1]] deleted [[arg2]]";
"BEA" = "View";
"BEM" = "[[arg1]] shared an email";
"BFA" = "View";
"BFDA" = "View";
"BFDM" = "[[arg1]] deleted content from \"[[arg2]]\"";
"BFM" = "[[arg1]] shared a file";
"BIA" = "Join";
"BIM" = "[[arg1]] invited you to join";
"BJA" = "View";
"BJM" = "[[arg1]] joined \"[[arg2]]\"";
"BLA" = "View";
"BLM" = "[[arg1]] left \"[[arg2]]\"";
"BMA" = "View";
"BMM" = "[[arg1]] shared an audio";
"BNA" = "View";
"BNM" = "[[arg1]] shared a clip";
"BOA" = "View";
"BOM" = "[[arg1]] made you the owner of \"[[arg2]]\"";
"BPA" = "View";
"BPM" = "[[arg1]] shared an image";
"BVA" = "View";
"BVM" = "[[arg1]] shared a video";
"CDA" = "View";
"CDM" = "[[arg1]] deleted the chat with you";
"CIA" = "View";
"CIM" = "[[arg1]] invited you to chat";
"CJA" = "View";
"CJM" = "[[arg1]] joined the chat";
"CLA" = "View";
"CLM" = "[[arg1]] left the chat";
"FAA" = "View";
"FAM" = "[[arg1]] added an attachment: [[arg2]] in \"[[arg3]]\".";
"FDAA" = "View";
"FDDA" = "View";
"FPA" = "View";
"FPM" = "[[arg1]] created a clip in \"[[arg3]]\".";
"FRA" = "View";
"FRAA" = "View";
"FRAM" = "[[arg1]]: Voice Message";
"GIA" = "View";
"GMA" = "View";
"GMM" = "You have received a new message";
"LSA" = "View";
"LSM" = "[[arg1]] shared a location";
"MAA" = "View";
"MAM" = "[[arg1]] accepted: [[arg2]] @ [[arg3]]";
"MCA" = "View";
"MCM" = "Meet Canceled: [[arg2]]";
"MDA" = "View";
"MDM" = "[[arg1]] declined: [[arg2]] @ [[arg3]]";
"MHA" = "View";
"MHM" = "Meeting Invitation from [[arg1]]: [[arg2]] @ [[arg3]]";
"MIA" = "Join";
"MIM" = "[[arg1]]: Calling to meet. Please join.";
"MRA" = "View";
"MRM" = "Meet recording is ready: [[arg2]]";
"MUA" = "View";
"MUM" = "Meeting Updated: [[arg2]] @ [[arg3]]";
"MVA" = "Accept";
"MVM" = "Meeting Invitation from [[arg1]]: [[arg2]] @ [[arg3]]";
"PA" = "View";
"PAA" = "View";
"PAM" = "[[arg1]] annotated";
"PCA" = "View";
"PCM" = "[[arg1]]: [[arg2]]";
"PDA" = "View";
"PDM" = "[[arg1]] deleted content from \"[[arg2]]\"";
"PM" = "[[arg1]] added a file";
"PMA" = "View";
"PMM" = "Your participants are waiting in your personal meeting room.";
"PPA" = "View";
"PPM" = "[[arg1]]: [[arg2]]";
"SAA" = "View";
"SAM" = "E-Sign assigned to you: [[arg2]]";
"SCA" = "View";
"SCM" = "E-Sign completed: [[arg2]]";
"SDA" = "View";
"SDM" = "E-Sign canceled: [[arg2]]";
"SEA" = "View";
"SEM" = "[[arg1]] deleted a document: [[arg2]]";
"SFA" = "View";
"SFM" = "[[arg1]] created a document for signing: [[arg2]]";
"SGA" = "View";
"SGM" = "[[arg1]] signed a document";
"SHA" = "View";
"SHM" = "E-Sign assigned to you: [[arg2]]";
"SSA" = "View";
"SSM" = "[[arg1]] signed a document";
"TAA" = "View";
"TAM" = "[[arg1]] accepted your invitation to connect";
"TASA" = "View";
"TASM" = "To-Do assigned to you: [[arg2]]";
"TATA" = "View";
"TATM" = "[[arg1]] added an attachment: [[arg4]]";
"TCMA" = "View";
"TCMM" = "[[arg1]] added a comment to a to-do item in \"[[arg3]]\".";
"TCPA" = "View";
"TCPM" = "[[arg1]] completed a To-Do: [[arg2]]";
"TCRA" = "View";
"TCRM" = "[[arg1]] added a To-Do: [[arg2]]";
"TDAA" = "View";
"TDAM" = "To-Do due today: [[arg2]]";
"TDDA" = "View";
"TDDM" = "[[arg1]] set a due date for a to-do item in \"[[arg3]]\".";
"TDLA" = "View";
"TDLM" = "[[arg1]] deleted a to-do item in \"[[arg3]]\".";
"TIA" = "View";
"TIM" = "[[arg1]] wants to connect with you.";
"TRAA" = "View";
"TRAM" = "Action required for transaction";
"TREA" = "View";
"TREM" = "Transaction expired";
"TRMA" = "View";
"TRMM" = "To-Do reminder: [[arg2]]";
"TRPA" = "View";
"TRPM" = "[[arg1]] reopened a To-Do: [[arg2]]";
"TRSA" = "View";
"TRUA" = "View";
"TRUM" = "Transaction updated: [[arg4]]";
"TUPA" = "View";
"TUPM" = "[[arg1]] updated a to-do item in \"[[arg3]]\".";
"MRMMTITLE" = "Reminder: ";
"MRMM" = "[[arg3]]";
"SRCM" = "Request has been completed";
"BCMP" = "You have received a new message";
"MIMP" = "You are invited to a meeting";
"PMMP" = "You are invited to a meeting";
"PCMP" = "You have received a new message";
"BFMP" = "You have a new document";
"BMMP" = "You have a new document";
"LSMP" = "You have a new document";
"PMP"  = "You have a new document";
"BEMP" = "You have a new document";
"PAMP" = "You have a document annotation";
"BVMP" = "You have received a video clip";
"BNMP" = "You have received a video clip";
"BPMP" = "You have received an image";
"MAMP" = "Your meeting has been accepted";
"MCMP" = "Your meeting has been cancelled";
"MHMP" = "You have a new meet invite";
"MVMP" = "You have a new meet invite";
"MDMP" = "Your meeting has been declined";
"MUMP" = "Your meeting has been updated";
"TCRMP" = "You have a to-do update";
"TASMP" = "You have a to-do assigned";
"TATMP" = "You have a to-do update";
"TCPMP" = "You have a to-do update";
"TDAMP" = "You have a to-do due";
"TRMMP" = "You have a to-do reminder";
"TRPMP" = "You have a to-do update";
"TUPMP" = "You have a to-do update";
"TRSM" = "[[arg1]] [[arg4]]";
"MRMMP" = "You have a meeting reminder";
"UCASM" = "[[arg2]] has been assigned to you.";
"UCAIM" = "[[arg1]] accepted your invitation.";
"GIM" = "You have been added to [[arg2]].";
"GAMM" = "You are now a manager of [[arg2]].";
"BJRM" = "[[arg1]] requested to join the workspace.";
"TRAM_APPROVAL" = "Approval assigned to you: [[arg2]]";
"TRAM_ACKNOWLEDGE" = "Acknowledgement assigned to you: [[arg2]]";
"TRAM_FILE_REQUEST" = "File Request assigned to you: [[arg2]]";

"TRSM_APPROVAL_APPROVED" = "[[arg1]] approved an Approval: [[arg2]]";
"TRSM_APPROVAL_DECLINED" = "[[arg1]] declined an Approval: [[arg2]]";
"TRSM_ACKNOWLEDGE" = "[[arg1]] acknowledged an Acknowledgement: [[arg2]]";
"TRUM_APPROVAL_COMPLETED" = "Approval completed: [[arg2]]";
"TRUM_APPROVAL_CANCELED" = "Approval canceled: [[arg2]]";
"TRUM_ACKNOWLEDGE_COMPLETED" = "Acknowledgement completed: [[arg2]]";
"TRUM_FILE_REQUEST_COMPLETED" = "File Request completed: [[arg2]]";
"TRROM_FILE_REQUEST" = "[[arg1]] reopened a File Request: [[arg2]]";
"TREM_DUE" = "Due today: [[arg2]]";
"SDAM" = "E-Sign due today: [[arg2]]";
"TRAM_FILE_REQUEST_REVIEW" = "File Request ready for review: [[arg2]]";
"SRM" = "[[arg1]] added an attachment: [[arg4]]";
"TRRM" = "[[arg1]] added an attachment: [[arg4]]";
"TRAM_FORM_REQUEST" = "Form assigned to you: [[arg2]]";
"TRAM_PDF_FORM_REQUEST" = "PDF Form assigned to you: [[arg2]]";
"TRSM_FORM_REQUEST" = "[[arg1]] submitted a Form: [[arg2]]";
"TRSM_PDF_FORM_REQUEST" = "[[arg1]] submitted a PDF Form: [[arg2]]";
"TRUM_FORM_REQUEST_COMPLETED" = "Form completed: [[arg2]]";
"TRUM_PDF_FORM_REQUEST_COMPLETED" = "PDF Form completed: [[arg2]]";
"TREM_APPROVAL" = "Approval due today: [[arg2]]";
"TREM_ACKNOWLEDGE" = "Acknowledgement due today: [[arg2]]";
"TREM_FILE_REQUEST" = "File Request due today: [[arg2]]";
"TREM_FORM_REQUEST" = "Form due today: [[arg2]]";
"TREM_PDF_FORM_REQUEST" = "PDF Form due today: [[arg2]]";
"TRAM_DOCUSIGN" = "DocuSign assigned to you: [[arg2]]";
"TRUM_DOCUSIGN_COMPLETED" = "DocuSign completed: [[arg2]]";
"TREM_DOCUSIGN" = "DocuSign due today: [[arg2]]";
"GMGM" = "You are now a manager of [[arg2]]";
"TRAM_JUMIO" = "Identity Verification assigned to you: [[arg2]]";
"TRUM_JUMIO" = "Identity Verification completed: [[arg2]]";
"TREM_JUMIO" = "Identity Verification due today: [[arg2]]";
"TRAM_INTEGRATION" = "Action assigned to you: [[arg2]]";
"TRUM_INTEGRATION_COMPLETED" = "Action completed: [[arg2]]";
"TREM_INTEGRATION" = "Action due today: [[arg2]]";
"TRBDM_INTEGRATION" = "Action due soon: [[arg2]]";
"TRADM_INTEGRATION" = "Action overdue: [[arg2]]";
"TRAM_MEET_REQUEST" = "Time Booking assigned to you: [[arg2]]";
"TRSM_MEET_REQUEST" = "[[arg1]] confirmed a time booking: [[arg2]]";
"TRUM_MEET_REQUEST_COMPLETED" = "Time Booking completed: [[arg2]]";
"TREM_MEET_REQUEST" = "Time Booking due today: [[arg2]]";
"MBM" = "[[arg1]] booked a meeting: [[arg2]]";
"TRAM_TODO" = "To-Do assigned to you: [[arg2]]";
"TREM_TODO" = "To-Do due today: [[arg2]]";
"TRUM_TODO_COMPLETED" = "To-Do completed: [[arg2]]";
"TRRMM_TODO" = "To-Do reminder: [[arg2]]";
"BIRMM" = "You are invited to the workspace [[arg2]]";
"BORMM" = "Some members have not joined the workspace [[arg2]]";
"TBDM" = "To-Do due soon: [[arg2]]";
"TADM" = "To-Do overdue: [[arg2]]";
"SBDM" = "E-Sign due soon: [[arg2]]";
"SADM" = "E-Sign overdue: [[arg2]]";
"TRBDM_TODO" = "To-Do due soon: [[arg2]]";
"TRADM_TODO" = "To-Do overdue: [[arg2]]";
"TRBDM_APPROVAL" = "Approval due soon: [[arg2]]";
"TRADM_APPROVAL" = "Approval overdue: [[arg2]]";
"TRBDM_ACKNOWLEDGE" = "Acknowledgement due soon: [[arg2]]";
"TRADM_ACKNOWLEDGE" = "Acknowledgement overdue: [[arg2]]";
"TRBDM_FILE_REQUEST" = "File Request due soon: [[arg2]]";
"TRADM_FILE_REQUEST" = "File Request overdue: [[arg2]]";
"TRBDM_FORM_REQUEST" = "Form due soon: [[arg2]]";
"TRBDM_PDF_FORM_REQUEST" = "PDF Form due soon: [[arg2]]";
"TRADM_FORM_REQUEST" = "Form overdue: [[arg2]]";
"TRADM_PDF_FORM_REQUEST" = "PDF Form overdue: [[arg2]]";
"TRBDM_DOCUSIGN" = "DocuSign due soon: [[arg2]]";
"TRADM_DOCUSIGN" = "DocuSign overdue: [[arg2]]";
"TRBDM_LAUNCH_WEB_APP" = "Web App due soon: [[arg2]]";
"TRADM_LAUNCH_WEB_APP" = "Web App overdue: [[arg2]]";
"TRBDM_JUMIO" = "Identity Verification due soon: [[arg2]]";
"TRADM_JUMIO" = "Identity Verification overdue: [[arg2]]";
"TRBDM_MEET_REQUEST" = "Time Booking due soon: [[arg2]]";
"TRADM_MEET_REQUEST" = "Time Booking overdue: [[arg2]]";
"TRUM_JUMIO_COMPLETED" = "Identity Verification completed: [[arg2]]";
"SPRM" = "E-Sign awaits your preparation: [[arg2]]";
"TRPRM_TODO" = "To-Do awaits your preparation: [[arg2]]";
"TRPRM_FILE_REQUEST" = "File Request awaits your preparation: [[arg2]]";
"TRPRM_APPROVAL" = "Approval awaits your preparation: [[arg2]]";
"TRPRM_ACKNOWLEDGE" = "Acknowledgement awaits your preparation: [[arg2]]";
"TRPRM_FORM_REQUEST" = "Form awaits your preparation: [[arg2]]";
"TRPRM_PDF_FORM_REQUEST" = "PDF Form awaits your preparation: [[arg2]]";
"TRPRM_LAUNCH_WEB_APP" = "Web App awaits your preparation: [[arg2]]";
"TRPRM_MEET_REQUEST" = "Time Booking awaits your preparation: [[arg2]]";
"TRBDM_DECISION" = "Decision due soon: [[arg2]]";
"TRADM_DECISION" = "Decision overdue: [[arg2]]";
"TREM_DECISION" = "Decision due today: [[arg2]]";
"TRAM_DECISION" = "Decision assigned to you: [[arg2]]";
"FDRAM" = "You have received some reaction(s) to your message: [[arg2]]";
"TRUM_AWAIT_COMPLETED" = "Wait action completed: [[arg2]]";
"TREM_AWAIT" = "Wait action due today: [[arg2]]";
"TRBDM_AWAIT" = "Wait action due soon: [[arg2]]";
"TRADM_AWAIT" = "Wait action overdue: [[arg2]]";
"SMCM" = "[[arg1]] marked an E-Sign as completed: [[arg2]]";
"TRMCM" = "[[arg1]] marked a Transaction as completed: [[arg2]]";
"TRMCM_APPROVAL" = "[[arg1]] marked an Approval as completed: [[arg2]]";
"TRMCM_ACKNOWLEDGE" = "[[arg1]] marked an Acknowledgement as completed: [[arg2]]";
"TRMCM_FILE_REQUEST" = "[[arg1]] marked a File Request as completed: [[arg2]]";
"TRMCM_MEET_REQUEST" = "[[arg1]] marked a Time Booking as completed: [[arg2]]";
"TRMCM_FORM_REQUEST" = "[[arg1]] marked a Form as completed: [[arg2]]";
"TRMCM_PDF_FORM_REQUEST" = "[[arg1]] marked a PDF Form as completed: [[arg2]]";
"TRMCM_DOCUSIGN" = "[[arg1]] marked a DocuSign as completed: [[arg2]]";
"TRMCM_JUMIO" = "[[arg1]] marked an Identity Verification as completed: [[arg2]]";
"TRMCM_INTEGRATION" = "[[arg1]] marked an action as completed: [[arg2]]";
"TRMCM_LAUNCH_WEB_APP" = "[[arg1]] marked a Web App as completed: [[arg2]]";
"TRMCM_TODO" = "[[arg1]] marked a To-Do as completed: [[arg2]]";
"TRMCM_DECISION" = "[[arg1]] marked a Decision as completed: [[arg2]]";
"TRMCM_AWAIT" = "[[arg1]] marked a Wait action as completed: [[arg2]]";
"TRAM_APPROVAL_REVIEW" = "Approval awaiting your review: [[arg2]]";
"BBDM" = "Workspace due soon: [[arg3]]";
"BDAM" = "Workspace due today: [[arg3]]";
"BADM" = "Workspace overdue: [[arg3]]";
"SRACM" = "New Request Created";
<!-- EOF PUSH NOTIFICATION -->