//This match to Zoom meeting parameter
export enum  MeetingAction {
  START_INSTANT_MEETING = 1, 
  SCHEDULE_MEETING      = 2
}
  
export enum AutoRecording {
  none = 'none',
  local = 'local',
  cloud = 'cloud'
}

export enum IntegrationServerResponseCode {
  OK = 200,
  NOT_FOUND = 404,
  CONFLICT = 409,
  COMMON_ERROR = 500,
}

export interface VendorMeetingParams {
  topic?: string;
  agenda?: string;
  start_time?: number;
  end_time?: number;
  duration?: number;
  timezone?: string;
  host_video?: boolean;
  participant_video?: boolean;
  mute_upon_entry?: boolean;
  auto_recording?: AutoRecording;
  type?: MeetingAction;
}

interface AssigneeRole {
  role: {
      id: string;
  };
}

interface AssigneeUser {
  user: {
      id: string;
      name?: string;
      email?: string;
      phone_number?: string;
      first_name?: string;
      last_name?: string;
  };
}

interface BuildFunctionInput {
  [anyCustomFormProp: string]: any;
}

interface BuildFunctionContext {
  action_key?: string;
  title?: string;
  description?: string;
  assignees: (AssigneeRole | AssigneeUser)[];
}

export interface BuildFunctionModel {
  app_id: string;
  auth_id: string;
  
  input?: BuildFunctionInput;
  context?: BuildFunctionContext;
}


interface ActionFunctionInput {
  [anyCustomFormProp: string]: any;
}

export interface ActionFunctionModel {
  board_id: string;
  transaction_sequence: number;
  step_sequence: number;
  button_id: string;
  input?: ActionFunctionInput;
  // assignees?: AssigneeUser[];
  
  // app_id: string;
  // auth_id: string;
  // view_token: string;
  // access_token: string;
  // creator_user_id: string;
  // assignees: AssigneeUser[];
}

interface CustomData {
  action_key?: string;
  action_title?: string;
  app_id?: string;
  app_name?: string;
  auth_id?: string;
  creator_user_id?: string;
}

export interface IActionFunctionModel extends ActionFunctionModel {
  customData?: CustomData;
}

export interface HeaderOption {
  auth_id?: string;
  access_token?: string;
  creator_user_id?: string;
  board_id?: string;
  board_view_token?: string;
}

export interface ActionEvent {
  type: string;
  options: {
    url: string;
  };
}

export interface ActionFunctionViewModel {
  code?: string;
  data: {
    status?: string;
    events: ActionEvent[];
  };
}

// enum I18n {
//   en = 'en'
// }

export interface CategoryModel {
  key?: string;
  title?: {
    en?: string;
  };
}

export enum IntegrationAppType {
  APP_TYPE_ACTION = 'APP_TYPE_ACTION',
  APP_TYPE_AUTOMATION = 'APP_TYPE_AUTOMATION',
  APP_TYPE_AWAIT = 'APP_TYPE_AWAIT',
  APP_TYPE_TRIGGER = 'APP_TYPE_TRIGGER',
}

interface AppAction {
  key: string;
  title: string;
}

export interface IntegrationApp {
  app_id?: string;
  category?: string;
  creator?: {
    user?: {
      id?: string;
    };
  };
  description?: string;
  name?: string;
  type?: IntegrationAppType;
  actions?: AppAction[];
  created_time?: string;
  updated_time?: string;
}

export interface AppConnection {
  app_id?: string;
  auth_id?: string;
  display_id?: string;
  created_time?: string;
  last_access_time?: string;
  last_access_failed?: boolean;
}


// ======oauth
//This will include all basic data structures
export enum OAuthType {
  integration = 'integration'
}

export interface OAuthAccount {
  app_id: string;
  auth_id: string; 
  display_id: string; 
}

export interface SendWebhookAmData {
  webhook_url: string; // required
  headers?: string[];  // optional
  payload?: string;    // json string. This will be copied to the payload of the output message to Webhook URL. 
  enableBasicAuth?: boolean; //true:the UI switch is on, false:
  auth?: {
    user_name: string;
    password: string;
    password_plain: string;
  }
}

export interface WaitAppExtURLPayload extends SendWebhookAmData {
  method: string;
  enableJWT: boolean;
  jwt: {
    jwt_key: string;
    jwt_secret: string;
  }
  custom_request_body: Record<string, string>;
}
