import { MxCallback } from 'isdk/src/api/defines'
import {
  UserId,
  FormElementId, FormElementTransformOption
} from '@model/form/defines/shared'
import {
  ElementFilledStatusViewModel,
  ElementFilledValueViewModel
} from '@model/form/defines/state'
import { FormViewModel } from '@model/form/defines/formViewModel'

import { CBoardUser } from '@controller/defines/CBoardUser'
import { FormElementAnyInputModel } from '@model/form/defines/allType'
import {Defines} from "isdk";
import {FileUploadValue} from "@model/form/defines/FormFileUpload";

export interface ISubscription {
  unsubscribe(): void;
}

export interface StepAssigneeBaseInfo {
  isTeamUser: boolean;
  stepSequence: number;
  teamId: string;
}

export interface FormViewModelInfos {
  isFirstTime?: boolean;
  isFormDeleted?: boolean;
  ownerIds: UserId[];
  formViewModel: FormViewModel;
  members: Map<UserId, CBoardUser>;
  myFilledValues?: Map<FormElementId, ElementFilledValueViewModel>;
  filledStatus?: Map<UserId, ElementFilledStatusViewModel>;
  filledValues?: Map<FormElementId, ElementFilledValueViewModel>;
  stepOfCurrentUser: StepAssigneeBaseInfo;
  uploadedFiles: Map<FormElementId, FileUploadValue[]>;
  urlCallbackInfo: {
    url: string;
    payload: object;
  };
}

export interface FormSubmitParams {
  boardId: string;
  transactionSequence: number;
  stepSequence: number;
  searchContent: string;
  actionId?: string;
}

export type TransactionInfoCallback = MxCallback<Partial<FormViewModelInfos>>

export interface IFormController {
  subscribeForm(
    boardId: string,
    transactionSequence: number,
    updateCallback: TransactionInfoCallback
  ): Promise<ISubscription>;

  saveUserFilledField(
    stepSequence: number,
    updatedFields: FormElementAnyInputModel[],
    myFilledElements: Map<FormElementId, ElementFilledValueViewModel>,
    option: FormElementTransformOption,
    status?: Partial<ElementFilledStatusViewModel>
  ): Promise<void>;

  updateUserFillStatus(
    boardId: string,
    transactionSequence: number,
    fieldStatus: ElementFilledStatusViewModel
  ): Promise<void>;

  submitForm(model: FormViewModel, stepSequence: number): Promise<Defines.Board>;

  upgradeAutoSavedData(
    boardId: string,
    transactionSequence: number,
    fields: ElementFilledValueViewModel[],
    formViewModel: FormViewModel
  ): Promise<void>;
}
