import {
  FormSubmitParams,
  FormViewModelInfos,
  IFormController,
  ISubscription,
  TransactionInfoCallback
} from '@newController/defines/formControllerNew'
import { Defines, MxISDK } from 'isdk'
import { BoardFormatter } from '@controller/utils/board'
import { BoardTransaction, MxCallback } from 'isdk/src/api/defines'
import { CBoardUser } from '@controller/defines/CBoardUser'
import { getCacheOrInstantBoard, readBoardBasicInfo } from '@newController/utils'
import { IBoardAssigneeViewModel } from '@model/board/boardViewModel'
import {
  ElementFilledFieldValueModel,
  ElementFilledStatusViewModel,
  ElementFilledValueViewModel,
  FormUserStateType
} from '@model/form/defines/state'
import {
  FormElementId, FormElementTransformOption,
  FormElementType,
  UserId
} from '@model//form/defines/shared'
import get from 'lodash/get'
import {
  calcUploadedFile,
  transactionToFormViewModel,
  transformFillFormModels
} from '@model/form/transform/form'
import {
  elementFilledStateViewModelToString,
  elementStateViewModelToString, getFilledContentString
} from '@model/form/transform/fillStatusAndData'
import { getAutoSavedDataFromCustomData } from '@model/form/transform/autosave'
import { FormViewModel } from '@model/form/defines/formViewModel'
import { TransactionController } from '@newController/board/transactionControllerImpl'
import moment from 'moment-timezone'
import { FormElementAnyInputModel } from '@model/form/defines/allType'
import { FormElementUserNameModel } from '@model/form/defines/FormUserName'
import { FormElementPhoneNumberModel, PhoneNumberValue } from '@model/form/defines/FormPhoneNumber'
import { isMultipleAssignee } from '@model/form/common/utils'
// @ts-ignore
import * as libphonenumber from '@vendor/libphonenumber-js/bundle/libphonenumber-max'
import { FormElementEmailAddressModel } from '@model/form/defines/FormEmailAddress'
import FormFactoryManager from '@model/form/factory'
import { CCustomError } from '@controller/common/CCustomError'
import {getUserTimezone} from '@model/form/factory/utils';
import { DateValue, FormElementDateModel } from '@model/form/defines/FormDate'
import {PickedFile, PluginType} from "@views/common/components/FilePicker";
import {IAttachmentViewModel} from "@model/baseObjects/defines/fileViewModel";
import {FileUploadValue} from "@model/form/defines/FormFileUpload";
import cloneDeep from "lodash/cloneDeep";


let formController: FormControllerImpl

class FormControllerImpl implements IFormController {
  static getInstance (): FormControllerImpl {
    if (!formController) {
      formController = new FormControllerImpl()
    }
    return formController
  }

  saveUserFilledField (
    stepSequence: number,
    updatedFields: FormElementAnyInputModel[],
    myFilledElements: Map<FormElementId, ElementFilledValueViewModel>,
    options: FormElementTransformOption,
    status?: Partial<ElementFilledStatusViewModel>
  ): Promise<void> {
    const mxBoard = MxISDK.getCurrentUser().getCacheBoard(options.boardId)
    const contents = []
    updatedFields.forEach(field => {
      const filledValue: Partial<ElementFilledFieldValueModel> = myFilledElements.get(field.id)
      contents.push({
        sequence: filledValue?.contentSequence,
        string_value: getFilledContentString(field, options)
      })
    })

    if (status) {
      contents.push({
        sequence: status.contentSequence,
        string_value: elementStateViewModelToString(status)
      })
    }

    return mxBoard
      .submitTransactionStep({
        sequence: options.transactionSequence,
        steps: [
          {
            sequence: stepSequence,
            contents
          }
        ]
      })
      .then(() => Promise.resolve())
  }

  getUserTimeZone (): string {
    return MxISDK.getCurrentUser().basicInfo.timezone || moment.tz.guess()
  }

  async subscribeForm (
    boardId: string,
    transactionSequence: number,
    updateCallback: TransactionInfoCallback
  ): Promise<ISubscription> {
    const realBoardInfo = await this.readWorkspaceBasicInfo(boardId)
    let pdfFile
    let viewToken = ''
    if(MxISDK.isAnonymousContext()) {
      const mxBoard = MxISDK.getCurrentUser().getCacheBoard(boardId)
      viewToken = mxBoard.option.viewToken

    }
    const [board, sub] = await this.subscribeTransaction(boardId, transactionSequence, (board) => {
      const transaction = get(board, 'transactions[0]')
      if (!transaction) {
        return
      }
      if(transaction.is_deleted){
        updateCallback({
          isFormDeleted:true
        })
      }
      const transformOption = {
        boardId,
        transactionSequence,
        // if user has submitted form , we not need apply the default value
        isCompleted: transaction.status == Defines.TransactionStatus.TRANSACTION_STATUS_COMPLETED,
        pdfFile,
        viewToken
      }
      let uploadedFiles =new Map()
      if (transaction) {

        const formViewModel = transactionToFormViewModel(
          transaction,
          boardId,
          realBoardInfo.members,
          transformOption
        )
        uploadedFiles = calcUploadedFile(transaction, board, formViewModel)
        formViewModel.ownerIds = realBoardInfo.ownerIds
        updateCallback({
          ...realBoardInfo,
          formViewModel,
          uploadedFiles,
          ...transformFillFormModels(boardId, transaction, realBoardInfo.members, formViewModel)
        })
      }
    })
    const transaction = board.transactions.find(
      (transaction) => transaction.sequence === transactionSequence
    )
    const transformOption = {
      boardId,
      transactionSequence,
      userTimezone: this.getUserTimeZone(),
      // if user has submitted form , we not need apply the default value
      applyDefaultValue:
        transaction.status !== Defines.TransactionStatus.TRANSACTION_STATUS_COMPLETED,
      viewToken
    }

    const formViewModel = transactionToFormViewModel(
      transaction,
      boardId,
      realBoardInfo.members,
      transformOption
    )
    const autoSavedData = getAutoSavedDataFromCustomData(
      transaction,
      formViewModel.assignees.map((u) => u.id)
    )
    if (autoSavedData.length) {
      await this.upgradeAutoSavedData(boardId, transactionSequence, autoSavedData, formViewModel)
    }
    const formInfos = transformFillFormModels(
      boardId,
      transaction,
      realBoardInfo.members,
      formViewModel
    )

    if (formViewModel.hasChangeAfterLastView) {
      await this.updateViewTime(
        boardId,
        transactionSequence,
        formInfos.stepOfCurrentUser.stepSequence
      )
      formViewModel.hasChangeAfterLastView = false
    }
    formViewModel.ownerIds = realBoardInfo.ownerIds
    const uploadedFiles = calcUploadedFile(transaction, board, formViewModel)

    updateCallback({
      isFirstTime: true,
      ...realBoardInfo,
      formViewModel,
      ...formInfos,
      uploadedFiles
    })
    return sub
  }

  readWorkspaceBasicInfo (
    boardId: string
  ): Promise<{ ownerIds: UserId[]; members: Map<UserId, CBoardUser> }> {
    return new Promise((resolve, reject) => {
      const ownerIds = []
      const members = new Map<string, IBoardAssigneeViewModel>()
      readBoardBasicInfo(boardId)
        .then((board) => {
          const creator = BoardFormatter.getActionCreator(board)
          if (creator.id) {
            ownerIds.push(creator.id)
          }
          board.users
            .filter((bu) => !bu.is_deleted)
            .forEach((bUser) => {
              const user = BoardFormatter.transformBoardUser(
                bUser,
                boardId,
                board.is_relation,
                board.is_inbox,
                BoardFormatter.getIsExternalConversation(board)
              )
              members.set(user.id, user)
            })
          ;(board.teams || [])
            .filter((team) => !team.is_deleted)
            .forEach((team) => {
              const user = BoardFormatter.getTeamAsUserFromBoard(team.group, board)
              members.set(user.id, user)
            })
          resolve({
            ownerIds,
            members
          })
        })
        .catch(reject)
    })
  }

  updateViewTime (boardId: string, transactionSequence: number, stepSequence: number) {
    const mxBoard = MxISDK.getCurrentUser().getCacheBoard(boardId)
    return mxBoard.updateTransactionViewTime(transactionSequence, stepSequence)
  }

  async subscribeTransaction (
    boardId: string,
    transactionSequence: number,
    updateCallback: MxCallback<Defines.Board>
  ): Promise<[Defines.Board, ISubscription]> {
    const mxBoard = MxISDK.getCurrentUser().getCacheBoard(boardId)
    const baseObject = {
      type: Defines.MxBaseObjectType.TRANSACTION,
      spath: `transactions[sequence=${transactionSequence}]`,
      sequence: transactionSequence,
      parentSequence: 0
    }
    const board = await mxBoard.readThread(baseObject)
    const sub = mxBoard.subscribeThread(baseObject, updateCallback)
    return Promise.resolve([board, sub])
  }

  async readFormInfo (boardId: string, transactionSequence: number): Promise<FormViewModelInfos> {
    const realBoardInfo = await this.readWorkspaceBasicInfo(boardId)

    const mxBoard = MxISDK.getCurrentUser().getCacheBoard(boardId)
    const baseObject = {
      type: Defines.MxBaseObjectType.TRANSACTION,
      spath: `transactions[sequence=${transactionSequence}]`,
      sequence: transactionSequence,
      parentSequence: 0
    }

    const board = await mxBoard.readThread(baseObject)
    const transaction = board.transactions.find(
      (transaction) => transaction.sequence === transactionSequence
    )
    // const pdfFile = getFormPDFFile(transaction, board)
    const transformOption = {
      boardId,
      transactionSequence,
      isCompleted: transaction.status === Defines.TransactionStatus.TRANSACTION_STATUS_COMPLETED
    }

    const formViewModel = transactionToFormViewModel(
      transaction,
      boardId,
      realBoardInfo.members,
      transformOption
    )
    //todo: handle uploaded files

    const uploadedFiles = calcUploadedFile(transaction, board, formViewModel)

    const formInfos = transformFillFormModels(
      boardId,
      transaction,
      realBoardInfo.members,
      formViewModel
    )

    return {
      ...realBoardInfo,
      ...formInfos,
      formViewModel,
      uploadedFiles
    } as FormViewModelInfos
  }

  updateUserFillStatus (
    boardId: string,
    transactionSequence: number,
    model: Partial<ElementFilledStatusViewModel>
  ): Promise<void> {
    const mxBoard = getCacheOrInstantBoard(boardId)

    model.type = FormUserStateType.Status

    return mxBoard
      .submitTransactionStep({
        sequence: transactionSequence,
        steps: [
          {
            sequence: model.stepSequence,
            contents: [
              {
                sequence: model.contentSequence,
                string_value: elementStateViewModelToString(model)
              }
            ]
          }
        ]
      })
      .then(() => Promise.resolve())
  }
  async submitForm (model: FormViewModel, stepSequence: number): Promise<Defines.Board>{
    const mxBoard = MxISDK.getCurrentUser().getInstantBoard(model.boardId)
    mxBoard.submitTransactionStep({
      sequence: model.transactionSequence,
      content: FormFactoryManager.getSearchContent(model),
      steps: [
        {
          sequence: stepSequence
        }
      ]
    })

    const requests = []
    FormFactoryManager.process(model, (element, factory) => {
      if (element.type === FormElementType.FileUpload) {
        const modelValue = element.value as FileUploadValue[]
        if(!modelValue){return}
        modelValue.forEach(item => {
          if(!item.url) {
            requests.push(new Promise(async (resolve, reject) => {
              const link = await this.createDownloadSortLink(model.boardId, item.resSeq)
              item.url = link
              resolve(null)
            }))
          }
        })
      }
    })

    await Promise.all(requests)

    const userTimezone = this.getUserTimeZone()
    const transaction: BoardTransaction = {
      sequence: model.transactionSequence,
      card: JSON.stringify(FormFactoryManager.toServerData(model, { userTimezone })),
      // content: searchContent,
      steps: [
        {
          sequence: stepSequence,
          status: Defines.TransactionStepStatus.STEP_STATUS_COMPLETED,
          action_logs: [
            {
              click_btn_id: model.actionId
            }
          ]
        }
      ]
    }
    return mxBoard.submitTransactionStep(transaction)
  }
  createFileViewToken(boardId: string,  referenceSequence: number):Promise<string> {
    return new Promise((resolve, reject) => {
      const mxBoard = getCacheOrInstantBoard(boardId)
      mxBoard.createResourceViewToken(referenceSequence).then(board =>{
        const token = get(board, 'view_tokens[0].token');
        resolve(token)
      }).catch((err) => {
        reject(err)
      })
    })
  }
  async createDownloadSortLink(boardId: string, resourceSequence: number):Promise<string> {
    const token = await this.createFileViewToken(boardId, resourceSequence)
    let domain = location.origin
    if(domain.indexOf('https://localhost') > -1){
      domain = `https://${process.env.proxyUrl}`
    }
    const url =  `${domain}/board/${boardId}/${resourceSequence}?t=${token}`
    const mxBoard = getCacheOrInstantBoard(boardId)
    const viewToken = mxBoard.option?.viewToken
    const sortLink = await MxISDK.createShortURL(url,{boardViewToken: viewToken})
    return `${domain}${sortLink}`
  }
  upgradeAutoSavedData (
    boardId: string,
    transactionSequence: number,
    fields: ElementFilledValueViewModel[],
    formViewModel: FormViewModel
  ) {
    const mxBoard = MxISDK.getCurrentUser().getCacheBoard(boardId)
    const stepSequence = fields[0].stepSequence
    const elementsMap = FormFactoryManager.getElements(formViewModel)
    const contents = fields
      .map((field) => {
        const model = elementsMap.get(field.elementId)
        if (!model) {
          return null
        }
        return {
          string_value: elementFilledStateViewModelToString(
            field,
            model as FormElementAnyInputModel
          )
        }
      })
      .filter((item) => item !== null)

    return mxBoard
      .submitTransactionStep({
        sequence: transactionSequence,
        steps: [
          {
            sequence: stepSequence,
            custom_data: '',
            contents
          }
        ]
      })
      .then(() => Promise.resolve())
  }

  /**
   * Handle elements with auto-fill enabled
   * we need ignore when user has filled
   * @param boardId
   * @param transactionSequence
   * @param formViewModel
   * @param stepOfCurrentUser
   * @param myFilledValues
   */
  autoFillForm ({
                  boardId,
                  transactionSequence,
                  formViewModel,
                  stepSequence,
                  myFilledValues
                }: {
    boardId: string;
    transactionSequence: number;
    formViewModel: FormViewModel;
    stepSequence: number;
    myFilledValues: Map<FormElementId, ElementFilledValueViewModel>;
  }): Promise<void> {
    const queue = []
    const multipleAssignee = isMultipleAssignee(formViewModel)
    if (multipleAssignee) {
      return Promise.resolve()
    }
    const option = {
      boardId,transactionSequence
    }
    const currentUser = MxISDK.getCurrentUser().basicInfo
    const updatedElements = []
    FormFactoryManager.process(formViewModel, (element, factory) =>{
      const fillModel = myFilledValues.get(element.id)
      // For cases marked as read-only, re-fill the values each time the form is accessed.
      if (fillModel && !(element as FormElementAnyInputModel).readonly) {
        return
      }
      if (element.type === FormElementType.UserName) {
        const elementModel = element as FormElementUserNameModel
        if (!elementModel.enableAutoFill) {
          return
        }
        factory.setValue(elementModel, {
          ...elementModel.value,
          firstName : currentUser.first_name,
          lastName : currentUser.last_name
        }, option)
        updatedElements.push(elementModel)
      } else if (element.type === FormElementType.PhoneNumber) {
        const elementModel = element as FormElementPhoneNumberModel
        if (!elementModel.enableAutoFill) {
          return
        }
        if (currentUser.phone_number) {
          const model = libphonenumber.parsePhoneNumberFromString(currentUser.phone_number)
          if (model && model.isValid()) {
            const filledValue = {
              phoneNumber: currentUser.phone_number.replace(`+${model.countryCallingCode}`, ''),
              countryCode: model.country,
              number: model.countryCallingCode
            } as PhoneNumberValue
            factory.setValue(elementModel, filledValue,option)
            updatedElements.push(elementModel)
          }
        }
      } else if (element.type === FormElementType.EmailAddress) {
        const elementModel = element as FormElementEmailAddressModel
        if (!elementModel.enableAutoFill) {
          return
        }
        factory.setValue(elementModel, currentUser.email,option)
        updatedElements.push(elementModel)
      } else if(element.type === FormElementType.Date) {
        const elementModel= element as FormElementDateModel
        if(elementModel.useCurrentDate){
          const filledValue = cloneDeep(elementModel.value) as DateValue
          const timezone = getUserTimezone()
          const mo = moment().tz(timezone)
          filledValue.dateStr = mo.format(elementModel.dateFormat)
          if(elementModel.withTime && elementModel.useCurrentTime){
            filledValue.hour = elementModel.enable24Hour ? mo.hour()?.toString() : mo.format('hh');
            filledValue.minute = mo.minute()?.toString()
          }
          factory.setValue(elementModel, filledValue, {...option, changedProperty:'dateStr'})
        }
      }else{
        return
      }
    })

    if (updatedElements.length > 0) {
      return this.saveUserFilledField(stepSequence,updatedElements, myFilledValues,{boardId, transactionSequence, isPDFForm: formViewModel.isPDFForm})
    }
    return Promise.resolve()
  }

  removeFileFromElement (boardId, transactionSequence, referenceSequence) {
    return new Promise((resolve, reject) => {
      TransactionController.removeTransactionAttachment(
        boardId,
        transactionSequence,
        [referenceSequence],
        { suppressFeed: true }
      )
        .catch((err) => {
          console.log('removeFileError', err)
          if (err.code === 'RESPONSE_ERROR_NOT_FOUND') {
            reject(new CCustomError('', { fileNotFound: true }))
          } else {
            reject(err)
          }
        })
        .then(resolve)
    })
  }
  uploadFileToElement(toBoardId: string, transactionSequence: number, file: PickedFile, opts: Defines.ICreateTransactionAttachmentParam): Promise<IAttachmentViewModel> {
    return new Promise((resolve, reject) => {
      if(!file){
        reject('no file')
      }
      if(file.fileFrom === PluginType.ContentLibrary || file.fileFrom === PluginType.Workspace){
        const libFile = file.rawInfo
        return TransactionController.createTransactionAttachmentFromFile(
          libFile.boardId,
          [libFile.SPath],
          toBoardId,
          transactionSequence,
          opts
        ).then((result) => {
          //@ts-ignore
          resolve(result[0])
        }).catch((err) => {
          reject(err)
        })
      } else if(file.blob){
        return TransactionController.createTransactionAttachmentFromLocalFile(toBoardId,transactionSequence, file, opts).then(result =>{
          resolve(result[0])
        }).catch((err) => {
          reject(err)
        })
      } else if(file.url){
        //@ts-ignore
        return  TransactionController.createTransactionAttachmentFromUrl(toBoardId,transactionSequence, file, opts).then(result =>{
          //@ts-ignore
          resolve(result[0])
        }).catch((err) => {
          reject(err)
        })
      }
      return Promise.reject(new CCustomError('not support this PickedFile',{notSupport:true}))
    })
  }
}

export const FormController = FormControllerImpl.getInstance()
