import { FormViewModel } from '@model/form/defines/formViewModel'
import {FormAnyElementViewModel} from '@model/form/defines/allType';
import {FormElementSelectionOption, OptionLabel, OptionValue} from '@model/form/defines/base';
import difference from 'lodash/difference';
import isUndefined from 'lodash/isUndefined';
import isFunction from 'lodash/isFunction';
import isObject from 'lodash/isObject';
import isNull from 'lodash/isNull';
import clone from 'lodash/clone';
import {FormField} from '@model/form/defines/serverDataStructure';
import {isDefine} from "@model/form/factory/shared";
import {DateValue} from "@model/form/defines/FormDate";
import {UserNameValue} from "@model/form/defines/FormUserName";
import {AddressValue} from "@model/form/defines/FormAddress";
import {CurrencyValue} from "@model/form/defines/FormCurrency";
import {PhoneNumberValue} from "@model/form/defines/FormPhoneNumber";

export function isMultipleAssignee (formViewModel: FormViewModel) {
  const assignees = formViewModel.assignees || []
  return assignees.length > 1 || assignees.some(assignee => assignee.isTeam)
}


/**
 *
 * @param model
 * @param specialAttrs
 */
type ViewModelKey = string
type ServerDataKey = string
type AliasMap = Record<ViewModelKey, ServerDataKey>

export interface TransformFunction<T> {
  (val: T[keyof T], model: T): T[keyof T];
}

export type  ModelAttrTransforms<T>  = Partial<Record<keyof T, TransformFunction<T>>>
export type FieldAttrTransforms<T extends { fieldSpecific?: object }> =
    Partial<Record<keyof T | keyof NonNullable<T['fieldSpecific']>, TransformFunction<T>>>;

export interface FieldToModelAttrsOption<T> {
  alias?: AliasMap;
  transforms?: FieldAttrTransforms<T>;
  applyDefaultValue?: boolean;
}
export type PossibleElementValue = string | [] | number| boolean | DateValue | UserNameValue | AddressValue| CurrencyValue | PhoneNumberValue

export function createDefaultValueTransform(defValue: PossibleElementValue){
  return function (val, field: FormField) {
    if(!isDefine(field.defaultValue)){
      if(!isDefine(val)){
        return clone(defValue)
      }
      return val
    }
    /**
     * in PDF form the multi-selection defaultValue is ''
     */
    if(typeof(field.defaultValue) !== typeof(defValue)){
      return clone(defValue)
    }
    if(isObject(field.defaultValue)){
      return clone(field.defaultValue)
    }
    return field.defaultValue
  }
}

export interface ModelToFieldAttrsOption<T> {
  alias?: AliasMap;
  transforms?: ModelAttrTransforms<T>;
}
const OnlyUIUsedProps = ['errors','isVisible','defaultPlaceholder','imageURL', 'defaultLabel']
export function getAttrsFromFormField<T> (
    field: FormField,
    defaultModel: T,
    specialKeys: (keyof T)[],
    option?: FieldToModelAttrsOption<FormField>
): Partial<T> {
  const allAttrs = Object.keys(defaultModel) as (keyof T)[];
  const alias = (option?.alias ? option.alias : {}) as Record<keyof T, string>
  let node = field


  const result: Partial<T> = {
  };
  (result as any).errors = []

  allAttrs.forEach(key => {
    const isSpecialKey = specialKeys.includes(key)
    let fieldKey = key as string
    const modelKey = key as string
    if(alias[key]){
      fieldKey = alias[key] as string
    }
    if(isSpecialKey){
      //@ts-ignore
      node = field['fieldSpecific']
    }else{
      node = field
    }
    let value = node[fieldKey]
    if(isUndefined(value) || isNull(value)){
      value = defaultModel[key]
    }
    if(key === 'defaultValue' && typeof(value) !== typeof(defaultModel[key])){
      value = defaultModel[key]
    }

    const converter = option?.transforms?.[fieldKey]

    if(isFunction(converter)) {
      value = converter(value, field)
    }

    if (isObject(value)) {
      result[modelKey] = clone(value);
    } else {
      result[modelKey] = value;
    }

  })
  return result;
}

export function getAttrsFromViewModel<T> (
    model: T,
    defaultModel: FormAnyElementViewModel,
    specialKeys: (keyof T)[],
    option?: ModelToFieldAttrsOption<T>
): Partial<FormField> {
  const root: Partial<FormField> = {
    fieldSpecific:{}
  };
  const fieldSpecific = root.fieldSpecific
  let result = root
  const allAttrs = difference(Object.keys(defaultModel),OnlyUIUsedProps) as (keyof T)[];
  const alias = (option?.alias ? option.alias : {}) as Record<keyof T, string>

  allAttrs.forEach(key => {
    const isSpecialKey = specialKeys.includes(key)
    let realKey = key as string
    if(alias[key]){
      realKey = alias[key] as string
    }
    if(isSpecialKey){
      result = fieldSpecific
    }else{
      result = root
    }
    let value = model[key]
    const converter = option?.transforms?.[key]
    if(isFunction(converter)) {
      try {
        value = converter(value, model)
      }catch (e){
        console.error(e)
      }
    }
    if(isObject(value)){
      result[realKey] = clone(value);
    }else {
      result[realKey] = value;
    }
  })
  return root;
}

export function getValidLabelsFromOptions(value:OptionValue[] | OptionValue, model: {options: FormElementSelectionOption[]}): OptionLabel | OptionLabel[]{
  const options = model.options
  if(Array.isArray(value)){
    return options.filter(item => value.includes(item.value)).map(item => item.label)
  }else{
    return options.find(item => item.value === value)?.label || ''
  }
}
export function getValidValuesFromOptions(labels: OptionLabel | OptionLabel[], model: {options: FormElementSelectionOption[]}): OptionValue[] | OptionValue {
  const options = model.options
  if(Array.isArray(labels)){
    if(!labels.length){
      return labels
    }
    return options.filter(item => labels.includes(item.label)).map(item => item.value)
  }else if(labels){
    return options.find(item => item.label === labels)?.value || ''
  }
  return labels
}
export function getValidValuesFromFieldOptions(labels: OptionLabel | OptionLabel[], model: FormField): OptionValue[] | OptionValue {
  const options = model.fieldSpecific.options

  if(Array.isArray(labels)){
    //@ts-ignore
    return options.filter(item => labels.includes(item.label)).map(item => item.value)
  }else{
    //@ts-ignore
    return options.find(item => item.label === labels)?.value || ''
  }
}


export function mergeTransforms<T>(
  ...transforms: TransformFunction<T>[]
): TransformFunction<T> {
  return function(val: T[keyof T], model: T): T[keyof T] {
    return transforms.reduce((result, fn) => fn(result, model), val);
  };
}