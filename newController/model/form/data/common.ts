import { ObjectUtils} from '@commonUtils';
import {FormModel} from '@model/form/defines/serverDataStructure';

export const FormCurrentVersion = '10.0';
export const PDFFormCurrentVersion = '10.2'
export interface UpgradePlugin {
    upgrade: Function;
    /**
     Form version at the time the plugin was created, Used to manage the execution order of plugins.
     */
    version: string;
    /**
     * Determines whether the form data is compatible with the current upgrade logic.
     * - If `supportVersion` is a string, the form's version must strictly equal it.
     * @example
     *     supportVersion: '<= 10.0'
     *     supportVersion: '<= 10.0 || >= 12.0'
     *     supportVersion: '<10.0 & > 9.9'
     * - If `supportVersion` is a function, returning `true` means the form is supported.
     */
    supportVersion: string | Function;
}
export interface UpgradeOption {
  isCompleted: boolean;
}
function isMatchTargetVersion(sourceVersionString: string, supportVersionString: string): boolean {
    const sourceVersion = parseFloat(sourceVersionString)

    // 将 OR 条件切成多组 AND 条件
    const orConditions = supportVersionString.split('||').map(str => str.trim())

    for (const andGroup of orConditions) {
        const andConditions = andGroup.split('&').map(str => str.trim())

        const allMatch = andConditions.every(condition => {
            const match = condition.match(/(<=|>=|<|>|=)\s*(\d+(\.\d+)?)/)
            if (!match) return false

            const operator = match[1]
            const targetVersion = parseFloat(match[2])

            switch (operator) {
                case '<': return sourceVersion < targetVersion
                case '<=': return sourceVersion <= targetVersion
                case '>': return sourceVersion > targetVersion
                case '>=': return sourceVersion >= targetVersion
                case '=': return sourceVersion === targetVersion
                default: return false
            }
        })
        if (allMatch) return true
    }

    return false
}
export function isCanUpgrade(version: string, supportVersionString: string| Function): boolean{
    if(!version){
        // version is add in 10.0
        return true
    }
    if(ObjectUtils.isFunction(supportVersionString)){
        return (supportVersionString as Function)(version)
    }
    return isMatchTargetVersion(version, supportVersionString as string)
}
export function isLatestVersion(version: string){
    return version === FormCurrentVersion
}
export function isLatestPDFFormVersion(version: string){
    return version === PDFFormCurrentVersion
}
export function runUpgradePlugins(upgradeFnList: UpgradePlugin[], form: FormModel,option:UpgradeOption){
    if(!upgradeFnList.length){
        return
    }
    upgradeFnList.sort((a, b) =>{
        return parseFloat(a.version) < parseFloat(b.version) ? -1: 1
    })
   const startIndex =  upgradeFnList.findIndex(item => {
       return isCanUpgrade(form.version, item.supportVersion)
    })
    if(startIndex == -1){
        return;
    }
    for(let start = startIndex; start < upgradeFnList.length; start++){
        const program =upgradeFnList[start]
       if(isCanUpgrade(form.version, program.supportVersion)){
           program.upgrade(form, option)
       }
    }
}