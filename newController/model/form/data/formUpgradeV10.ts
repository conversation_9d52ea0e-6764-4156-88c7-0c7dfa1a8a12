import { FormModel } from '@model/form/defines/serverDataStructure'
import { FormElementTransformOption, FormElementType } from '@model/form/defines/shared'
import {isSupportUniqueName, makeUniqueName} from '@views/form/common/uniqueName';
import {UpgradePlugin} from '@model/form/data/common';
import {CFormModel} from '@controller/defines/CFormModel';
import {ObjectUtils} from '@commonUtils';

export const currVersion = '10.0'

 function upgrade (data: FormModel,option: FormElementTransformOption):void {

    data.data.pages.forEach((page) => {
        page.fields.forEach((field) => {
            /**
             * MV-18841
             * File upload value support multi files
             */
            if (field.type === FormElementType.FileUpload) {
                if(field.value && !Array.isArray(field.value))
                field.value = [field.value]
            }
            /**
             * MV-18817
             * 1. add unique name,
             * 2. user name support default value
             * 3. email address support default value
             */
            if(isSupportUniqueName(field.type)){
                makeUniqueName(field, data.data as CFormModel)
            }
            if(ObjectUtils.isDefine(field.fieldSpecific.enableDefaultValue)){
                return
            }
            if([FormElementType.UserName, FormElementType.EmailAddress].includes(field.type)){
                field.fieldSpecific.enableDefaultValue = field.fieldSpecific.defaultFromProfile
                if(!field.defaultValue) {
                    if (field.type === FormElementType.UserName) {
                        field.defaultValue = {
                            firstName: '',
                            lastName: ''
                        }
                    } else {
                        field.defaultValue = ''
                    }
                }
            }
        })
    })
     data.version = currVersion
     console.log(`Upgrade form to ${currVersion} success!`)
}

const  UpgradeToV10: UpgradePlugin = {
    version: currVersion,
    supportVersion: `< ${currVersion}`,
    upgrade
}
export default UpgradeToV10