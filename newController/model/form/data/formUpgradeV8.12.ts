import {FormModel} from '@model/form/defines/serverDataStructure'

import {UpgradePlugin} from '@model/form/data/common';
import { FormElementTransformOption, FormElementType } from '@model/form/defines/shared'
import {ObjectUtils} from '@commonUtils';

export const currVersion = '8.12'

function upgrade (data: FormModel,option: FormElementTransformOption):void {
    const targetVersion = currVersion
  if(option.isCompleted){
    return
  }
// tslint:disable
    data.data.pages.forEach((page) => {
        page.fields.forEach((field) => {
            if(ObjectUtils.isDefine(field.defaultValue)){
                return;
            }
            switch (field.type) {
              case FormElementType.Number:
              case FormElementType.SingleLineText:
              case FormElementType.MultiLineText:
                  field.defaultValue = field.value;
                  field.value = ''
                  break;
                case FormElementType.Date:
                    //@ts-ignore
                    if(field.value?.timestamp){
                        field.defaultValue = JSON.parse(JSON.stringify(field.value))
                        //@ts-ignore
                        field.value.timestamp = 0
                    }
                    break;
                case FormElementType.PhoneNumber:
                case FormElementType.Address:
                    //@ts-ignore
                    if(field.value?.countryCode){
                        field.defaultValue = JSON.parse(JSON.stringify(field.value))
                        //@ts-ignore
                        field.value.countryCode = ''
                    }
                    break;
                case FormElementType.Currency:
                    //@ts-ignore
                    if(field.value?.amount){
                        field.defaultValue = JSON.parse(JSON.stringify(field.value))
                        //@ts-ignore
                        field.value.amount = 0
                    }
                    break;
            }
            
        })
    })
    // tslint:enable
    data.version = currVersion
    console.log(`Upgrade form to ${targetVersion} success!`)
}

const  UpgradeToV812: UpgradePlugin = {
    version: currVersion,
    supportVersion: `< ${currVersion}`,
    upgrade
}
export default UpgradeToV812