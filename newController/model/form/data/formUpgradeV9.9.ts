import {FormModel} from '@model/form/defines/serverDataStructure'

import {UpgradePlugin} from '@model/form/data/common';
import { FormElementTransformOption, FormElementType } from '@model/form/defines/shared'
import {ObjectUtils} from '@commonUtils';

export const currVersion = '9.9'

function upgrade (data: FormModel,option: FormElementTransformOption):void {
    const targetVersion = currVersion
  if(option.isCompleted){
    return
  }
    data.data.pages.forEach((page) => {
        page.fields.forEach((field) => {
            if(ObjectUtils.isDefine(field.defaultValue)){
                return;
            }
            switch (field.type) {
                case FormElementType.SingleSelection:
                case FormElementType.DropdownList:
                case FormElementType.EmailAddress:
                    field.defaultValue = field.value;
                    field.value = ''
                    break;
                case FormElementType.MultiSelection:

                    break;
                case FormElementType.UserName:


                    break;
            }

        })
    })
    data.version = currVersion
    console.log(`Upgrade form to ${targetVersion} success!`)
}

const  UpgradeToV990: UpgradePlugin = {
    version: currVersion,
    supportVersion: `< ${currVersion}`,
    upgrade
}
export default UpgradeToV990