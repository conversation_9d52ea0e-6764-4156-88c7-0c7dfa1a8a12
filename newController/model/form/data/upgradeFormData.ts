import { FormModel } from '@model/form/defines/serverDataStructure'
import UpgradeToV10 from '@model/form/data/formUpgradeV10';
import UpgradeToV8_12 from '@model/form/data/formUpgradeV8.12';
import UpgradeToV9_9 from '@model/form/data/formUpgradeV9.9';
import { isLatestVersion, runUpgradePlugins, UpgradeOption, UpgradePlugin } from '@model/form/data/common'
const upgradeFnList:UpgradePlugin[] = [UpgradeToV8_12,UpgradeToV9_9,UpgradeToV10]

export function upgradeFormData(form: FormModel,option?: UpgradeOption){
    if(isLatestVersion(form.version)){
        return;
    }
   runUpgradePlugins(upgradeFnList, form, option)
}
