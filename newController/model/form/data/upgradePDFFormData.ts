import {FormModel} from '@model/form/defines/serverDataStructure';
import {
  isLatestPDFFormVersion,
  runUpgradePlugins, UpgradeOption,
  UpgradePlugin
} from '@model/form/data/common'
import PDFUpgradeToV102 from '@model/form/data/pdfFormUpgradeV10.2'

const upgradeFnList:UpgradePlugin[] = [PDFUpgradeToV102]

export function upgradePDFFormData(form:FormModel,option: UpgradeOption){
    if(isLatestPDFFormVersion(form.version)){
        return;
    }
    runUpgradePlugins(upgradeFnList, form,option)
}
