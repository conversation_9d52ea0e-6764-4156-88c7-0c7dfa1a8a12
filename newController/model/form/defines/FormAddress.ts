import { FormElementType } from './shared'
import type { FormElementBaseUIProps, FormElementInputBaseModel } from '@model/form/defines/base'

interface AddressValue {
  addressLineOne: string;
  addressLineTwo: string;
  city: string;
  state: string;
  zipcode: string;
  countryCode: string;
  countryDisplayName: string;
}

interface AddressSpecial {
  lockCountry: boolean;
  showAddressLineTwo: boolean;
  showCity: boolean;
  showState: boolean;
  showZipcode: boolean;
  showCountry: boolean;
  lineOneLabel: string;
  lineTwoLabel: string;
  cityLabel: string;
  stateLabel: string;
  zipCodeLabel: string;
  countryLabel: string;
}



type FormElementAddressModel = FormElementInputBaseModel<AddressValue> &
  AddressSpecial & FormElementBaseUIProps & {
  type: FormElementType.Address;
}
const AddressDefaultValue: AddressValue = {
  addressLineOne: '',
  addressLineTwo: '',
  city: '',
  state: '',
  zipcode: '',
  countryCode: 'US',
  countryDisplayName: '',
}

const AddressDefaultProps: FormElementAddressModel = {
  name: '',
  uniqueName: '',
  lockCountry: false,
  showAddressLineTwo: false,
  showCity: false,
  showState: false,
  showZipcode: false,
  showCountry: false,
  hideLabel: false,
  required: false,
  value: {
    ...AddressDefaultValue
  },
  defaultValue: {
    ...AddressDefaultValue
  },
  id: '',
  isProtected: false,
  label: '$t(“address”)',
  defaultLabel: '$t(“address”)',
  placeholder: '',
  supporting: '',
  readonly: false,
  type: FormElementType.Address,
  isVisible: true,
  errors: [],
  lineOneLabel:'',
  lineTwoLabel: '',
  cityLabel: '',
  stateLabel: '',
  zipCodeLabel: '',
  countryLabel: ''
}
export const AddressSpecialKeys: Exclude<keyof FormElementAddressModel, 'condition'>[] = [
  'lockCountry',
  'showAddressLineTwo',
  'showCity',
  'showState',
  'showZipcode',
  'showCountry',
  'hideLabel',
  'required',
]
export { AddressDefaultProps, AddressDefaultValue }
export type { FormElementAddressModel, AddressValue }
