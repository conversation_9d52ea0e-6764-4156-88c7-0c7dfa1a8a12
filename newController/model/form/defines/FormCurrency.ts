import type { FormElementInputBaseModel, FormElementBaseUIProps } from './base'
import { FormElementType } from './shared'

interface CurrencyValue {
  amount: string;
  code: string;
}

interface CurrencySpecial {
  maxLength: string;
  minLength: string;
  precision: number;
}

const CurrencyPrecision = [0, 1, 2]


type FormElementCurrencyModel = FormElementInputBaseModel<CurrencyValue> &
  CurrencySpecial & FormElementBaseUIProps & {
  type: FormElementType.Currency;
}
const CurrencyDefaultValue: CurrencyValue = {
  amount: '',
  code: ''
}
const CurrencyDefaultProps: FormElementCurrencyModel = {
  type: FormElementType.Currency,
  name: '',
  uniqueName: '',
  placeholder: '0.00',
  precision: 0,
  label: '$t("currency")',
  defaultLabel: '$t("currency")',
  hideLabel: false,
  supporting: '',
  required: false,
  readonly: false,
  minLength: '',
  maxLength: '',
  isProtected: false,
  isVisible: true,
  value: { ...CurrencyDefaultValue },
  defaultValue: { ...CurrencyDefaultValue },
  defaultPlaceholder:'',
  id: '',
  errors:[]
}
const CurrencySpecialKeys: Exclude<keyof FormElementCurrencyModel, 'condition'>[]  = ['precision', 'minLength',
  'maxLength', 'hideLabel', 'isProtected', 'placeholder',
  'readonly', 'required', 'supporting']
export { CurrencyPrecision, CurrencyDefaultProps, CurrencyDefaultValue,CurrencySpecialKeys }

export type { FormElementCurrencyModel ,CurrencyValue}