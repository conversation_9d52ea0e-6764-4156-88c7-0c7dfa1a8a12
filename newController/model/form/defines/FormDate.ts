import { FormElementType } from './shared'
import { FormElementInputBaseModel, FormElementBaseUIProps } from './base'
import {TransformFunction} from "@model/form/common/utils";
import moment from "moment";
import {getUserTimezone} from '@model/form/factory/utils'

interface DateValue {
  hour: string;
  minute: string;
  timeFormat: 'AM' | 'PM';
  timestamp: number;
  dayTimestamp: number;
  dateStr: string;
}

const DateDefaultVaue: DateValue = {
  hour: null,
  minute: null,
  timeFormat: 'AM',
  timestamp: null,
  dayTimestamp: 0,
  dateStr: ''
}

const DateSupportFormats = [
  'DD, MMM YYYY',
  'DD/MMM/YYYY',
  'DD/MM/YYYY',
  'DD-MM-YYYY',
  'DD-MMM-YYYY',
  'DD MM YYYY',
  'DD MMM YYYY',
  'MM/DD/YYYY',
  'MMM/DD/YYYY',
  'll'
]
type DateSupportFormatsType = (typeof DateSupportFormats)[number];


interface DateSpecial {
  useCurrentDate: boolean;
  useCurrentTime: boolean;
  enable24Hour: boolean;
  dateFormat: DateSupportFormatsType;
  withTime: boolean;
  withDate: boolean;
  enableDefaultValue: boolean;
  displayValue: string;
}


type FormElementDateModel = FormElementInputBaseModel<DateValue> &
  DateSpecial & FormElementBaseUIProps & {
  type: FormElementType;
}

const DateDefaultProps: FormElementDateModel = {
  name: '',
  id: '',
  uniqueName: '',
  placeholder:'',
  type: FormElementType.Date,
  label: '$t("date")',
  defaultLabel: '$t("date")',
  hideLabel: false,
  supporting: '',
  required: false,
  readonly: false,
  isProtected: false,
  isVisible: true,
  value: { ...DateDefaultVaue },
  defaultValue: { ...DateDefaultVaue },
  displayValue: '',
  useCurrentDate: false,
  useCurrentTime: false,
  dateFormat: 'DD/MMM/YYYY',
  enable24Hour: false,
  withTime: false,
  withDate: true, //do we use it?
  defaultPlaceholder:'',
  errors:[],
  enableDefaultValue: false
}
/**
 * Define which properties need to be placed in fieldSpecific
 */
export const DateSpecialKeys: Exclude<keyof FormElementDateModel, 'condition'>[] = [
   'enable24Hour', 'dateFormat', 'withTime','useCurrentDate','useCurrentTime',
  'withDate','isProtected','supporting','hideLabel','required', 'readonly','enableDefaultValue']

export const DateToServerTransforms:Partial<Record<keyof FormElementDateModel, TransformFunction<FormElementDateModel>>> = {
  enable24Hour: function (enable) {
    return enable ? '24' : 'A'
  },
  displayValue: function (val,model){
    const {dateStr} = model.value
    const timezone = getUserTimezone()
    const dayTimestamp = moment.tz(dateStr,model.dateFormat, timezone).valueOf()
    return dayTimestamp ? moment.tz(dayTimestamp, timezone).format(model.dateFormat) : ''
  }
}
export const DateToModelTransforms = {
  timeFormat: function (value) {
    return value === '24'
  }
}
export const DateAttrAlias = {
  useCurrentDate: 'currentDate',
  useCurrentTime:'currentTime',
  enable24Hour: 'timeFormat'
}

export { DateDefaultProps, DateSupportFormats, DateDefaultVaue }
export type { FormElementDateModel , DateValue}
