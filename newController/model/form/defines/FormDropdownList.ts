import  { FormElementType } from './shared'
import {FormElementBaseUIProps, FormElementInputBaseModel, FormElementSelectionOption} from '@model/form/defines/base'

interface DropdownListSpecial {
  options: FormElementSelectionOption[];
}




type FormElementDropdownListModel = FormElementInputBaseModel<string> &
  DropdownListSpecial & FormElementBaseUIProps & {
  type: FormElementType.DropdownList;
}

const DropdownListDefaultProps: FormElementDropdownListModel = {
  name: '',
  id:'',
  uniqueName: '',
  type: FormElementType.DropdownList,
  options: [{value: 'Type Option 1'}, {value: 'Type Option 2'}],
  value: '',
  label: '$t("type_a_question")',
  defaultLabel: '$t("type_a_question")',
  placeholder: '$t("select_an_option")',
  defaultValue: '',
  isProtected: false,
  isVisible: true,
  readonly: false,
  required: false,
  hideLabel: false,
  supporting: '',
  errors:[]
}
const DropdownListSpecialKeys: Exclude<keyof FormElementDropdownListModel, 'condition'>[]= ['options','placeholder','required','supporting','hideLabel']


export { DropdownListDefaultProps, DropdownListSpecialKeys }
export type { FormElementDropdownListModel }