import  { FormElementType } from './shared'
import type { FormElementBaseUIProps, FormElementInputBaseModel } from '@model/form/defines/base'

interface EmailAddressSpecial {
  enableAutoFill: boolean;
}



type FormElementEmailAddressModel = FormElementInputBaseModel<string> &
  EmailAddressSpecial & FormElementBaseUIProps & {
  type: FormElementType.EmailAddress;
}
const EmailAddressDefaultProps: FormElementEmailAddressModel = {
  name: '',
  id:'',
  uniqueName: '',
  type: FormElementType.EmailAddress,
  defaultValue: '',
  value: '',
  placeholder: '',
  isProtected: false,
  isVisible: true,
  readonly: false,
  required: false,
  hideLabel: false,
  supporting: '',
  label: '$t("email")',
  defaultLabel: '$t("email")',
  enableAutoFill: false,
  errors:[]
}
const EmailAddressSpecialKeys: Exclude<keyof FormElementEmailAddressModel, 'condition'>[] = ['enableAutoFill','hideLabel','placeholder','supporting','required','readonly']
export const EmailAddressAlias = {
  enableAutoFill: 'defaultFromProfile'
}
export { EmailAddressSpecialKeys, EmailAddressDefaultProps }
export type { FormElementEmailAddressModel }