import { FormElementId, FormElementType } from './shared'
import type { FormElementBaseUIProps, FormElementInputBaseModel } from '@model/form/defines/base'
interface FileUploadError{
  isRemoveError?: boolean;
  allowRetry?: boolean;
}
interface FileUploadValue {
  name: string;
  uuid: string;
  SPath?: string;
  type: string;
  size: number;
  /**
   * reference sequence
   */
  refSeq: number;
  /**
   * resource sequence
   */
  resSeq: number;
  /**
   * the shared file url, for download in PDF
   */
  url: string;
  /**
   * Below are some UI presentation-related properties that will not be saved to the server.
   */
  thumbnail?: string;
  /**
   * the file uploading status.
   */
  loading?: boolean;
  error?: FileUploadError;
}

interface FileUploadSpecial {
  maxFileSize: number;
  fileAccept: string;
  maxFileCount: number;
}

type FormElementFileUploadModel = FormElementInputBaseModel<FileUploadValue[]> &
  FileUploadSpecial & FormElementBaseUIProps & {
  type: FormElementType.FileUpload;
}

interface AttachmentCustomData {
  uuid: string;
  elementId: FormElementId;
  step: number; //step sequence
}

const FileUploadDefaultProps: FormElementFileUploadModel = {
  name: '',
  uniqueName: '',
  placeholder: '',
  value: [],
  defaultValue: [] ,
  isVisible: true,
  readonly: false,
  isProtected: false,
  required: false,
  hideLabel: false,
  defaultPlaceholder: '',
  supporting: '',
  label: '$t("FileUpload")',
  defaultLabel: '$t("FileUpload")',
  type: FormElementType.FileUpload,
  maxFileSize: 0,
  maxFileCount:1,
  fileAccept: '',
  id: '',
  errors: []
}
const FileUploadSpecialKeys: Exclude<keyof FormElementFileUploadModel, 'condition'>[] = [ 'hideLabel', 'placeholder', 'required', 'supporting', 'maxFileSize', 'fileAccept', 'maxFileCount' ]
export { FileUploadDefaultProps, FileUploadSpecialKeys }
export type { FormElementFileUploadModel, FileUploadValue, AttachmentCustomData }