import  {
  AlignmentType,
  FormElementType
} from './shared'
import { FormElementBaseModel, type FormElementBaseUIProps } from './base'

interface HeadingSpecial {
  labelAlignment: AlignmentType;
  imageAlignment: AlignmentType;
  imageAlt: string;
  imageName: string;
  supporting: string;
  imageUUID: string;
  imageWidth: string;
  enableImage: boolean;
}
interface HeadingUIProps {
  imageURL?: string;
}



type FormElementHeadingModel = FormElementBaseModel &
  FormElementBaseUIProps &
  HeadingUIProps &
  HeadingSpecial & {
  type: FormElementType.Heading;
}

const HeadingDefaultProps: FormElementHeadingModel = {
  name: '',
  id: '',
  type: FormElementType.Heading,
  labelAlignment: AlignmentType.Left,
  imageAlignment: AlignmentType.Left,
  label: '',
  defaultLabel: '',
  hideLabel: false,
  supporting: '',
  imageName: '',
  imageUUID: '',
  enableImage: false,
  imageWidth: '',
  imageAlt: '',
  isVisible: true,
  errors: [],
}
const HeadingSpecialKeys: Exclude<keyof FormElementHeadingModel, 'condition'>[] = ['imageWidth', 'imageAlt', 'imageName', 'imageUUID', 'labelAlignment', 'imageAlignment', 'enableImage','supporting']

export { HeadingSpecialKeys, HeadingDefaultProps }
export type { FormElementHeadingModel }