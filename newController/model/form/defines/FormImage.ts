import {
  AlignmentType,
  FormElementType
} from './shared'
import type { FormElementBaseUIProps, FormElementBaseModel } from '@model/form/defines/base'

interface FormImageSpecial {
  imageAlignment: AlignmentType;
  imageAlt: string;
  imageUUID: string;
  imageName: string;
  imageWidth: string;
  supporting: string;
}
interface FormImageUIProps {
  imageURL: string;
}



type FormElementImageModel = FormElementBaseModel &
  FormElementBaseUIProps &
  FormImageUIProps &
  FormImageSpecial & {
  type: FormElementType.Image;
}

const FormImageDefaultProps: FormElementImageModel = {
  name: '',
  imageAlignment: AlignmentType.Left,
  imageWidth:'200',
  label: '$t("Image")',
  imageURL: '',
  defaultLabel: '',
  hideLabel: true,
  imageAlt: '',
  imageName: '',
  imageUUID: '',
  id: '',
  type: FormElementType.Image,
  isVisible: true,
  supporting: '',
  errors: [],
}
const FormImageSpecialKeys: Exclude<keyof FormElementImageModel, 'condition'>[] = ['imageWidth', 'imageAlt', 'imageName', 'imageUUID', 'imageAlignment','supporting']

export { FormImageSpecialKeys, FormImageDefaultProps }
export type { FormElementImageModel }