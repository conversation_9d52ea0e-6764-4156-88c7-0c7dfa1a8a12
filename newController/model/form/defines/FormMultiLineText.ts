import { FormElementType } from './shared'
import type { FormElementBaseUIProps, FormElementInputBaseModel } from '@model/form/defines/base'

interface MultiLineTextSpecial {
  rowHeight: string;
  maxLength: string;
  minLength: string;
}

type FormElementMultiLineTextModel = FormElementInputBaseModel<string> &
  MultiLineTextSpecial & FormElementBaseUIProps & {
  type: FormElementType.MultiLineText;
}
const MultiLineTextDefaultProps: FormElementMultiLineTextModel ={
  name: '',
  type: FormElementType.MultiLineText,
  id: '',
  uniqueName: '',
  value: '',
  label: '$t("type_a_question")',
  defaultLabel: '$t("type_a_question")',
  hideLabel: false,
  defaultValue:'',
  rowHeight: '4',
  required: false,
  readonly: false,
  isProtected: false,
  supporting:'',
  placeholder: '$t("enter_your_answer_here")',
  maxLength:'',
  minLength:'',
  isVisible: true,
  errors:[]
}
const MultiLineTextSpecialKeys: Exclude<keyof FormElementMultiLineTextModel, 'condition'>[]  =['rowHeight', 'hideLabel','placeholder', 'isProtected','readonly','required','supporting','minLength', 'maxLength']
export {
  MultiLineTextDefaultProps, MultiLineTextSpecialKeys
}
export type { FormElementMultiLineTextModel }