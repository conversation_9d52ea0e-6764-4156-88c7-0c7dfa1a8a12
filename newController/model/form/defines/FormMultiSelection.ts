import { FormElementType } from './shared'
import type { FormElementBaseUIProps, FormElementInputBaseModel } from '@model/form/defines/base'
import { FormElementSelectionOption } from '@model/form/defines/base'

import {getValidValuesFromOptions} from "@model/form/common/utils";

interface MultiSelectionBaseProps {
  options: FormElementSelectionOption[];
  value: string[];
  defaultValue: string[];
  // for pdf check box group case.
  onlyCanSelectOne: boolean;

  displayValue: string;
}


type FormElementMultiSelectionModel =
  FormElementInputBaseModel<string[]>
  & FormElementBaseUIProps
  & MultiSelectionBaseProps
  & {
  type: FormElementType.MultiSelection;
}
const MultiSelectionDefaultProps: FormElementMultiSelectionModel = {
  name: '',
  type: FormElementType.MultiSelection,
  id: '',
  uniqueName: '',
  options: [{ value: 'Type Option 1' }, { value: 'Type Option 2' }],
  value: [],
  defaultValue: [],
  displayValue: '',
  isVisible: true,
  label: '',
  hideLabel: false,
  supporting: '',
  required: false,
  placeholder: '',
  isProtected: false,
  readonly: false,
  defaultLabel: '',
  defaultPlaceholder: '',
  onlyCanSelectOne: false,
  errors: []
}

export const MultiSelectionToServerTransforms = {
  displayValue: function (value, model) {
    const options = model.options
    const values = getValidValuesFromOptions(model.value, model) as string[]
    values.sort((a, b) => options.indexOf(a) - options.indexOf(b))
    return values.join(', ')
  }
}

const MultiSelectionSpecialKeys: Exclude<keyof FormElementMultiSelectionModel, 'condition'>[] = ['options', 'hideLabel', 'supporting', 'required']
export { MultiSelectionDefaultProps, MultiSelectionSpecialKeys }
export type { FormElementMultiSelectionModel }