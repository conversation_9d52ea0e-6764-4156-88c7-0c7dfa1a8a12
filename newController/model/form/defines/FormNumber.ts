import { FormElementType } from './shared'
import type { FormElementBaseUIProps, FormElementInputBaseModel } from '@model/form/defines/base'


interface NumberSpecial {
  precision: number;
  minLength: string;
  maxLength: string;
}


type FormElementNumberModel = FormElementInputBaseModel<string|null> &
  NumberSpecial & FormElementBaseUIProps & {
  type: FormElementType.Number;
}

const NumberDefaultProps: FormElementNumberModel = {
  name: '',
  uniqueName: '',
  type: FormElementType.Number,
  placeholder: '123',
  precision: 0,
  label: '$t("number")',
  hideLabel: false,
  supporting: '',
  required: false,
  readonly: false,
  defaultValue: null,
  minLength: '',
  maxLength: '',
  isProtected: false,
  isVisible: true,
  value: null,
  id: '',
  defaultLabel: '$t("number")',
  defaultPlaceholder:'',
  errors:[]
}
const NumberSpecialKeys: Exclude<keyof FormElementNumberModel, 'condition'>[] = ['precision','minLength','maxLength','hideLabel', 'placeholder','readonly','required','supporting', 'isProtected']
export { NumberDefaultProps, NumberSpecialKeys }
export type { FormElementNumberModel }