import { FormElementType } from './shared'
import { FormElementBaseModel, FormElementBaseUIProps } from '@model/form/defines/base'

type FormElementParagraphModel = FormElementBaseModel &
  FormElementBaseUIProps &
  {
    type: FormElementType.Paragraph;
    text: string;
  }
const ParagraphDefaultProps: FormElementParagraphModel = {
  id: '',
  name:'',
  isVisible: true,
  hideLabel: false,
  label: '$t("Paragraph")',
  defaultLabel: '$t("Paragraph")',
  type: FormElementType.Paragraph,
  text: '',
  errors:[]
}
const ParagraphSpecialKeys: Exclude<keyof FormElementParagraphModel, 'condition'>[] = ['hideLabel']
export { ParagraphDefaultProps ,ParagraphSpecialKeys}
export type { FormElementParagraphModel }