import { FormElementType } from './shared'
import type { FormElementBaseUIProps, FormElementInputBaseModel } from '@model/form/defines/base'
import {ModelAttrTransforms} from "@model/form/common/utils";

interface PhoneNumberValue {
  countryCode: string;
  number: string;
  phoneNumber: string;
}

interface PhoneNumberSpecial {
  enableAutoFill: boolean;
  lockCountry: boolean;

  displayValue: string;
}

type FormElementPhoneNumberModel = FormElementInputBaseModel<PhoneNumberValue> &
  PhoneNumberSpecial & FormElementBaseUIProps & {
  type: FormElementType.PhoneNumber;
}
const PhoneNumberDefaultValue = {
  countryCode: 'US',
  number: '',
  phoneNumber: ''
}
const PhoneNumberDefaultProps: FormElementPhoneNumberModel = {
  name: '',
  uniqueName: '',
  placeholder: '(*************',
  value: {
    ...PhoneNumberDefaultValue
  } as PhoneNumberValue,
  defaultValue: {
    ...PhoneNumberDefaultValue
  } as PhoneNumberValue,
  displayValue: '',
  isProtected: false,
  isVisible: true,
  enableAutoFill: false,
  readonly: false,
  required: false,
  hideLabel: false,
  lockCountry: false,
  defaultPlaceholder:'',
  supporting: '',
  label: '$t("phoneNumber")',
  defaultLabel: '$t("phoneNumber")',
  type: FormElementType.PhoneNumber,
  id: '',
  errors:[]
}
export const PhoneNumberAlias = {
  enableAutoFill: 'defaultFromProfile'
}
export const FormPhoneNumberTransforms: ModelAttrTransforms<FormElementPhoneNumberModel> = {
  displayValue(value,model){
    const {phoneNumber, number} = model.value
    return phoneNumber ? `${number}${phoneNumber}` : ''
  }
}
const PhoneNumberSpecialKeys: Exclude<keyof FormElementPhoneNumberModel, 'condition'>[] = ['enableAutoFill','lockCountry','hideLabel','placeholder','readonly','required','supporting','isProtected']
export { PhoneNumberDefaultProps, PhoneNumberSpecialKeys, PhoneNumberDefaultValue }
export type { FormElementPhoneNumberModel ,PhoneNumberValue}