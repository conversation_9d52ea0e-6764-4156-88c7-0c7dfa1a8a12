import type {
  FormElementInputBaseModel
} from './base'
import { FormElementType } from './shared'
import type { FormElementBaseUIProps } from '@model/form/defines/base'

type FormElementSignatureModel = FormElementInputBaseModel<string> & FormElementBaseUIProps &
  {
    type: FormElementType.Signature;
    imageURL?: string;
    value: string;
  }

const SignatureDefaultProps: FormElementSignatureModel = {
  name: '',
  id: '',
  uniqueName: '',
  type: FormElementType.Signature,
  value: '',
  defaultValue: '',
  isProtected: false,
  isVisible: true,
  readonly: false,
  required: false,
  hideLabel: false,
  placeholder: '',
  defaultPlaceholder: '',
  supporting: '',
  label: '$t("Signature")',
  defaultLabel: '$t("Signature")',
  errors: []
}
export type { FormElementSignatureModel }
const SignatureSpecialKeys: Exclude<keyof FormElementSignatureModel, 'condition'>[] = ['hideLabel', 'required','readonly']

export {
  SignatureSpecialKeys, SignatureDefaultProps
}