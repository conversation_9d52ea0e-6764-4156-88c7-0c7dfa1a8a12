import { FormElementType } from './shared'
import type { FormElementBaseUIProps, FormElementInputBaseModel } from '@model/form/defines/base'


type FormElementSingleLineTextModel = FormElementInputBaseModel<string> & FormElementBaseUIProps &
  {
    type: FormElementType.SingleLineText;
    maxLength: string;
    minLength: string;
  }
const SingleLineTextDefaultProps: FormElementSingleLineTextModel = {
  name: '',
  id: '',
  uniqueName: '',
  value: '',
  defaultValue: '',
  isProtected: false,
  isVisible: true,
  readonly: false,
  required: false,
  hideLabel: false,
  supporting: '',
  label: '$t("type_a_question")',
  defaultLabel: '$t("type_a_question")',
  placeholder: '$t("enter_your_answer_here")',
  defaultPlaceholder: '$t("enter_your_answer_here")',
  type: FormElementType.SingleLineText,
  minLength: '',
  maxLength: '',
  errors:[]
}
const SingleLineTextSpecialKeys: Exclude<keyof FormElementSingleLineTextModel, 'condition'>[]= ['hideLabel', 'placeholder','readonly','required','supporting', 'isProtected', 'minLength','maxLength']
export { SingleLineTextDefaultProps ,SingleLineTextSpecialKeys}
export type { FormElementSingleLineTextModel }