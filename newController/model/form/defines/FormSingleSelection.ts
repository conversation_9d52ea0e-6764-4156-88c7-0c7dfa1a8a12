import { FormElementType } from './shared'
import type { FormElementBaseUIProps, FormElementInputBaseModel } from '@model/form/defines/base'
import { FormElementSelectionOption } from '@model/form/defines/base'
import {FormField} from '@model/form/defines/serverDataStructure';
import isString from 'lodash/isString';

type SingleSelectionBaseProps = {
  options: FormElementSelectionOption[];
  value?: string;
  defaultValue?: string;
}

type FormElementSingleSelectionModel =
  FormElementInputBaseModel<string>
  & FormElementBaseUIProps
  & SingleSelectionBaseProps
  & {
  type: FormElementType.SingleSelection;
}

const SingleSelectionDefaultProps: FormElementSingleSelectionModel = {
  type: FormElementType.SingleSelection,
  name: '',
  id: '',
  uniqueName: '',
  options: [{ value: 'Type Option 1' }, { value: 'Type Option 2' }],
  value: '',
  defaultValue: '',
  isVisible: true,
  label: '',
  hideLabel: false,
  supporting: '',
  required: false,
  placeholder: '',
  isProtected: false,
  readonly: false,
  defaultLabel: '',
  defaultPlaceholder: '',
  errors: []
}


export function getValidValueFromOptions (value: string, model: FormElementSingleSelectionModel) {
  const options = model.options
  return options.find(item => item.value === value) ? value : ''
}
export function getValidValueFromOptionsFromField (value: string, model: FormField) {
  /**
   * for form item in options is string, for PDF form item is object of {label, value}
   */
  const options = (model.fieldSpecific?.options || []).map((item) => {
    if(isString(item)){
      return item
    }else{
      const option = item as FormElementSelectionOption
      return option.value
    }
  })
  return options.includes(value)? value : ''
}
export const SingleSelectionToServerTransforms = {
  value: getValidValueFromOptions
}

const SingleSelectionSpecialKeys: Exclude<keyof FormElementSingleSelectionModel, 'condition'>[] = ['options', 'hideLabel', 'supporting', 'required']

export { SingleSelectionSpecialKeys, SingleSelectionDefaultProps }
export type { FormElementSingleSelectionModel }