import  { FormElementType } from './shared'
import type { FormElementBaseUIProps, FormElementInputBaseModel } from '@model/form/defines/base'

interface UserNameValue {
  firstName: string;
  lastName: string;
  prefix: string;
  middleName: string;
  suffix: string;
}

interface UserNameBaseProps {
  prefixOptions: string[];
}

interface UserNameSpecial {
  showMiddleName: boolean;
  showPrefix: boolean;
  showSuffix: boolean;
  enableDefaultValue: boolean;
  enableAutoFill: boolean;
}


const UserNameDefaultValue: UserNameValue = {
  firstName: '',
  lastName: '',
  prefix: '',
  middleName: '',
  suffix: ''
}

type FormElementUserNameModel = FormElementInputBaseModel<UserNameValue>
  & UserNameBaseProps & FormElementBaseUIProps
  & UserNameSpecial & {
  type: FormElementType.UserName;
}

const UserNameDefaultProps: FormElementUserNameModel = {
  name: '',
  id:'',
  uniqueName: '',
  type: FormElementType.UserName,
  value: {
    ...UserNameDefaultValue
  },
  defaultValue: {
    ...UserNameDefaultValue
  },
  prefixOptions: [
    'Mr.',
    'Mrs.',
    'Ms.'
  ],
  placeholder: '',
  isProtected: false,
  isVisible: true,
  readonly: false,
  required: false,
  hideLabel: false,
  showMiddleName: false,
  showPrefix: false,
  showSuffix: false,
  enableDefaultValue: false,
  enableAutoFill: false,
  supporting: '',
  label: '$t("name")',
  defaultLabel: '$t("name")',
  errors:[]
}
const UserNameSpecialKeys: Exclude<keyof FormElementUserNameModel, 'condition'>[]= ['showMiddleName', 'showPrefix','prefixOptions','showSuffix','enableAutoFill','supporting','required','hideLabel','isProtected','readonly','enableDefaultValue']
export const UserNameAlias = {
  enableAutoFill: 'defaultFromProfile'
}
export { UserNameSpecialKeys, UserNameDefaultProps ,UserNameDefaultValue}
export type { FormElementUserNameModel, UserNameValue }
