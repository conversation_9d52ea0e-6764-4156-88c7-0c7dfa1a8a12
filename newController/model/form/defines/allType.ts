import { FormElementSignatureModel } from './FormSignature'
import type { FormElementNumberModel } from './FormNumber'
import { AddressValue, FormElementAddressModel } from './FormAddress'
import { FormElementEmailAddressModel } from './FormEmailAddress'
import { CurrencyValue, FormElementCurrencyModel } from './FormCurrency'
import { DateValue, FormElementDateModel } from './FormDate'
import { FormElementDropdownListModel } from './FormDropdownList'
import { FormElementHeadingModel } from './FormHeading'
import { FormElementLineSeparatorModel } from './FormLineSeparator'
import { FormElementMultiSelectionModel } from './FormMultiSelection'
import { FormElementMultiLineTextModel } from './FormMultiLineText'
import { FormElementImageModel } from './FormImage'
import { FormElementPhoneNumberModel, PhoneNumberValue } from './FormPhoneNumber'
import { FileUploadValue, FormElementFileUploadModel } from './FormFileUpload'
import { FormElementPageBreakModel } from './FormPageBreak'
import { FormElementUserNameModel, UserNameValue } from './FormUserName'
import { FormElementSingleLineTextModel } from './FormSingleLineText'
import { FormElementSingleSelectionModel } from './FormSingleSelection'
import { FormElementPageModel } from '@model/form/defines/FormPage'
import { FormViewModel } from '@model/form/defines/formViewModel'
import { FormElementParagraphModel } from '@model/form/defines/FormParagraph'
import {FormElementType} from "@model/form/defines/shared";

type FormElementSelectionModels =
  FormElementMultiSelectionModel
  | FormElementDropdownListModel
  | FormElementSingleSelectionModel

type FormAnyElementViewModel =
  FormElementNumberModel
  | FormElementAddressModel
  | FormElementEmailAddressModel
  | FormElementCurrencyModel
  | FormElementDateModel
  | FormElementDropdownListModel
  | FormElementHeadingModel
  | FormElementLineSeparatorModel
  | FormElementMultiSelectionModel
  | FormElementMultiLineTextModel
  | FormElementImageModel
  | FormElementPhoneNumberModel
  | FormElementPageBreakModel
  | FormElementUserNameModel
  | FormElementSignatureModel
  | FormElementSingleLineTextModel
  | FormElementSingleSelectionModel
  | FormElementFileUploadModel
 | FormElementPageModel
| FormElementParagraphModel

type FormAnyViewModel = FormAnyElementViewModel | FormViewModel
type FormElementAnyInputModel = FormElementNumberModel
  | FormElementAddressModel
  | FormElementEmailAddressModel
  | FormElementCurrencyModel
  | FormElementDateModel
  | FormElementDropdownListModel
  | FormElementMultiSelectionModel
  | FormElementMultiLineTextModel
  | FormElementPhoneNumberModel
  | FormElementUserNameModel
  | FormElementSignatureModel
  | FormElementSingleLineTextModel
  | FormElementSingleSelectionModel
  | FormElementFileUploadModel
type FormAllConditionSupportedElements = FormElementDropdownListModel |
  FormElementDateModel
| FormElementCurrencyModel
| FormElementSingleSelectionModel
| FormElementMultiSelectionModel
| FormElementNumberModel

type FormAnyElementValue = string | number | string[] | PhoneNumberValue | CurrencyValue | AddressValue | DateValue | UserNameValue | FileUploadValue[];


type HasOptionsElement = FormElementMultiSelectionModel | FormElementSingleSelectionModel | FormElementDropdownListModel


export type {
  HasOptionsElement,
  FormAnyElementViewModel, FormElementSelectionModels, FormAnyViewModel,
  FormElementAnyInputModel,FormAllConditionSupportedElements,
  FormAnyElementValue
}