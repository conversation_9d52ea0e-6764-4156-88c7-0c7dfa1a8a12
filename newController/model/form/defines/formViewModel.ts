import {DueTimeFrameType} from 'isdk/src/proto/generated/DueTimeFrameType'
import {FormElementPageModel} from '@model/form/defines/FormPage'
import {FormElementType, Timestamp, UserId} from '@model/form/defines/shared'
import {CBoardUser} from '@controller/defines/CBoardUser'


interface FormViewModel {
    type: FormElementType;
    title?: string;
    description?: string;
    dueDate?: number;
    dueInTimeframe?: DueTimeFrameType;
    excludeWeekends?: boolean;
    templateName?: string;
    templateDescription?: string;
    transactionSequence?: number;
    boardId?: string;
    flowStepId?: string;
    version: string;
    /**
     * Represents the version number used by the server to extract data,
     * in order to handle cases where the same property name has different meanings in different versions.
     */
    extractionVersion: string;
    pages: FormElementPageModel[];
    /**
     * A value of true indicates that the form is empty (prepare case).
     */
    isEmptyForm: boolean;
    isPasswordProtected?: boolean;
    isIntegrationForm?: boolean;
    /**
     * creator can see the value of the protected element, but in flow the creator is engine not real user.
     * so we need save the creator when create the form action
     */
    creator?: UserId;
    /**
     * The ownerIds include below users.
     * 1. form creator
     * 2. flow creator
     * 3. board owner
     */
    ownerIds?: UserId[];
    /**
     * Set to true to indicate that the current action is a template.
     */
    isTemplate: boolean;
    isCompleted?: boolean;
    hasChangeAfterLastView?: boolean;
    updatedTime?: Timestamp;
    submittedAssigneeId?: UserId;
    assigneeIds?: UserId[];
    assignees?: CBoardUser[];
    /**
     * submit form need this info
     */
    actionId?: string;
    isPDFForm?: boolean;
    /**
     * for preview
     */
    downloadPDFUrl?: string;
    downloadMaskedPDFUrl?: string;
    downloadCSVUrl?: string;
    downloadMaskedCSVUrl?: string;
}

export type {
    FormViewModel
}
