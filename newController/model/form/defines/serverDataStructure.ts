import { FormElementType } from './shared'

export interface FieldCondition {
  enable: boolean;
  visible: boolean;
  rule: boolean;
  rules: any[];
}

export interface PDFFormSpecifics {
  flags: number;
  height: number;
  name: string;
  width: number;
  x: number;
  y: number;
  pdfFormFieldId: string;
  exportValue: string;
  defaultValue: string;
}

export interface FieldSpecific {
  isProtected: boolean;
  hideLabel: boolean;
  placeholder: string;
  supporting: string;
  required: boolean;
  readonly: boolean;
  size: string;
  imageUUID?: string;
  imageName?: string;
  defaultFromProfile: boolean;
  condition?: FieldCondition | null;
  currentDate: boolean;
  currentTime: boolean;
  timeFormat: string;
  disableAutoFill: boolean;
  options: string[];
  enableDDR?: boolean;
  enableImage?: boolean;
  imageAlt?: string;
  pdfFormFieldSpecifics?: PDFFormSpecifics[];
  format: string;
  precision: number;
  minLength: string;
  maxLength: string;
  lockCountry: boolean;
  showAddressLineTwo: boolean;
  showCity: boolean;
  showState: boolean;
  showZipcode: boolean;
  showCountry: boolean;
  dateFormat: string;
  withTime: boolean;
  withDate: boolean;
  maxFileSize: number;
  fileAccept: string;
  imageWidth: string;
  labelAlignment: string;
  imageAlignment: string;
  rowHeight: string;
  defaultCountry: string;
  showMiddleName: boolean;
  showPrefix: boolean;
  prefixOptions: string;
  showSuffix: boolean;
  enableDefaultValue:boolean;
  fontSize?: string;
  charSpace?: string;
}

export type FieldSpecificKeys = Exclude<keyof FieldSpecific, 'condition'>[]

export interface FormCustomData {
  width: number;
  height: number;
  x: number;
  y: number;
  fontSize?: string;
  pageUUID?: string;
  letterSpacing?: string;
}

export interface FormField {
  type: FormElementType;
  uniqueName: string;
  id: string;
  name: string;
  label?: string;
  fieldSpecific: Partial<FieldSpecific>;
  value: unknown;
  defaultValue?: unknown;
  displayValue?: string;
  image?: string;
  imageAlt?: string;
  text?: string;
  customData?: FormCustomData;
  pdfFormFieldId?:string;
  lineOneLabel?: string;
  lineTwoLabel?: string;
  cityLabel?: string;
  stateLabel?: string;
  zipCodeLabel?: string;
  countryLabel?: string;
}

export interface FormPage {
  id: string;
  name: string;
  sequence?: number;
  label?: string;
  creator?: string;
  fields: FormField[];
  type: FormElementType;
  pageNumber?: number;
  fieldSpecific: Partial<FieldSpecific>;
  customData?: Partial<FormCustomData>;
}

export interface FormModel {
  type: FormElementType;
  creator?: string;
  version?: string;
  extractionVersion?: string;
  data: {
    pages: FormPage[];
  };
  isEmptyForm: boolean;
  isPDFForm?: boolean;
}
