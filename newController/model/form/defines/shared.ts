import {ConditionRuleViewModel, ElementConditionViewModel} from '@model/form/defines/condition'
import {FormPreprocessor} from '@model/form/factory/FormPreprocessor'
import {ActionScene} from '@views/newActions/common/types'
import {FormElementAnyInputModel} from '@model/form/defines/allType';

type FormElementId = string
type UserId = string
type TransactionStepSequence = number
type Timestamp = number
type FileUUID = string
type FileName = string
type ReferenceSequence = number

enum FormElementType {
    Date = 'Date',
    Address = 'Address',
    Currency = 'Currency',
    Number = 'Number',
    Page = 'Page',
    Image = 'Image',
    Signature = 'Signature',
    Heading = 'Heading',
        SingleLineText = 'SingleLineText',
    UserName = 'UserName',
    MultiSelection = 'MultiSelection',
    SingleSelection = 'SingleSelection',
    MultiLineText = 'MultiLineText',
    EmailAddress = 'EmailAddress',
    PhoneNumber = 'PhoneNumber',
    DropdownList = 'DropdownList',
    //compatible with old data, the value used old value
    LineSeparator = 'LineSeperator',
    PageBreak = 'PageBreak',
    Form = 'form',
    PDFForm = 'PdfForm',
    FileUpload = 'FileUpload',
    Paragraph = 'Paragraphs',
}

type FormElementAllType =
    FormElementType.Address
    | FormElementType.Currency
    | FormElementType.Number
    | FormElementType.Date
    | FormElementType.Page
    | FormElementType.Image
    | FormElementType.Signature
    | FormElementType.Heading
    | FormElementType.SingleLineText
    | FormElementType.UserName
    | FormElementType.MultiSelection
    | FormElementType.SingleSelection
    | FormElementType.MultiLineText
    | FormElementType.EmailAddress
    | FormElementType.PhoneNumber
    | FormElementType.DropdownList
    | FormElementType.LineSeparator
    | FormElementType.PageBreak
    | FormElementType.FileUpload
    | FormElementType.Paragraph

const FormAllDisplayElements = [FormElementType.Page, FormElementType.Image, FormElementType.PageBreak, FormElementType.LineSeparator, FormElementType.Heading]
const FormAllInputElements = [FormElementType.Number, FormElementType.UserName, FormElementType.MultiSelection,
    FormElementType.SingleSelection, FormElementType.DropdownList, FormElementType.Date, FormElementType.Signature,
    FormElementType.EmailAddress, FormElementType.PhoneNumber, FormElementType.FileUpload, FormElementType.Currency, FormElementType.SingleLineText, FormElementType.MultiLineText]

interface FormUIOption {
    editor: string;
    view: string;
}

enum AlignmentType {
    Left = 'eft',
    Center = 'center',
    Right = 'right',
}

const AlignmentOptions = Object.values(AlignmentType)


const FormElementUIProps = [
    'isVisible', 'defaultLabel', 'defaultPlaceholder'
]
const FormElementBaseInputProps = [
    'id', 'type', 'value', 'defaultValue', 'condition'
]
const FormElementBaseProps = ['id', 'type']

interface FormElementCreateOption {
    // the $t function in vue-i18n
    $t?: Function;
  isTemplate?:boolean
}

interface FormPDFFilePage {
    uuid: string;
    pageNumber: number;
    background: string;
}

interface FormPDFFile {
    pages: FormPDFFilePage[];
}

interface FormElementTransformOption {
    isCompleted?: boolean;
    isPDFForm?: boolean;
    /**
     * Image, Heading, Signature, ...etc component has image resource.
     * there need boardId, transactionSequence , viewToken to get image url
     */
    boardId: string;
    transactionSequence: number;
    viewToken?: string;
    userTimezone?: string;
    $t?: Function;
    pdfFile?: FormPDFFile;
}

interface FormElementTransformToServerOption {
    userTimezone?: string;
}

interface FormValidateOptions {
    // Setting it to true indicates that the validation is for the correctness of the template.
    isValidateTemplate?: boolean;
    enableDDR?: boolean;
    validatePage?: number;
    notSaveError?: boolean;
}

interface FormFactoryOption {
    preprocessor: FormPreprocessor;
    type: FormElementType;
}

const enum FormScenes {
    /**
     * preview
     */
    Preview,

    /**
     * current is Fill form case
     */
    FillForm,
    EditForm,
    CreateForm,
}


interface FormChangeValueOption extends FormElementTransformOption {
    changedProperty?: string;
}

interface FormSyncValueOption extends FormElementTransformOption {
    focusedElementId?: FormElementId;
    focusedProperty?: string;
    changedProperty?: string;
}

interface FormRuntimeOptions {
    /**
     * if true, the form will show DDR entry in input field
     */
    enableDDR: boolean;
    /**
     * if true, the form will show protected value for current user
     */
    showProtected: boolean;
    /**
     * Indicates the usage scenario that the current view is in
     */
    scenes: FormScenes;
    usedScene?: ActionScene;
    isTemplate:boolean;
    isPDFForm?: boolean;
    viewToken?: string;
    isMultipleAssignees?: boolean;
    isCompleted?: boolean;
}

interface FormValidateResult {
    errorElements: FormElementAnyInputModel[];
    /**
     * The invalid radio group/checkbox group has selectable elements on the next page.
     */
    nextPageHasRelatedElement?: boolean;
    isValid: boolean;
}

interface FormElementFactory<T, B, C> {
    create(option: FormElementCreateOption): T;

    toServerData(model: T, option?: FormElementTransformToServerOption): B;

    toViewModel(data: B, option: FormElementTransformOption): T;

    validate(model: T, options?: FormValidateOptions): Promise<any>;

    /**
     * clear user filled data
     * @param model
     */
    resetValue(model: T): void;

    uiOption(runTimeOptions: FormRuntimeOptions): FormUIOption;

    /**
     * When the user fills out the form, set a handler for a specific field,
     * and determine whether to synchronize the user data to the element’s view model
     * based on the specific requirements of the element.
     * @param model
     * @param value
     * @param option FormChangeValueOption
     */
    setValue(model: T, value: Partial<C>, option: Partial<FormChangeValueOption>): void;

    /**
     * When the user is filling out the form, and data from other users is subscribed and received,
     * it is necessary to determine which data needs to be synchronized based on whether
     * the current user has focus on the current element, and how to synchronize it.
     * @param model
     * @param value
     * @param option FormSyncValueOption
     */
    syncValue(model: T, value: C, option: FormSyncValueOption): void;
}

interface FormElementChangeFactory<T, B> {
    /**
     * Sometimes, when the user updates a UI value, but multiple data points need to be updated.
     * this method will return all value that need to be updated in once.
     *
     * @param model {T} - the model value is sync with server
     * @param localValue {B} - local value
     * @param option {FormElementTransformOption} - transform option
     * @param changedProperties {string} (optional) - the changed attribute name
     *
     * @return changed properties
     */
    setValue(model: T, localValue: B, option: FormElementTransformOption, changedProperties?: string): void;

    /**
     * Synchronize the data updated by other form fillers.
     * The element currently focused by the user should exclude withOutProperties.
     * @param model
     * @param localValue
     * @param withOutProperties
     */
    syncValue(model: T, localValue: B, withOutProperties?: string): void;
}

interface FormConditionElementFactory<T> {
    meetExpected(model: T, rule: ConditionRuleViewModel): boolean;
}


interface FormAttachmentBaseInfo {
    stepSequence: number;
    elementId: string;
    referenceSequence: ReferenceSequence;
}

export type {
    FormElementAllType,
    UserId,
    FormUIOption,
    Timestamp,
    FormElementId,
    FormElementFactory,
    FormElementCreateOption,
    FormElementTransformOption,
    FormRuntimeOptions,
    FormElementTransformToServerOption,
    FileName,
    FileUUID,
    ReferenceSequence,
    FormAttachmentBaseInfo,
    FormConditionElementFactory,
    FormElementChangeFactory,
    FormChangeValueOption,
    FormFactoryOption,
    FormPDFFile,
    FormValidateOptions,
    FormSyncValueOption,
    FormValidateResult,
    TransactionStepSequence
}
export {
    FormScenes,
    AlignmentType,
    FormElementType,
    FormElementBaseInputProps,
    FormElementBaseProps,
    FormElementUIProps,
    AlignmentOptions,
    FormAllDisplayElements,
    FormAllInputElements
}
