import {
  Form<PERSON>hangeValueOption,
  FormElementCreateOption,
  FormElementFactory, FormElementTransformOption,
  FormUIOption, FormValidateOptions
} from '../defines/shared'
import {processLangResource, removeDDRSource} from './shared'
import uuid from 'uuid/v4'
import { FormField } from '@model/form/defines/serverDataStructure'
import {
  AddressDefaultProps, AddressDefaultValue, AddressValue,AddressSpecialKeys,
  type FormElementAddressModel
} from '@model/form/defines/FormAddress'
import { FormErrorType } from '@model/form/defines/base'
import cloneDeep from 'lodash/cloneDeep'
import {
  buildValidateResult,
  setFormElementValue,
  syncFormElementObjectValue,
  validateFieldMaxLength
} from '@model/form/factory/utils'
import {
  createDefaultValueTransform,
  getAttrsFromFormField,
  getAttrsFromViewModel
} from '@model/form/common/utils'

class AddressFactory implements FormElementFactory<FormElementAddressModel, FormField, AddressValue> {
  create (option: FormElementCreateOption): FormElementAddressModel {
    const result = {
      ...cloneDeep(AddressDefaultProps),
      id: uuid()
    }
    if (option.$t) {
      processLangResource(result, option.$t)
    }
    return result
  }

  toServerData (model: FormElementAddressModel): FormField {
    return getAttrsFromViewModel(model,AddressDefaultProps, AddressSpecialKeys) as FormField
  }

  toViewModel (data: FormField, option?: FormElementTransformOption): FormElementAddressModel {
    const applyDefaultValue = option?.isCompleted !== true
    const transforms = {}
    if(applyDefaultValue){
      transforms['value'] = createDefaultValueTransform(AddressDefaultValue)
    }
    return getAttrsFromFormField(data,AddressDefaultProps,AddressSpecialKeys,{transforms}) as FormElementAddressModel
  }
  

  validate (model: FormElementAddressModel, option: FormValidateOptions): Promise<boolean> {
    model.errors = []
    const errors = []
    if (model.required) {
      if (!model.value.addressLineOne) {
        errors.push({
          field: 'addressLineOne',
          errorType: FormErrorType.Required
        })
      }

      if (model.showCity && !model.value.city) {
        errors.push({
          field: 'city',
          errorType: FormErrorType.Required
        })
      }
      if (model.showCountry && !model.value.countryCode) {
        errors.push({
          field: 'countryCode',
          errorType: FormErrorType.Required
        })
      }
      if (model.showState && !model.value.state) {
        errors.push({
          field: 'state',
          errorType: FormErrorType.Required
        })
      }
      if (model.showZipcode && !model.value.zipcode) {
        errors.push({
          field: 'zipcode',
          errorType: FormErrorType.Required
        })
      }
    }
    const maxLengthMap = {
      addressLineOne: 300,
      addressLineTwo: 300,
      city: 200,
      state: 200,
      zipcode: 10
    }
    validateFieldMaxLength(model, maxLengthMap, errors, option)

    return buildValidateResult(model, errors, option)
  }

  resetValue (model: FormElementAddressModel) {
    model.value = {
      ...cloneDeep(AddressDefaultValue)
    }
  }

  uiOption (): FormUIOption {
    return {
      view: 'FormAddressView',
      editor: 'FormAddressOption'
    }
  }

  setValue (model: FormElementAddressModel, value: AddressValue, option: FormChangeValueOption): void {
    setFormElementValue(model, value, option)
  }

  syncValue (model: FormElementAddressModel, value: AddressValue, option: FormChangeValueOption): void {
    syncFormElementObjectValue(model, value, option)
  }
}

export {
  AddressFactory
}