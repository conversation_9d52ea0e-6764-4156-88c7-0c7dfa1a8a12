import {
  Form<PERSON>hangeValueOption,
  FormConditionElementFactory,
  FormElementCreateOption,
  FormElementFactory, FormElementTransformOption,
  FormUIOption, FormValidateOptions
} from '../defines/shared'
import {CurrencyDefaultProps, CurrencyDefaultValue, CurrencyValue, CurrencySpecialKeys} from '../defines/FormCurrency'
import type { FormElementCurrencyModel } from '../defines/FormCurrency'
import { processLangResource } from './shared'
import uuid from 'uuid/v4'
import {  FormField } from '@model/form/defines/serverDataStructure'
import { FormErrorType } from '@model/form/defines/base'
import cloneDeep from 'lodash/cloneDeep'
import { ConditionRuleViewModel } from '@model/form/defines/condition'
import { isNumberValueMeetExpected } from '@model/form/common/condition'
import { buildValidateResult, setFormElementValue, syncFormElementObjectValue } from '@model/form/factory/utils'
import {
  getAttrsFromFormField,
  getAttrsFromViewModel,
  ModelAttrTransforms,
  createDefaultValueTransform
} from '@model/form/common/utils'



class CurrencyFactory implements FormElementFactory<FormElementCurrencyModel, FormField, CurrencyValue>, FormConditionElementFactory<FormElementCurrencyModel> {
  create (option: FormElementCreateOption): FormElementCurrencyModel {
    const result = {
      ...cloneDeep(CurrencyDefaultProps),
      id: uuid()
    }
    if (option.$t) {
      processLangResource(result, option.$t)
    }
    return result
  }

  toServerData (model: FormElementCurrencyModel): FormField {
    return getAttrsFromViewModel(model,CurrencyDefaultProps, CurrencySpecialKeys) as FormField
  }

  toViewModel (data: FormField, option?: FormElementTransformOption): FormElementCurrencyModel {
    const applyDefaultValue = option?.isCompleted !== true
    const transforms: ModelAttrTransforms<FormField> = {}
    if(applyDefaultValue){
      transforms['value'] = createDefaultValueTransform(CurrencyDefaultValue)
    }
    return getAttrsFromFormField(data,CurrencyDefaultProps, CurrencySpecialKeys, {transforms,applyDefaultValue}) as FormElementCurrencyModel
  }

  validate (model: FormElementCurrencyModel, option: FormValidateOptions): Promise<boolean> {
    const {amount, code} = model.value
    model.errors = []
    const errors = []
    if (model.required && !amount) {
      errors.push({
        field: 'amount',
        errorType: FormErrorType.Required
      })
    }
    if (model.required && !code) {
      errors.push({
        field: 'code',
        errorType: FormErrorType.Required
      })
    }
    if (amount) {
      const amountNumber = parseFloat(amount)
      if (model.maxLength) {
        if (parseInt(model.maxLength, 10) < amountNumber) {
          errors.push({
            field: 'amount',
            errorType: FormErrorType.Limit
          })
        }
      }
      if (model.minLength) {
        if (parseInt(model.minLength, 10) > amountNumber) {
          errors.push({
            field: 'amount',
            errorType: FormErrorType.Limit
          })
        }
      }

      const precision = model.precision
      if (!isNaN(precision)) {
        const precisionRegEx = new RegExp(`^(?:-?\\d*\.\\d{0,${precision}}|\\d+)$`)
        if (!precisionRegEx.test(amount)) {
          errors.push({
            field: 'amount',
            errorType: FormErrorType.Precision,
            params: { precision }
          })
        }
      }
    }
    return buildValidateResult(model, errors, option)
  }

  resetValue (model: FormElementCurrencyModel) {
    model.value = {
      ...cloneDeep(CurrencyDefaultValue)
    }
  }

  uiOption (): FormUIOption {
    return {
      view: 'FormCurrencyView',
      editor: 'FormCurrency'
    }
  }

  meetExpected (model: FormElementCurrencyModel, rule: ConditionRuleViewModel): boolean {
    return isNumberValueMeetExpected(rule, model.value.amount)
  }

  setValue (model: FormElementCurrencyModel, value: CurrencyValue, option: FormChangeValueOption): void {
    setFormElementValue(model, value, option)
  }

  syncValue (model: FormElementCurrencyModel, value: CurrencyValue, option: FormChangeValueOption): void {
    syncFormElementObjectValue(model, value, option)
  }
}

export {
  CurrencyFactory
}