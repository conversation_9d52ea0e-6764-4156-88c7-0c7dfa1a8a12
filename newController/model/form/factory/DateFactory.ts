import {
  Form<PERSON><PERSON>eV<PERSON>ueOption,
  FormConditionElementFactory,
  FormElementCreateOption,
  FormElementFactory,
  FormElementTransformOption,
  FormElementTransformToServerOption,
  FormRuntimeOptions,
  FormUIOption, FormValidateOptions
} from '../defines/shared'
import {
  Date<PERSON>ttr<PERSON>lias,
  DateDefaultProps,
  DateDefaultVaue, DateSpecialKeys,
  DateToModelTransforms,
  DateToServerTransforms,
  DateValue
} from '../defines/FormDate'
import { processLangResource } from './shared'
import uuid from 'uuid/v4'
import { FormElementDateModel } from '@model/form/defines/FormDate'
import {  FormField } from '@model/form/defines/serverDataStructure'

import { FormErrorType } from '@model/form/defines/base'
import moment from 'moment-timezone'
import cloneDeep from 'lodash/cloneDeep'
import { ConditionRuleViewModel } from '@model/form/defines/condition'
import { isNumberValueMeetExpected } from '@model/form/common/condition'
import { getUserTimezone, buildValidateResult, syncFormElementObjectValue } from '@model/form/factory/utils'
import {
  getAttrsFromFormField,
  getAttrsFromViewModel,
  ModelAttrTransforms,
  createDefaultValueTransform
} from '@model/form/common/utils'

/**
 * base on dateStr, hour, minute, AP/PM to calculation below attributes:
 * timestamp (with hour, minute)
 * dayTimestamp - the day start timestamp for
 * @param model
 */
function calculateDateValueFromDateStr(val: DateValue, model: FormElementDateModel){
  const timezone = getUserTimezone()

  const { hour, minute, timeFormat, dateStr, timestamp: oldTimestamp } = val
  let theDateStr = dateStr
  if (!theDateStr && oldTimestamp) {
    //Compatibility with legacy data.
    theDateStr = moment.tz(oldTimestamp, timezone).format(model.dateFormat)
  }
  const dateFormat = model.dateFormat
  let dateAndTimeStr = theDateStr
  let dateAndTimeFormat = dateFormat
  if (model.withTime && hour && minute) {
    dateAndTimeFormat = `${dateFormat} hh:mm`
    dateAndTimeStr = `${theDateStr} ${hour}:${minute}`
    if (!model.enable24Hour) {
      dateAndTimeFormat = `${dateFormat} hh:mm A`
      dateAndTimeStr = `${theDateStr} ${hour}:${minute} ${timeFormat}`
    }
  }

  const timestamp = moment.tz(dateAndTimeStr, dateAndTimeFormat, timezone).valueOf()
  const dayTimestamp = moment.tz(theDateStr,dateFormat, timezone).valueOf()

  const dateValue: DateValue = {
    hour,
    minute,
    timeFormat,
    dateStr: theDateStr,
    timestamp,
    dayTimestamp
  }
  return dateValue
}

class DateFactory
  implements
    FormElementFactory<FormElementDateModel, FormField, DateValue>,
    FormConditionElementFactory<FormElementDateModel> {
  create (option: FormElementCreateOption): FormElementDateModel {
    const result = {
      ...cloneDeep(DateDefaultProps),
      id: uuid()
    }
    if (option.$t) {
      processLangResource(result, option.$t)
    }
    return result
  }

  toServerData (model: FormElementDateModel, option: FormElementTransformToServerOption): FormField {
    const transforms: ModelAttrTransforms<FormElementDateModel> = {
      value: calculateDateValueFromDateStr,
      ...DateToServerTransforms,
    }
    const field= getAttrsFromViewModel(model, DateDefaultProps, DateSpecialKeys,{transforms,alias:DateAttrAlias}) as FormField
    return field
  }

  toViewModel (data: FormField, option?: FormElementTransformOption): FormElementDateModel {
    const transforms = {
     ...DateToModelTransforms
    }
    const applyDefaultValue = option?.isCompleted !== true
    if(applyDefaultValue){
      transforms['value'] = createDefaultValueTransform(DateDefaultVaue)
    }

    return getAttrsFromFormField(data, DateDefaultProps, DateSpecialKeys,{
      alias: DateAttrAlias,
      transforms}) as FormElementDateModel
  }

  validate (model: FormElementDateModel, option?:FormValidateOptions): Promise<boolean> {
    model.errors = []
    const errors = []
    if(option?.isValidateTemplate){
      return Promise.resolve(true)
    }
    if (model.withTime) {
      if (model.required || model.value.hour || model.value.minute) {
        if (model.required && (!model.value.hour || !model.value.minute)) {
          errors.push({
            field: model.value.hour ? 'minute' : 'hour',
            errorType: FormErrorType.Required
          })
        }
        if (!!model.value.hour !== !!model.value.minute) {
          errors.push({
            field: model.value.hour ? 'minute' : 'hour',
            errorType: FormErrorType.InvalidTime
          })
        }
        const hour = model.value.hour ? parseInt(model.value.hour): ''
        const minute = model.value.minute? parseInt(model.value.minute): ''
        if (model.enable24Hour) {
          if (hour < 0 || hour > 23) {
            errors.push({
              field: 'hour',
              errorType: FormErrorType.InvalidTime
            })
          }
        } else {
          if (hour < 1 || hour > 12) {
            errors.push({
              field: 'hour',
              errorType: FormErrorType.InvalidTime
            })
          }
        }
        if (minute < 0 || minute > 60) {
          errors.push({
            field: 'minute',
            errorType: FormErrorType.InvalidTime
          })
        }
      }
    }

    if (model.required && model.withDate) {
      if (!model.value.dateStr) {
        errors.push({
          field: 'dateStr',
          errorType: FormErrorType.Required
        })
      }
    }
    return buildValidateResult(model, errors, option)
  }

  resetValue (model: FormElementDateModel) {
    model.value = {
      ...cloneDeep(DateDefaultVaue)
    }
    model.displayValue = ''
  }

  uiOption (runTimeOptions: FormRuntimeOptions): FormUIOption {
    if (runTimeOptions.isPDFForm) {
      return {
        view: 'PDFFormDateView',
        editor: 'FormElementDateSetting'
      }
    }
    return {
      view: 'FormDateView',
      editor: 'FormDateOptionView'
    }
  }

  meetExpected (model: FormElementDateModel, rule: ConditionRuleViewModel): boolean {
    let timestamp = model.value.timestamp
    if (timestamp) {
      timestamp = moment(moment(timestamp).format('YYYY-MM-DD')).valueOf()
    }
    return isNumberValueMeetExpected(rule, timestamp)
  }

  setValue (model: FormElementDateModel, value: DateValue, option?: FormChangeValueOption): void {
    const changedProperty = option?.changedProperty
    if (changedProperty) {
      const dateFormat = model.dateFormat
      const { dateStr, hour, minute, timeFormat } = value
      const theDateStr = dateStr
      let dateAndTimeStr = dateStr
      let dateAndTimeFormat = dateFormat
      if (model.withTime && hour && minute) {
        dateAndTimeFormat = `${dateFormat} hh:mm`
        dateAndTimeStr = `${theDateStr} ${hour}:${minute}`
        if (!model.enable24Hour) {
          dateAndTimeFormat = `${dateFormat} hh:mm A`
          dateAndTimeStr = `${theDateStr} ${hour}:${minute} ${timeFormat}`
        }
      }
      const timezone = getUserTimezone()

      const timestamp = moment.tz(dateAndTimeStr, dateAndTimeFormat, timezone).valueOf()
      const dayTimestamp = moment.tz(theDateStr, timezone).valueOf()

      model.value = {
        dateStr,
        hour,
        minute,
        timeFormat,
        timestamp,
        dayTimestamp
      }
    } else {
      throw new Error('You need to specify the property to be updated.')
      // model.value = cloneDeep(value)
    }
  }

  syncValue (model: FormElementDateModel, value: DateValue, option: FormChangeValueOption): void {
    syncFormElementObjectValue(model, value, option)
  }
}

export { DateFactory }
