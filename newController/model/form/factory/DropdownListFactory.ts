import {
  Form<PERSON><PERSON>eValueOption,
  FormConditionElementFactory,
  FormElementCreateOption,
  FormElementFactory,
  FormElementTransformOption,
  FormRuntimeOptions, FormSyncValueOption,
  FormUIOption, FormValidateOptions
} from '../defines/shared'
import { processLangResource } from './shared'
import uuid from 'uuid/v4'
import { FormField } from '@model/form/defines/serverDataStructure'

import {
  FormElementDropdownListModel,
  DropdownListDefaultProps,
  DropdownListSpecialKeys
} from '@model/form/defines/FormDropdownList'
import { FormErrorType } from '@model/form/defines/base'
import cloneDeep from 'lodash/cloneDeep'
import { ConditionRuleViewModel } from '@model/form/defines/condition'
import { isStringValueMeetExpected } from '@model/form/common/condition'
import {
  createDefaultValueTransform,
  getAttrsFromFormField,
  getAttrsFromViewModel,
  ModelAttrTransforms
} from '@model/form/common/utils';
import { buildValidateResult } from '@model/form/factory/utils'

class DropdownListFactory
  implements
    FormElementFactory<FormElementDropdownListModel, FormField, string>,
    FormConditionElementFactory<FormElementDropdownListModel> {
  create(option: FormElementCreateOption): FormElementDropdownListModel {
    const result = {
      ...cloneDeep(DropdownListDefaultProps),
      id: uuid()
    }
    if (option.$t) {
      processLangResource(result, option.$t)
    }
    return result
  }

  toServerData(model: FormElementDropdownListModel): FormField {
    return getAttrsFromViewModel(model, DropdownListDefaultProps, DropdownListSpecialKeys) as FormField
  }

  toViewModel(data: FormField, option?: FormElementTransformOption): FormElementDropdownListModel {
    const applyDefaultValue = option?.isCompleted !== true
    const transforms: ModelAttrTransforms<FormField> = {}

    if(applyDefaultValue){
      transforms['value'] = createDefaultValueTransform('')
    }
    return getAttrsFromFormField(data, DropdownListDefaultProps, DropdownListSpecialKeys, {transforms}) as FormElementDropdownListModel
  }

  validate(model: FormElementDropdownListModel, option: FormValidateOptions ={}): Promise<boolean> {
    model.errors = []
    const errors = []
    if(!option.isValidateTemplate){
      if (model.required && !model.value) {
        errors.push({
          field: '',
          errorType: FormErrorType.Required
        })
      }
    }

    return buildValidateResult(model, errors, option)

  }

  resetValue(model: FormElementDropdownListModel) {
    model.value = ''
  }

  uiOption(runtimeOptions: FormRuntimeOptions): FormUIOption {
    if (runtimeOptions.isPDFForm) {
      return {
        view: 'PDFFormDropdownListView',
        editor: 'FormDropdownListSetting'
      }
    }
    return {
      view: 'FormDropdownListView',
      editor: 'FormDropdownList'
    }
  }

  meetExpected(model: FormElementDropdownListModel, rule: ConditionRuleViewModel): boolean {
    return isStringValueMeetExpected(rule, model.value)
  }

  setValue(
    model: FormElementDropdownListModel,
    value: string,
    option: FormChangeValueOption
  ): void {
    if(model.options.find(item => item.value === value)){
      model.value = value
    }
  }

  syncValue(
    model: FormElementDropdownListModel,
    value: string,
    option: FormSyncValueOption
  ): void {

      model.value = value

  }
}

export { DropdownListFactory }
