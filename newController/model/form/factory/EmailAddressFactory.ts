import {
  FormChangeValueOption,
  FormElement<PERSON>reateOption,
  FormElementFactory,
  FormElementTransformOption,
  FormUIOption, FormValidateOptions
} from '../defines/shared'
import { processLangResource } from './shared'
import uuid from 'uuid/v4'
import { Form<PERSON>ield } from '@model/form/defines/serverDataStructure'
import {
  FormElementEmailAddressModel,
  EmailAddressDefaultProps,
  EmailAddressSpecialKeys, EmailAddressAlias
} from '@model/form/defines/FormEmailAddress'
import { FormErrorType } from '@model/form/defines/base'
import cloneDeep from 'lodash/cloneDeep'
import {syncFormElementSampleValue, validateFieldMaxLength} from '@model/form/factory/utils'
import {
  createDefaultValueTransform,
  getAttrsFromFormField,
  getAttrsFromViewModel,
  ModelAttrTransforms
} from '@model/form/common/utils';
import { buildValidateResult } from '@model/form/factory/utils'

class EmailAddressFactory
  implements FormElementFactory<FormElementEmailAddressModel, FormField, string> {
  create (option: FormElementCreateOption): FormElementEmailAddressModel {
    const result = {
      ...cloneDeep(EmailAddressDefaultProps),
      id: uuid()
    }
    if (option.$t) {
      processLangResource(result, option.$t)
    }
    return result
  }

  toServerData (model: FormElementEmailAddressModel): FormField {
    return getAttrsFromViewModel(model, EmailAddressDefaultProps, EmailAddressSpecialKeys, {alias: EmailAddressAlias}) as FormField
  }

  toViewModel (data: FormField, option?: FormElementTransformOption): FormElementEmailAddressModel {
    const applyDefaultValue = option?.isCompleted !== true
    const transforms: ModelAttrTransforms<FormField> = {}

    if(applyDefaultValue){
      transforms['value'] = createDefaultValueTransform('')
    }
    return getAttrsFromFormField(data,EmailAddressDefaultProps, EmailAddressSpecialKeys,{transforms, alias: EmailAddressAlias}) as FormElementEmailAddressModel
  }

  validate (model: FormElementEmailAddressModel,option: FormValidateOptions): Promise<boolean> {
    model.errors = []
    const errors = []
    if (model.required && !model.value) {
      errors.push({
        field: '',
        errorType: FormErrorType.Required
      })
    }
    const maxLengthMap = {
      value: 350
    }
    validateFieldMaxLength(model, maxLengthMap,errors, option)
    return buildValidateResult(model, errors, option)

  }

  resetValue (model: FormElementEmailAddressModel) {
    model.value = ''
  }

  uiOption (): FormUIOption {
    return {
      view: 'FormEmailAddressView',
      editor: 'FormEmailAddress'
    }
  }

  setValue (
    model: FormElementEmailAddressModel,
    value: string,
    option: FormChangeValueOption
  ): void {
    model.value = value
  }

  syncValue (
    model: FormElementEmailAddressModel,
    value: string,
    option: FormChangeValueOption
  ): void {
    syncFormElementSampleValue(model, value, option)
  }
}

export { EmailAddressFactory }
