import {
  Form<PERSON><PERSON><PERSON>V<PERSON>ueOption,
  FormElement<PERSON>reateOption,
  FormElementFactory,
  FormElementTransformOption,
  FormElementType,
  FormUIOption, FormValidateOptions
} from '@model/form/defines/shared'
import { FormField } from '@model/form/defines/serverDataStructure'
import uuid from 'uuid/v4'
import { processLangResource } from '@model/form/factory/shared'
import { buildValidateResult } from '@model/form/factory/utils'

import {
  FormElementFileUploadModel,
  FileUploadDefaultProps,
  FileUploadSpecialKeys,
  FileUploadValue
} from '@model/form/defines/FormFileUpload'
import cloneDeep from 'lodash/cloneDeep'

import {
  getAttrsFromFormField,
  getAttrsFromViewModel,
  ModelAttrTransforms,
} from '@model/form/common/utils';
import {FormErrorType} from "@model/form/defines/base";

class FileUploadFactory
  implements FormElementFactory<FormElementFileUploadModel, FormField, FileUploadValue[]> {
  create (option: FormElementCreateOption): FormElementFileUploadModel {
    const result: FormElementFileUploadModel = {
      ...cloneDeep(FileUploadDefaultProps),
      type: FormElementType.FileUpload,
      id: uuid()
    }
    if (option.$t) {
      processLangResource(result, option.$t)
    }
    return result as FormElementFileUploadModel
  }

  toServerData (model: FormElementFileUploadModel): FormField {
    return getAttrsFromViewModel(model, FileUploadDefaultProps, FileUploadSpecialKeys) as FormField
  }

  toViewModel (data: FormField, option: FormElementTransformOption): FormElementFileUploadModel {
    const isCompleted = option?.isCompleted
    const transforms: ModelAttrTransforms<FormField> = {}

    if(!isCompleted){
      transforms['value'] = function (){
        /**
         * in 10.0 changed the value to computed value from references.
         * for old data in value we need clear.
         */
        return []
      }
    }
    return getAttrsFromFormField(data, FileUploadDefaultProps, FileUploadSpecialKeys, {transforms}) as FormElementFileUploadModel
  }

  validate (model: FormElementFileUploadModel, option?: FormValidateOptions): Promise<boolean> {
    if(option?.isValidateTemplate){
      return Promise.resolve(true)
    }
    model.errors = []
    const errors = []
    if(model.required){
      if(model.value.length <= 0){
        errors.push({
          field: '',
          errorType: FormErrorType.Required
        })
      }
    }
    if(model.maxFileCount && model.value.length > model.maxFileCount){
      errors.push({
        field: '',
        errorType: FormErrorType.MaxLimit,
        params: {
          count: model.maxFileCount
        }
      })
    }
    return buildValidateResult(model, errors, option)
  }

  resetValue (model: FormElementFileUploadModel) {
    model.value = []
  }

  uiOption (): FormUIOption {
    return {
      view: 'FormFileUploadView',
      editor: 'FormFileUploadOption'
    }
  }

  setValue (
    model: FormElementFileUploadModel,
    value: FileUploadValue[],
    option: FormChangeValueOption
  ): void {
    model.value = Array.from(value)
  }

  syncValue (
    model: FormElementFileUploadModel,
    value: FileUploadValue[],
    option: FormChangeValueOption
  ): void {
    model.value = Array.from(value)
  }
}

export { FileUploadFactory }
