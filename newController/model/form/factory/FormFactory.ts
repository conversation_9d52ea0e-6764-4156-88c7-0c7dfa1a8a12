import {
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ueOption,
  FormE<PERSON><PERSON>reateOption,
  FormElementFactory,
  FormElementTransformOption,
  FormElementTransformToServerOption,
  FormElementType,
  FormFactoryOption,
  FormUIOption,
  FormValidateOptions,
  FormValidateResult
} from '@model/form/defines/shared'
import { getFormElementFactory, isDefine } from '@model/form/factory/shared'
import { FormViewModel } from '@model/form/defines/formViewModel'

import { FormModel, FormPage } from '@model/form/defines/serverDataStructure'
import { FormElementPageModel } from '@model/form/defines/FormPage'
import { FormPreprocessor } from '@model/form/factory/FormPreprocessor'
import { PageFactory } from '@model/form/factory/PageFactory'
import flatten from 'lodash/flatten'
import { FormElementAnyInputModel } from '@model/form/defines/allType'
import {FormCurrentVersion, PDFFormCurrentVersion} from '@model/form/data/common';

class FormFactory implements FormElementFactory<FormViewModel, FormModel, null> {
  private preprocessor: FormPreprocessor
  private type: FormElementType
  private pageFactory: PageFactory

  constructor(option?: FormFactoryOption) {
    if (option) {
      this.preprocessor = option.preprocessor
      this.pageFactory = new PageFactory(option)
      this.type = option.type
    } else {
      this.pageFactory = getFormElementFactory(FormElementType.Page) as PageFactory
      this.type = FormElementType.Form
    }
  }

  getPageFactory(): PageFactory {
    return this.pageFactory
  }

  create(option: FormElementCreateOption): FormViewModel {
    return {
      type: this.type,
      isTemplate: option.isTemplate,
      pages: [],
      isEmptyForm: true,
      version: (this.type === FormElementType.Form) ? FormCurrentVersion: PDFFormCurrentVersion,
      extractionVersion: ''
    }
  }

  toServerData(model: FormViewModel, option?: FormElementTransformToServerOption): FormModel {
    let isEmptyForm = true
    const preprocessor = this.preprocessor
    preprocessor && preprocessor.beforeFormToServerData(model)
    const pages = model.pages.map((page) => {
      if (page.elements.length) {
        isEmptyForm = false
      }
      const field = this.pageFactory.toServerData(page, option) as FormPage
      preprocessor && preprocessor.afterPageToServerData(field, page, option)
      return field
    })
    return {
      type: model.type,
      version: model.version,
      extractionVersion: model.extractionVersion || '',
      data: {
        pages
      },
      isEmptyForm,
      creator: model.creator
    }
  }

  /**
   *
   * @param model
   * @param option.applyDefaultValue apply default value to element value
   *  case 1: When converting a completed form, the user-entered values are stored in value, so defaultValue should not be applied
   *  case 2: When filling out or previewing the form, default values need to be applied
   */
  toViewModel(model: FormModel, option: FormElementTransformOption): FormViewModel {
    const pages: FormElementPageModel[] = []

    const preprocessor = this.preprocessor
    preprocessor && preprocessor.beforeFormToViewModel(model, {isCompleted: option.isCompleted})

    const formPages = model.data.pages

    formPages.forEach((element, index) => {
      if (!element.pageNumber) {
        element.pageNumber = index
      }
      preprocessor && preprocessor.beforePageToViewModel(element, option)

      const viewModel = this.pageFactory.toViewModel(element, option) as FormElementPageModel
      preprocessor && preprocessor.afterPageToViewModel(viewModel, element, option)
      pages.push(viewModel)
    })

    const formViewModel = {
      type: model.type,
      creator: model.creator,
      isEmptyForm: model.isEmptyForm,
      version: model.version,
      extractionVersion: model.extractionVersion,
      pages
    } as FormViewModel
    preprocessor && preprocessor.afterFormToViewModel(formViewModel, model)
    return formViewModel
  }

  validate(
    model: FormViewModel,
    option: FormValidateOptions
  ): Promise<boolean | FormValidateResult> {
    const elements = []
    if (isDefine(option.validatePage)) {
      const validatePage = model.pages[option.validatePage]
      elements.push(this.pageFactory.validate(validatePage, option))
    } else {
      model.pages.forEach((element) => {
        if (element.isVisible) {
          elements.push(this.pageFactory.validate(element, option))
        }
      })
    }
    return new Promise((resolve, reject) => {
      Promise.allSettled(elements).then((results) => {
        let errors = results
          .filter((result) => result.status === 'rejected')
          .map((result) => {
            //@ts-ignore
            return result.reason
          })
        if (errors.length > 0) {
          errors = flatten(errors) as FormElementAnyInputModel[]
        }
        const result = this.preprocessor.afterFormValidate(model, errors, option)
        if (!result.isValid) {
          reject(result)
        } else {
          resolve(true)
        }
      })
    })
  }

  resetValue(model: FormViewModel) {
    model.pages.forEach((element) => {
      const factory = getFormElementFactory(element.type)
      if (factory) {
        factory.resetValue(element)
      }
    })
  }

  uiOption(): FormUIOption {
    return {
      view: '',
      editor: ''
    }
  }

  setValue(model: FormViewModel, value: string, option: FormChangeValueOption): void {}

  syncValue(model: FormViewModel, value: string, option: FormChangeValueOption): void {}
}

export { FormFactory }
