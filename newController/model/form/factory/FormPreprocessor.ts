import { IFormPreprocessor } from '@model/form/factory/Preprocessor'
import { FormAnyElementViewModel, FormElementAnyInputModel,HasOptionsElement } from '@model/form/defines/allType'

import { FormField, FormModel, FormPage } from '@model/form/defines/serverDataStructure'
import { FormElementPageModel } from '@model/form/defines/FormPage'
import {
  conditionViewModelToServerData,
  fieldConditionToConditionViewModel
} from '@model/form/transform/conditions'
import { FormViewModel } from '@model/form/defines/formViewModel'
import {
  FormElementTransformOption,
  FormElementTransformToServerOption, FormElementType,
  FormValidateOptions,
  FormValidateResult
} from '@model/form/defines/shared'
import { upgradeFormData} from '@model/form/data/upgradeFormData';
import {getValidLabelsFromOptions, getValidValuesFromOptions} from "@model/form/common/utils";
import FormFactoryManager from "@model/form/factory/index";
import isString from "lodash/isString";
import { UpgradeOption } from '@model/form/data/common'

type OptionsElementValue = string | string[]
export function  needHandleOptions(type: FormElementType){
  return [FormElementType.MultiSelection,FormElementType.SingleSelection,FormElementType.DropdownList].includes(type)
}
export class FormPreprocessor implements IFormPreprocessor {
  afterElementToServerData (field: FormField, model: FormAnyElementViewModel) {
    const inputModel = model as FormElementAnyInputModel
    if (inputModel.condition) {
      if(!field.fieldSpecific){
        field.fieldSpecific = {}
      }
      field.fieldSpecific.condition = conditionViewModelToServerData(inputModel.condition)
    }
    /**
     * downgrade the options and value data
     */
    if(needHandleOptions(model.type)) {
      if (field.value) {
        field.value = getValidLabelsFromOptions(field.value as OptionsElementValue, model as HasOptionsElement)
      }
      field.fieldSpecific.options = (model as HasOptionsElement).options.map(item => item.label)
    }
  }

  afterElementToViewModel (model: FormAnyElementViewModel, field: FormField) {
    if (field.fieldSpecific?.condition) {
      model.condition = fieldConditionToConditionViewModel(field.fieldSpecific.condition)
    }
    let idIndex =  0
    function sanitizeString(input) {
      if (!isString(input)) {
        return input;
      }

      const MAX_LENGTH = 30;
      const languageRanges = [
        '\u0041-\u005A\u0061-\u007A\u0030-\u0039\u005F', // 基本拉丁字母(英文数字下划线)
        '\u4e00-\u9fff',   // 中文
        '\u3040-\u309f\u30a0-\u30ff', // 日文平假名/片假名
        '\uac00-\ud7a3',   // 韩文
        '\u0400-\u04FF',   // 西里尔字母(俄文等)
        '\u0600-\u06FF',   // 阿拉伯文
        '\u0900-\u097F',   // 梵文
        '\u0E00-\u0E7F',   // 泰文
        '\u0C80-\u0CFF',   // 卡纳达文
        '\u0590-\u05FF',   // 希伯来文
        '\u3000-\u303F',   // 中文标点符号(可选保留)
      ].join('');

      let str = input
          .replace(/\s+/g, '_') // 将空白字符替换为下划线
          .replace(new RegExp(`[^${languageRanges}]`, 'g'), ''); // 只保留指定语言字符

      if (str.length > MAX_LENGTH) {
        str = str.slice(0, MAX_LENGTH) + '_' + idIndex;
        idIndex++;
      }

      return str;
    }
    /**
     * upgrade options from string to {label:'', value:''}
     */
    if(needHandleOptions(model.type)){
      const viewModel = model as HasOptionsElement
      //@ts-ignore
      viewModel.options = viewModel.options.map(item => {
       return {
         label: item,
         value: sanitizeString(item)
       }
      })
      if(viewModel.value) {
        viewModel.value = getValidValuesFromOptions(viewModel.value, viewModel)
      }
    }
  }
  beforeFormToViewModel(form: FormModel,option?: UpgradeOption) {
      upgradeFormData(form,option)
  }
  beforePageToViewModel (field: FormPage, option: FormElementTransformOption) {

  }

  afterPageToViewModel (
    model: FormElementPageModel,
    field: FormPage,
    option: FormElementTransformOption
  ) {
    if (field.fieldSpecific?.condition) {
      model.condition = fieldConditionToConditionViewModel(field.fieldSpecific.condition)
    }

    model.background = ''
  }

  afterPageToServerData (
    field: FormPage,
    model: FormElementPageModel,
    option: FormElementTransformToServerOption
  ) {
    if (model.condition) {
      if(!field.fieldSpecific){
        field.fieldSpecific = {}
      }
      field.fieldSpecific.condition = conditionViewModelToServerData(model.condition)
    }

  }

  beforeElementToServerData (model: FormAnyElementViewModel) {}

  beforeElementToViewModel (field: FormField) {}

  beforePageToServerData (
    model: FormAnyElementViewModel,
    option: FormElementTransformToServerOption
  ) {}
  afterFormToServerData(form: FormModel, model:FormViewModel){

  }
  beforeFormToServerData(model: FormViewModel){
    //Change the condition.expectedValue from value to label
    const elementsMap = FormFactoryManager.getElements(model)
    elementsMap.forEach(element =>{
      (element.condition?.elementRules ||[]).forEach((rule) => {
        const target = elementsMap.get(rule.elementId)
        if(target && needHandleOptions(target.type)){
          rule.expectedValue = getValidLabelsFromOptions(rule.expectedValue, target as HasOptionsElement) as string
        }
      })
    })
  }
  afterFormToViewModel (model: FormViewModel, form: FormModel) {
    //Change the condition.expectedValue from label to value.
    const elementsMap = FormFactoryManager.getElements(model)
    elementsMap.forEach(element =>{
      (element.condition?.elementRules ||[]).forEach((rule) => {
        const target = elementsMap.get(rule.elementId)
        if(target && needHandleOptions(target.type)) {
          rule.expectedValue = getValidValuesFromOptions(rule.expectedValue, target as HasOptionsElement) as string
        }
      })
    })
  }

  afterFormValidate (
    model: FormViewModel,
    errors: FormElementAnyInputModel[],
    option: FormValidateOptions
  ): FormValidateResult {
    return {
      isValid: errors.length <=0,
      errorElements: errors
    }
  }
}
