import {
  Form<PERSON><PERSON><PERSON>V<PERSON>ueOption,
  FormElementCreateOption,
  FormElementFactory,
  FormElementTransformOption,
  FormElementType,
  FormUIOption
} from '@model/form/defines/shared'
import { FormField } from '@model/form/defines/serverDataStructure'
import uuid from 'uuid/v4'
import { processLangResource } from '@model/form/factory/shared'

import {
  FormElementHeadingModel,
  HeadingDefaultProps,
  HeadingSpecialKeys
} from '@model/form/defines/FormHeading'

import { makeTransactionResourceUrl } from '@controller/contentLibrary/src/form'
import cloneDeep from 'lodash/cloneDeep'
import {getAttrsFromFormField, getAttrsFromViewModel} from '@model/form/common/utils';

class HeadingFactory implements FormElementFactory<FormElementHeadingModel, FormField, null> {
  create (option: FormElementCreateOption): FormElementHeadingModel {
    const result: FormElementHeadingModel = {
      ...cloneDeep(HeadingDefaultProps),
      type: FormElementType.Heading,
      id: uuid()
    }
    if (option.$t) {
      processLangResource(result, option.$t)
    }
    return result as FormElementHeadingModel
  }

  toServerData (model: FormElementHeadingModel): FormField {
    return getAttrsFromViewModel(model, HeadingDefaultProps, HeadingSpecialKeys) as FormField
  }

  toViewModel (data: FormField, option: FormElementTransformOption): FormElementHeadingModel {
    const model = getAttrsFromFormField(data, HeadingDefaultProps, HeadingSpecialKeys) as FormElementHeadingModel
    if (model.enableImage && model.imageUUID) {
      model.imageURL = makeTransactionResourceUrl(
        option.boardId,
        option.transactionSequence,
        model.imageUUID,
        option.viewToken
      )
    }
    return model
  }

  validate (model: FormElementHeadingModel): Promise<boolean> {
    return Promise.resolve(true)
  }

  resetValue (model: FormElementHeadingModel) {}

  setValue (model: FormElementHeadingModel, value: string, option: FormChangeValueOption): void {}

  syncValue (model: FormElementHeadingModel, value: string, option: FormChangeValueOption): void {}

  uiOption (): FormUIOption {
    return {
      view: 'FormHeadingView',
      editor: 'FormHeadingOption'
    }
  }
}

export { HeadingFactory }
