import {
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ueOption,
  FormElement<PERSON>reateOption,
  FormElementFactory,
  FormElementTransformOption,
  FormElementType,
  FormUIOption
} from '@model/form/defines/shared'
import { FormField } from '@model/form/defines/serverDataStructure'
import uuid from 'uuid/v4'

import {
  FormElementImageModel,
  FormImageDefaultProps,
  FormImageSpecialKeys
} from '@model/form/defines/FormImage'
import { makeTransactionResourceUrl } from '@controller/contentLibrary/src/form'
import cloneDeep from 'lodash/cloneDeep'

import {getAttrsFromFormField, getAttrsFromViewModel} from "@model/form/common/utils";

class ImageFactory implements FormElementFactory<FormElementImageModel, FormField, null> {
  create (option: FormElementCreateOption): FormElementImageModel {
    const result: FormElementImageModel = {
      ...cloneDeep(FormImageDefaultProps),
      type: FormElementType.Image,
      id: uuid()
    }
    return result as FormElementImageModel
  }

  toServerData (model: FormElementImageModel): FormField {
    return getAttrsFromViewModel(model, FormImageDefaultProps, FormImageSpecialKeys) as FormField
  }

  toViewModel (data: FormField, option: FormElementTransformOption): FormElementImageModel {
    const model: FormElementImageModel = getAttrsFromFormField(data, FormImageDefaultProps, FormImageSpecialKeys) as FormElementImageModel
    if (model.imageUUID) {
      model.imageURL = makeTransactionResourceUrl(
        option.boardId,
        option.transactionSequence,
        model.imageUUID,
        option.viewToken
      )
    }
    return model
  }

  validate (model: FormElementImageModel): Promise<boolean> {
    return Promise.resolve(true)
  }

  resetValue (model: FormElementImageModel) {
    return
  }

  setValue (model: FormElementImageModel, value: string, option: FormChangeValueOption): void {
    return
  }

  syncValue (model: FormElementImageModel, value: string, option: FormChangeValueOption): void {
    return
  }

  uiOption (): FormUIOption {
    return {
      view: 'FormImageView',
      editor: 'FormImageOption'
    }
  }
}

export { ImageFactory }
