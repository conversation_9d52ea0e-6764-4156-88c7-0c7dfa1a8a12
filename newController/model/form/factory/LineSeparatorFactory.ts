import {
  Form<PERSON><PERSON><PERSON>V<PERSON>ueOption,
  FormElementCreateOption,
  FormElementFactory,
  FormElementType,
  FormUIOption
} from '@model/form/defines/shared'
import { FormField } from '@model/form/defines/serverDataStructure'
import uuid from 'uuid/v4'

import {
  FormElementLineSeparatorModel,
  LineSeparatorDefaultProps
} from '@model/form/defines/FormLineSeparator'
import cloneDeep from 'lodash/cloneDeep'
import {getAttrsFromFormField, getAttrsFromViewModel} from '@model/form/common/utils';

class LineSeparatorFactory
  implements FormElementFactory<FormElementLineSeparatorModel, FormField, null> {
  create (option: FormElementCreateOption): FormElementLineSeparatorModel {
    const result: FormElementLineSeparatorModel = {
      ...cloneDeep(LineSeparatorDefaultProps),
      type: FormElementType.LineSeparator,
      id: uuid(),
      condition: null,
      errors: []
    }
    return result as FormElementLineSeparatorModel
  }

  toServerData (model: FormElementLineSeparatorModel): FormField {
    return getAttrsFromViewModel(model, LineSeparatorDefaultProps, []) as FormField
  }

  toViewModel (data: FormField): FormElementLineSeparatorModel {
    return getAttrsFromFormField(data, LineSeparatorDefaultProps, []) as FormElementLineSeparatorModel
  }

  validate (model: FormElementLineSeparatorModel): Promise<boolean> {
    return Promise.resolve(true)
  }

  resetValue (model: FormElementLineSeparatorModel) {}

  uiOption (): FormUIOption {
    return {
      view: 'FormLineSeparatorView',
      editor: 'FormLineSeparator'
    }
  }

  setValue (
    model: FormElementLineSeparatorModel,
    value: string,
    option: FormChangeValueOption
  ): void {}

  syncValue (
    model: FormElementLineSeparatorModel,
    value: string,
    option: FormChangeValueOption
  ): void {}
}

export { LineSeparatorFactory }
