import {
  FormChangeValueOption,
  FormElementCreateOption,
  FormElementFactory,
  FormElementTransformOption, FormRuntimeOptions,
  FormUIOption, FormValidateOptions
} from '../defines/shared'
import {processLangResource, removeDDRSource} from './shared'
import uuid from 'uuid/v4'

import { FormField } from '@model/form/defines/serverDataStructure'
import pick from 'lodash/pick'
import {
  FormElementMultiLineTextModel,
  MultiLineTextDefaultProps,
  MultiLineTextSpecialKeys
} from '@model/form/defines/FormMultiLineText'
import { FormErrorType } from '@model/form/defines/base'
import cloneDeep from 'lodash/cloneDeep'
import { buildValidateResult, syncFormElementSampleValue } from '@model/form/factory/utils'
import { getFieldSpecificFormViewModel } from '@model/form/transform/common'
import {FormElementSingleLineTextModel} from "@model/form/defines/FormSingleLineText";
import {
  createDefaultValueTransform,
  getAttrsFromFormField,
  getAttrsFromViewModel,
  ModelAttrTransforms
} from "@model/form/common/utils";

class MultiLineTextFactory
  implements FormElementFactory<FormElementMultiLineTextModel, FormField, string> {
  create (option: FormElementCreateOption): FormElementMultiLineTextModel {
    const result = {
      ...cloneDeep(MultiLineTextDefaultProps),
      id: uuid()
    }
    if (option.$t) {
      processLangResource(result, option.$t)
    }
    return result
  }

  toServerData (model: FormElementMultiLineTextModel): FormField {
    return getAttrsFromViewModel(model, MultiLineTextDefaultProps, MultiLineTextSpecialKeys) as FormField
  }

  toViewModel (data: FormField, option?: FormElementTransformOption): FormElementMultiLineTextModel {
    const applyDefaultValue = option?.isCompleted !== true
    const transforms: ModelAttrTransforms<FormField> = {}

    if(applyDefaultValue){
      transforms['value'] = createDefaultValueTransform('')
    }
    return getAttrsFromFormField(data,MultiLineTextDefaultProps, MultiLineTextSpecialKeys, {transforms}) as FormElementMultiLineTextModel
  }

  validate (model: FormElementMultiLineTextModel, option?:FormValidateOptions): Promise<boolean> {
    model.errors = []
    const errors = []
    const isTemplate = option?.isValidateTemplate
    let validateValue = isTemplate? model.defaultValue :  model.value
    if(option?.enableDDR) {
      validateValue = removeDDRSource(validateValue)
    }
    if (!isTemplate && model.required) {
      if(!validateValue) {
        errors.push({
          field: '',
          errorType: FormErrorType.Required
        })
      }
    }
    if(validateValue) {
      if (model.maxLength && validateValue.length > parseInt(model.maxLength, 10)) {
        errors.push({
          field: '',
          errorType: FormErrorType.MaxLimit,
          params: {
            maxLength: model.maxLength
          }
        })
      }
      if (model.minLength && validateValue.length < parseInt(model.minLength, 10)) {
        errors.push({
          field: '',
          errorType: FormErrorType.MinLimit,
          params: {
            minLength: model.minLength,
          }
        })
      }

    }
    return buildValidateResult(model, errors, option)

  }

  resetValue (model: FormElementMultiLineTextModel) {
    model.value = ''
  }

  uiOption (runtimeOption: FormRuntimeOptions): FormUIOption {
    if (runtimeOption.isPDFForm) {
      return {
        view: 'PDFFormMultiLineTextView',
        editor: 'FormInputTextSetting'
      }
    }
    return {
      view: 'FormMultiLineTextView',
      editor: 'FormMultiLineText'
    }
  }

  setValue (
    model: FormElementMultiLineTextModel,
    value: string,
    option: FormChangeValueOption
  ): void {
    model.value = value
  }

  syncValue (
    model: FormElementMultiLineTextModel,
    value: string,
    option: FormChangeValueOption
  ): void {
    syncFormElementSampleValue(model, value, option)
  }
}

export { MultiLineTextFactory }
