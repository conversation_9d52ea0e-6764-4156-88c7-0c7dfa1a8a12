import {
    Form<PERSON>hange<PERSON><PERSON>ueOption,
    FormConditionElementFactory,
    FormElementCreateOption,
    FormElementFactory, FormElementTransformOption, FormRuntimeOptions,
    FormUIOption, FormValidateOptions
} from '../defines/shared'
import {processLangResource} from './shared'
import uuid from 'uuid/v4'
import {FormField} from '@model/form/defines/serverDataStructure'


import {
    FormElementMultiSelectionModel,
    MultiSelectionDefaultProps,
    MultiSelectionSpecialKeys, MultiSelectionToServerTransforms
} from '@model/form/defines/FormMultiSelection'
import {FormErrorType} from '@model/form/defines/base'
import cloneDeep from 'lodash/cloneDeep'
import {ConditionRuleViewModel} from '@model/form/defines/condition'
import {isArrayValueMeetExpected} from '@model/form/common/condition'
import { buildValidateResult, syncFormElementSampleValue } from '@model/form/factory/utils'
import {
    createDefaultValueTransform,
    getAttrsFromFormField,
    getAttrsFromViewModel, getValidValuesFromFieldOptions, mergeTransforms,
    ModelAttrTransforms
} from '@model/form/common/utils';


class MultiSelectionFactory implements FormElementFactory<FormElementMultiSelectionModel, FormField, string[]>, FormConditionElementFactory<FormElementMultiSelectionModel> {
    create (option: FormElementCreateOption): FormElementMultiSelectionModel {
        const result = {
            ...cloneDeep(MultiSelectionDefaultProps),
            value: [],
            defaultValue: [],
            id: uuid()
        }
        if (option.$t) {
            processLangResource(result, option.$t)
        }
        return result
    }

    toServerData (model: FormElementMultiSelectionModel): FormField {
        return  getAttrsFromViewModel(model, MultiSelectionDefaultProps, MultiSelectionSpecialKeys, {
            transforms: MultiSelectionToServerTransforms
        }) as FormField
    }

    toViewModel (data: FormField, option?: FormElementTransformOption): FormElementMultiSelectionModel {
        const applyDefaultValue = option?.isCompleted !== true
        const transforms: ModelAttrTransforms<FormField> = {}

        if(applyDefaultValue){
            transforms['value'] = createDefaultValueTransform([])
        }
        return getAttrsFromFormField(data,MultiSelectionDefaultProps, MultiSelectionSpecialKeys, {transforms}) as FormElementMultiSelectionModel
    }

    validate (model: FormElementMultiSelectionModel, option?:FormValidateOptions): Promise<boolean> {
        model.errors = []
      const errors = []
        if(option?.isValidateTemplate){
            return Promise.resolve(true)
        }
        if (model.required && !model.value?.length) {
            errors.push({
                field: '',
                errorType: FormErrorType.Required
            })
        }
      return buildValidateResult(model, errors, option)

    }

    resetValue (model: FormElementMultiSelectionModel) {
        model.value =  []
        model.errors = []
        model.displayValue = ''
    }

    uiOption (runtimeOption: FormRuntimeOptions): FormUIOption {
        if (runtimeOption.isPDFForm) {
            return {
                view: 'PDFFormMultiSelectionView',
                editor: 'PDFSelectionSetting'
            }
        }
        return {
            view: 'FormMultiSelectionView',
            editor: 'FormMultiSelection'
        }
    }

    meetExpected (model: FormElementMultiSelectionModel, rule: ConditionRuleViewModel): boolean {
        return isArrayValueMeetExpected(rule, model.value)
    }

    setValue (
        model: FormElementMultiSelectionModel,
        value: string[],
        option: FormChangeValueOption
    ): void {

        model.value = cloneDeep(value)
    }

    syncValue (
        model: FormElementMultiSelectionModel,
        value: string[],
        option: FormChangeValueOption
    ): void {
        syncFormElementSampleValue(model, value, option)
    }
}

export {
    MultiSelectionFactory
}
