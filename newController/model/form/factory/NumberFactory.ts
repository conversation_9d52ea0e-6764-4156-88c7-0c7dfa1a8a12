import {
  Form<PERSON>hange<PERSON><PERSON><PERSON>Option,
  FormConditionElementFactory,
  FormElementCreateOption,
  FormElementFactory, FormElementTransformOption,
  FormUIOption, FormValidateOptions
} from '../defines/shared'
import {isDefine, processLangResource, removeDDRSource} from './shared'
import uuid from 'uuid/v4'

import { FormField } from '@model/form/defines/serverDataStructure'
import {
  FormElementNumberModel,
  NumberDefaultProps,
  NumberSpecialKeys
} from '@model/form/defines/FormNumber'
import { FormErrorType } from '@model/form/defines/base'
import cloneDeep from 'lodash/cloneDeep'
import { ConditionRuleViewModel } from '@model/form/defines/condition'
import { isNumberValueMeetExpected } from '@model/form/common/condition'
import { buildValidateResult, syncFormElementSampleValue } from '@model/form/factory/utils'
import {
  createDefaultValueTransform,
  getAttrsFromFormField,
  getAttrsFromViewModel,
  ModelAttrTransforms,
} from '@model/form/common/utils';


class NumberFactory implements FormElementFactory<FormElementNumberModel, FormField, string>, FormConditionElementFactory<FormElementNumberModel> {
  create (option: FormElementCreateOption): FormElementNumberModel {
    const result = {
      ...cloneDeep(NumberDefaultProps),
      id: uuid()
    }
    if (option.$t) {
      processLangResource(result, option.$t)
    }
    return result
  }

  toServerData (model: FormElementNumberModel): FormField {
    return getAttrsFromViewModel(model, NumberDefaultProps, NumberSpecialKeys) as FormField
  }

  toViewModel (data: FormField, option?: FormElementTransformOption): FormElementNumberModel {
    const applyDefaultValue = option?.isCompleted !== true
    const transforms: ModelAttrTransforms<FormField> = {}

    if(applyDefaultValue){
      transforms['value'] = createDefaultValueTransform('')
    }
    return getAttrsFromFormField(data, NumberDefaultProps, NumberSpecialKeys, {transforms}) as FormElementNumberModel
  }

  validate (model: FormElementNumberModel, option?: FormValidateOptions): Promise<boolean> {
    model.errors = []
    const errors = []
    const isTemplate = option?.isValidateTemplate
    let validateValue = isTemplate? model.defaultValue :  model.value
    if(option?.enableDDR) {
      // Handle DDR validation if needed
      if (option.enableDDR && validateValue) {
        validateValue = removeDDRSource(validateValue)
      }
    }

    // Required field validation
    if (model.required) {
      if (!isDefine(validateValue) || validateValue === null || validateValue === '') {
        errors.push({
          field: '',
          errorType: FormErrorType.Required
        })
      }
    }

    // Only validate min/max length if we have a value
    if (validateValue !== null && validateValue !== '') {
      // Min length validation
      if (model.minLength && model.minLength !== '') {
        const minLength = parseFloat(model.minLength)
        if (!isNaN(minLength) && parseFloat(validateValue) < minLength) {
          errors.push({
            field: '',
            errorType: FormErrorType.MinLimit,
            params: {
              minLength: minLength
            }
          })
        }
      }

      // Max length validation
      if (model.maxLength && model.maxLength !== '') {
        const maxLength = parseFloat(model.maxLength)
        if (!isNaN(maxLength) && parseFloat(validateValue) > maxLength) {
          errors.push({
            field: '',
            errorType: FormErrorType.MaxLimit,
            params: {
              maxLength: maxLength
            }
          })
        }
      }

      // Precision validation could be added here if needed
    }

    return buildValidateResult(model, errors, option)

  }

  resetValue (model: FormElementNumberModel) {
    model.value = null
  }

  uiOption (): FormUIOption {
    return {
      view: 'FormNumberView',
      editor: 'FormNumber'
    }
  }

  meetExpected (model: FormElementNumberModel, rule: ConditionRuleViewModel): boolean {
    return isNumberValueMeetExpected(rule, model.value)
  }

  setValue (
    model: FormElementNumberModel,
    value: string,
    option: FormChangeValueOption
  ): void {
    model.value = value
  }

  syncValue (
    model: FormElementNumberModel,
    value: string,
    option: FormChangeValueOption
  ): void {
    syncFormElementSampleValue(model, value, option)
  }
}

export {
  NumberFactory
}