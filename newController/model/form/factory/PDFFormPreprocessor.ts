import { IFormPreprocessor } from '@model/form/factory/Preprocessor'
import { FormAnyElementViewModel, FormElementAnyInputModel } from '@model/form/defines/allType'
import { FormCustomData, FormField, FormModel, FormPage } from '@model/form/defines/serverDataStructure'
import { FormElementPageModel } from '@model/form/defines/FormPage'
import { FormElementCustomModel } from '@model/form/defines/base'
import { FormViewModel } from '@model/form/defines/formViewModel'
import {
  FormAllInputElements,
  FormElementTransformOption,
  FormElementTransformToServerOption,
  FormElementType,
  FormValidateOptions,
  FormValidateResult
} from '@model/form/defines/shared'
import FormFactoryManager from '@model/form/factory/index'
import { isDefine } from '@model/form/factory/shared'
import uniq from 'lodash/uniq'
import { FormElementSingleSelectionModel } from '@model/form/defines/FormSingleSelection'
import { FormElementMultiSelectionModel } from '@model/form/defines/FormMultiSelection'
import { upgradePDFFormData} from '@model/form/data/upgradePDFFormData';
import { UpgradeOption } from '@model/form/data/common'

interface ValidateGroupResult {
  items: Array<FormElementSingleSelectionModel | FormElementMultiSelectionModel>;
  hasValue: boolean;
}

export class PDFFormPreprocessor implements IFormPreprocessor {
  afterElementToServerData (field: FormField, model: FormAnyElementViewModel) {
    if (FormAllInputElements.includes(model.type)) {
      const inputModel = model as FormElementAnyInputModel
      const customData = inputModel.customData
      if (customData) {
        field.customData = customData as FormCustomData
      }
      field.pdfFormFieldId = inputModel.pdfFormFieldId
    }
  }

  afterElementToViewModel (model: FormAnyElementViewModel, field: FormField) {
    if (FormAllInputElements.includes(model.type)) {
      const inputModel = model as FormElementAnyInputModel
      if (field.customData) {
        inputModel.customData = {
          ...(field.customData as FormElementCustomModel)
        }
      }
      inputModel.pdfFormFieldId = field.pdfFormFieldId
    }
  }

  beforePageToViewModel (field: FormPage) {}
  beforeFormToViewModel(form: FormModel,option: UpgradeOption) {
    upgradePDFFormData(form,option)
  }
  afterPageToViewModel (
    model: FormElementPageModel,
    field: FormPage,
    option: FormElementTransformOption
  ) {
    if (field.customData) {
      const { width, height, pageUUID } = field.customData

      model.pageUUID = pageUUID
      model.width = width
      model.height = height
      model.backgroundPDF = `/board/${option.boardId}/${pageUUID}`
      model.thumbnail = `/board/${option.boardId}/page/${pageUUID}/thumbnail`
      if (option.viewToken) {
        model.backgroundPDF += `?t=${option.viewToken}`
        model.thumbnail += `?t=${option.viewToken}`
      }
    }
  }

  afterPageToServerData (
    field: FormPage,
    model: FormElementPageModel,
    option: FormElementTransformToServerOption
  ) {
    field.customData = {
      width: model.width,
      height: model.height,
      pageUUID: model.pageUUID
    }
  }
  beforeFormToServerData(model:FormViewModel){}
  beforeElementToServerData (model: FormAnyElementViewModel) {}

  beforeElementToViewModel (field: FormField) {}

  beforePageToServerData (
    model: FormAnyElementViewModel,
    option: FormElementTransformToServerOption
  ) {}

  afterFormToViewModel (model: FormViewModel, form: FormModel) {
    model.isPDFForm = true
  }
  afterFormToServerData(form: FormModel, model:FormViewModel){
    // if(!form.version){
    //   form.version = PDFFormCurrentVersion
    // }
  }

  afterFormValidate (
    formViewModel: FormViewModel,
    errors: FormElementAnyInputModel[],
    option: FormValidateOptions
  ): FormValidateResult {
    /**
     * Handle the exclusive selection logic for checkboxes and radio buttons with the same name, which is unique to PDF forms.
     * 1. required  any one has set value all item should be no err
     */

    const nameMap: Record<string, ValidateGroupResult> = {}
    FormFactoryManager.process(formViewModel, (model) => {
      if (
        [FormElementType.SingleSelection, FormElementType.MultiSelection].includes(model.type) &&
        model.name
      ) {
        if (!nameMap[model.name]) {
          nameMap[model.name] = {
            items: [],
            hasValue: false
          }
        }
        nameMap[model.name].items.push(
          model as FormElementSingleSelectionModel | FormElementMultiSelectionModel
        )
      }
    })
    /**
     * Determine whether the radio group /checkbox group has a value.
     */
    Object.values(nameMap).forEach((element: ValidateGroupResult) => {
      element.hasValue = !!element.items.find((item) => {
        if (item.type === FormElementType.MultiSelection) {
          return item.value?.length > 0
        } else {
          return isDefine(item.value) && item.value != ''
        }
      })
    })
    /**
     * Fix the validation result of the radio group /checkbox group
     */
    const errorElements = errors.filter((model) => {
      const needHandle =
        [FormElementType.SingleSelection, FormElementType.MultiSelection].includes(model.type) &&
        model.name
      const elementGroup = nameMap[model.name]
      if(!needHandle){
        return false
      }
      if (needHandle && elementGroup) {
        if (elementGroup.hasValue) {
          /**
           * In a group, if any element has a value, all required errors for the group’s elements should be ignored
           */
          model.errors = []
          return false
        }
      }
      return true
    })
    /**
     * If validating a single page, it is necessary to check whether the next page has options related to the erroneous elements.
     */
    const validatePage = option?.validatePage
    if (isDefine(validatePage) && errorElements.length) {
      const totalPage = formViewModel.pages.length
      if (validatePage < totalPage) {
        const errorElementName = uniq(errorElements.filter((item) => item.name).map((e) => e.name))
        if (errorElementName.length === 1) {
          const nextPage = formViewModel.pages[validatePage + 1]
          const sameNameElement = nextPage.elements.find(
            (item) => item.name === errorElementName[0]
          )
          if (sameNameElement) {
            errorElements.forEach((item) => {
              item.errors = []
            })
          }
        }
      }
    }
  const realErrors = errors.filter((item) => { return item.errors.length > 0 })
    return {
      isValid: realErrors.length <= 0,
      errorElements: realErrors
    }
  }
}
