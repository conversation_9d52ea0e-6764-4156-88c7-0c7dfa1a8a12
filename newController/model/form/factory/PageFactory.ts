import {
  <PERSON><PERSON><PERSON><PERSON>V<PERSON>ueOption,
  FormElement<PERSON>reateOption,
  FormElementFactory,
  FormElementTransformOption,
  FormElementTransformToServerOption,
  FormElementType,
  FormFactoryOption,
  FormUIOption,
  FormValidateOptions
} from '@model/form/defines/shared'
import { getFormElementFactory } from '@model/form/factory/shared'
import { getElementBaseViewModel } from '@model/form/transform/common'
import { FormField, FormPage } from '@model/form/defines/serverDataStructure'
import { FormElementPageModel } from '@model/form/defines/FormPage'
import uuid from 'uuid/v4'
import { FormAnyElementViewModel, FormElementAnyInputModel } from '@model/form/defines/allType'
import { FormPreprocessor } from '@model/form/factory/FormPreprocessor'

class PageFactory implements FormElementFactory<FormElementPageModel, FormPage, null> {
  private preprocessor: FormPreprocessor

  constructor (option?: FormFactoryOption) {
    if (option) {
      this.preprocessor = option.preprocessor
    }
  }

  create (option?: FormElementCreateOption): FormElementPageModel {
    return {
      id: uuid(),
      name: '',
      type: FormElementType.Page,
      elements: [],
      pageNumber: 1,
      defaultLabel: '',
      label: '',
      hideLabel: true,
      isVisible: true,
      errors: []
    }
  }

  toServerData (model: FormElementPageModel, option?: FormElementTransformToServerOption): FormPage {
    const fields = []
    const preprocessor: FormPreprocessor = this.preprocessor

    model.elements.forEach((element) => {
      const factory = getFormElementFactory(element.type)
      if (factory) {
        preprocessor && preprocessor.beforeElementToServerData(element)
        const field = factory.toServerData(element, option) as FormField
        preprocessor && preprocessor.afterElementToServerData(field, element)
        fields.push(field)
      }
    })
    const page =  {
      id: model.id,
      type: FormElementType.Page,
      pageNumber: model.pageNumber,
      fields
    } as FormPage

    return page
  }

  toViewModel (data: FormPage, option: FormElementTransformOption): FormElementPageModel {
    const elements: FormAnyElementViewModel[] = []
    const preprocessor: FormPreprocessor = this.preprocessor
    data.fields.forEach((element) => {
      preprocessor && preprocessor.beforeElementToViewModel(element)
      const factory = getFormElementFactory(element.type)
      if (factory) {
        const viewModel = factory.toViewModel(element, option) as FormAnyElementViewModel
        preprocessor && preprocessor.afterElementToViewModel(viewModel, element)
        elements.push(viewModel)
      }
    })
    return {
      ...getElementBaseViewModel(data),
      type: FormElementType.Page,
      pageNumber: data.pageNumber,
      label: '',
      defaultLabel: '',
      hideLabel: true,
      elements
    } as FormElementPageModel
  }

  validate (
    model: FormElementPageModel,
    option?: FormValidateOptions
  ): Promise<boolean | FormElementAnyInputModel[]> {
    const elements = []
    model.elements.forEach((element) => {
      const factory = getFormElementFactory(element.type)
      if (factory && element.isVisible) {
        elements.push(factory.validate(element, option))
      }
    })
    return new Promise((resolve, reject) => {
      if (!elements.length) {
        resolve(true)
      }
      Promise.allSettled(elements).then((results) => {
        const errors = results
          .filter((result) => result.status === 'rejected')
          .map((result) => {
            //@ts-ignore
            return result.reason
          }) as FormElementAnyInputModel[]

        if (errors.length > 0) {
          reject(errors)
        } else {
          resolve(true)
        }
      })
    })
  }

  resetValue (model: FormElementPageModel) {
    model.elements.forEach((element) => {
      const factory = getFormElementFactory(element.type)
      if (factory) {
        factory.resetValue(element)
      }
    })
  }

  uiOption (): FormUIOption {
    return {
      view: '',
      editor: ''
    }
  }

  setValue (model: FormElementPageModel, value: string, option: FormChangeValueOption): void {}

  syncValue (model: FormElementPageModel, value: string, option: FormChangeValueOption): void {}
}

export { PageFactory }
