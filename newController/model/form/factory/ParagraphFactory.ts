import {
  Form<PERSON><PERSON><PERSON>V<PERSON>ueOption,
  FormElementCreateOption,
  FormElementFactory,
  FormElementTransformOption,
  FormElementType,
  FormUIOption
} from '@model/form/defines/shared'
import { FormField } from '@model/form/defines/serverDataStructure'
import uuid from 'uuid/v4'
import { processLangResource } from '@model/form/factory/shared'

import {
  FormElementParagraphModel,
  ParagraphDefaultProps,
  ParagraphSpecialKeys
} from '@model/form/defines/FormParagraph'

import cloneDeep from 'lodash/cloneDeep'
import {getAttrsFromFormField, getAttrsFromViewModel} from "@model/form/common/utils";

class ParagraphFactory implements FormElementFactory<FormElementParagraphModel, FormField, null> {
  create (option: FormElementCreateOption): FormElementParagraphModel {
    const result: FormElementParagraphModel = {
      ...cloneDeep(ParagraphDefaultProps),
      type: FormElementType.Paragraph,
      id: uuid()
    }
    if (option.$t) {
      processLangResource(result, option.$t)
    }
    return result as FormElementParagraphModel
  }

  toServerData (model: FormElementParagraphModel): FormField {
    return getAttrsFromViewModel(model, ParagraphDefaultProps, ParagraphSpecialKeys) as FormField
  }

  toViewModel (data: FormField, option: FormElementTransformOption): FormElementParagraphModel {
    const model = getAttrsFromFormField(data, ParagraphDefaultProps, ParagraphSpecialKeys) as FormElementParagraphModel
    return model
  }

  validate (model: FormElementParagraphModel): Promise<boolean> {
    return Promise.resolve(true)
  }

  resetValue (model: FormElementParagraphModel) {}

  uiOption (): FormUIOption {
    return {
      view: 'FormParagraphView',
      editor: 'FormParagraphOption'
    }
  }

  setValue (model: FormElementParagraphModel, value: string, option: FormChangeValueOption): void {}

  syncValue (model: FormElementParagraphModel, value: string, option: FormChangeValueOption): void {}
}

export { ParagraphFactory }
