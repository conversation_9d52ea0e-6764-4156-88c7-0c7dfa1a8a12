import {
  FormChangeV<PERSON>ueOption,
  FormElementCreateOption,
  FormElementFactory,
  FormElementTransformOption, FormSyncValueOption,
  FormUIOption, FormValidateOptions
} from '../defines/shared'
import {
  FormElementPhoneNumberModel, FormPhoneNumberTransforms, PhoneNumberAlias,
  PhoneNumberDefaultProps, PhoneNumberDefaultValue,
  PhoneNumberSpecialKeys,
  PhoneNumberValue
} from '../defines/FormPhoneNumber'
import { processLangResource } from './shared'
import uuid from 'uuid/v4'
import { FormField } from '@model/form/defines/serverDataStructure'

import { FormErrorType } from '@model/form/defines/base'
import cloneDeep from 'lodash/cloneDeep'
//@ts-ignore
import { parsePhoneNumberFromString } from '@vendor/libphonenumber-js/bundle/libphonenumber-mobile'
import { buildValidateResult, setFormElementValue } from '@model/form/factory/utils'
import {
  createDefaultValueTransform,
  getAttrsFromFormField,
  getAttrsFromViewModel,
  ModelAttrTransforms,
} from '@model/form/common/utils';

class PhoneNumberFactory
  implements FormElementFactory<FormElementPhoneNumberModel, FormField, PhoneNumberValue> {
  constructor () {

  }

  create (option: FormElementCreateOption): FormElementPhoneNumberModel {
    const result = {
      ...cloneDeep(PhoneNumberDefaultProps),
      id: uuid()
    }
    if (option.$t) {
      processLangResource(result, option.$t)
    }
    return result
  }

  toServerData (model: FormElementPhoneNumberModel): FormField {
    return getAttrsFromViewModel(model, PhoneNumberDefaultProps, PhoneNumberSpecialKeys, {
      alias: PhoneNumberAlias,
      transforms: FormPhoneNumberTransforms
    }) as FormField
  }

  toViewModel (data: FormField, option?: FormElementTransformOption): FormElementPhoneNumberModel {
    const applyDefaultValue = option?.isCompleted !== true
    const transforms: ModelAttrTransforms<FormField> = {}

    if(applyDefaultValue){
      transforms['value'] = createDefaultValueTransform(PhoneNumberDefaultValue)
    }
    return getAttrsFromFormField(data, PhoneNumberDefaultProps, PhoneNumberSpecialKeys, {alias: PhoneNumberAlias,transforms}) as FormElementPhoneNumberModel
  }

  async validate (model: FormElementPhoneNumberModel, option?: FormValidateOptions): Promise<boolean> {
    model.errors = []
    const errors = []
    const { phoneNumber, number } = model.value
    if (model.required && !phoneNumber) {
      errors.push({
        field: 'phoneNumber',
        errorType: FormErrorType.Required
      })
    }
    if (phoneNumber) {
      try {
        const phoneModel = parsePhoneNumberFromString(`${number}${phoneNumber}`)
        if (!phoneModel?.isValid()) {
          errors.push({
            field: 'phoneNumber',
            errorType: FormErrorType.PhoneNumber
          })
        }
      } catch (e) {
        console.debug(e)
      }
    }

    return buildValidateResult(model, errors, option)

  }

  resetValue (model: FormElementPhoneNumberModel) {
    model.value = {
     ...cloneDeep(PhoneNumberDefaultValue),
    }
    model.displayValue = ''
  }

  uiOption (): FormUIOption {
    return {
      view: 'FormPhoneNumberView',
      editor: 'FormPhoneNumber'
    }
  }

  setValue (
    model: FormElementPhoneNumberModel,
    value: PhoneNumberValue,
    option: FormChangeValueOption
  ): void {
    setFormElementValue(model, value, option)
  }

  syncValue (
    model: FormElementPhoneNumberModel,
    value: PhoneNumberValue,
    option: FormSyncValueOption
  ): void {
    const focusedProperty = option.focusedProperty
    if (focusedProperty) {
      Object.keys(value).forEach((key) => {
        if (key !== focusedProperty) {
          if (focusedProperty === 'countryCode' && key == 'number') {
            return
          }
          model.value[key] = value[key]
        }
      })
    } else {
      model.value = cloneDeep(value)
    }
  }
}

export { PhoneNumberFactory }
