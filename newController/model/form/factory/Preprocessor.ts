import {<PERSON><PERSON><PERSON>, FormModel, FormPage} from '@model/form/defines/serverDataStructure'
import {FormAnyElementViewModel, FormElementAnyInputModel} from '@model/form/defines/allType'
import {FormElementPageModel} from '@model/form/defines/FormPage'
import {FormViewModel} from '@model/form/defines/formViewModel'
import {
    FormElementTransformOption,
    FormElementTransformToServerOption,
    FormValidateOptions, FormValidateResult
} from '@model/form/defines/shared'
import { UpgradeOption } from '@model/form/data/common'


interface IFormPreprocessor {
  beforeFormToViewModel(form: FormModel,option: UpgradeOption)
  beforeFormToServerData(form:FormViewModel);
    afterFormToServerData(form: FormModel, model:FormViewModel);
    beforeElementToViewModel(field: FormField);

    beforeElementToServerData(model: FormAnyElementViewModel);

    beforePageToViewModel(field: FormPage, option: FormElementTransformOption);

    beforePageToServerData(model: FormAnyElementViewModel, option: FormElementTransformToServerOption);

    afterPageToServerData(field: FormPage, model: FormElementPageModel, option: FormElementTransformToServerOption);

    afterElementToServerData(field: FormField, model: FormAnyElementViewModel);

    afterElementToViewModel(model: FormAnyElementViewModel, field: FormField);

    afterPageToViewModel(model: FormElementPageModel, field: FormPage, option: FormElementTransformOption);

    afterFormToViewModel(model: FormViewModel, form: FormModel);

    afterFormValidate(model: FormViewModel, errors: FormElementAnyInputModel[], option: FormValidateOptions): FormValidateResult;
}

export type {
    IFormPreprocessor
}