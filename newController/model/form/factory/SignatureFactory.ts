import {
    Form<PERSON><PERSON><PERSON>V<PERSON>ueOption,
    FormElementCreateOption,
    FormElementFactory,
    FormElementTransformOption, FormRuntimeOptions,
    FormUIOption, FormValidateOptions
} from '../defines/shared'
import {processLangResource} from './shared'
import uuid from 'uuid/v4'
import {FormField} from '@model/form/defines/serverDataStructure'
import {
    FormElementSignatureModel,
    SignatureDefaultProps,
    SignatureSpecialKeys
} from '@model/form/defines/FormSignature'

import {makeTransactionResourceUrl} from '@controller/contentLibrary/src/form'
import {FormErrorType} from '@model/form/defines/base'
import cloneDeep from 'lodash/cloneDeep'

import {getAttrsFromViewModel, getAttrsFromFormField} from '@model/form/common/utils';
import { buildValidateResult } from '@model/form/factory/utils'

class SignatureFactory implements FormElementFactory<FormElementSignatureModel, FormField, string> {
    create (option: FormElementCreateOption): FormElementSignatureModel {
        const result = {
            ...cloneDeep(SignatureDefaultProps),
            id: uuid()
        }
        if (option.$t) {
            processLangResource(result, option.$t)
        }
        return result
    }

    toServerData (model: FormElementSignatureModel): FormField {
        return getAttrsFromViewModel(model, SignatureDefaultProps, SignatureSpecialKeys) as FormField
    }

    toViewModel (data: FormField, option: FormElementTransformOption): FormElementSignatureModel {
        const model = getAttrsFromFormField(data, SignatureDefaultProps, SignatureSpecialKeys) as FormElementSignatureModel
        if (model.value) {
            model.imageURL = makeTransactionResourceUrl(option.boardId, option.transactionSequence, model.value, option.viewToken)
        }
        return model
    }


    validate (model: FormElementSignatureModel, option?: FormValidateOptions): Promise<boolean> {
        model.errors = []
      const errors = []
        if(option?.isValidateTemplate){
            return Promise.resolve(true)
        }
        if (model.required && !model.value) {
            errors.push({
                field: '',
                errorType: FormErrorType.Required
            })
        }
      return buildValidateResult(model, errors, option)

    }

    resetValue (model: FormElementSignatureModel) {
        model.value = ''
    }

    uiOption (runTimeOptions: FormRuntimeOptions): FormUIOption {
        if (runTimeOptions.isPDFForm) {
            return {
                view: 'PDFFormSignatureView',
                editor: '',
            }
        }
        return {
            view: 'FormSignatureView',
            editor: 'FormSignature'
        }
    }

    setValue (
        model: FormElementSignatureModel,
        value: string,
        option: FormChangeValueOption
    ): void {
        if (value) {
            model.value = value
            model.imageURL = makeTransactionResourceUrl(option.boardId, option.transactionSequence, model.value, option.viewToken)
        } else {
            model.value = ''
            model.imageURL = ''
        }
    }

    syncValue (
        model: FormElementSignatureModel,
        value: string,
        option: FormChangeValueOption
    ): void {
        this.setValue(model, value, option)
    }
}

export {SignatureFactory}
