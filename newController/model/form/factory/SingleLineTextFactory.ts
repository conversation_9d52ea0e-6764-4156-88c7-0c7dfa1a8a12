import {
  FormC<PERSON><PERSON>V<PERSON>ueOption,
  FormElementCreateOption,
  FormElementFactory,
  FormElementTransformOption, FormRuntimeOptions,
  FormUIOption, FormValidateOptions
} from '../defines/shared'
import {processLangResource, removeDDRSource} from './shared'
import uuid from 'uuid/v4'

import { FormField } from '@model/form/defines/serverDataStructure'

import {
  FormElementSingleLineTextModel,
  SingleLineTextDefaultProps,
  SingleLineTextSpecialKeys
} from '@model/form/defines/FormSingleLineText'
import { FormErrorType } from '@model/form/defines/base'
import cloneDeep from 'lodash/cloneDeep'
import { buildValidateResult, syncFormElementSampleValue } from '@model/form/factory/utils'
import {
  createDefaultValueTransform,
  getAttrsFromFormField,
  getAttrsFromViewModel,
  ModelAttrTransforms,
} from "@model/form/common/utils";

class SingleLineTextFactory implements FormElementFactory<FormElementSingleLineTextModel, Form<PERSON>ield, string> {
  create (option: FormElementCreateOption): FormElementSingleLineTextModel {
    const result = {
      ...cloneDeep(SingleLineTextDefaultProps),
      id: uuid()
    }
    if (option.$t) {
      processLangResource(result, option.$t)
    }
    return result
  }

  toServerData (model: FormElementSingleLineTextModel): FormField {
    return getAttrsFromViewModel(model, SingleLineTextDefaultProps, SingleLineTextSpecialKeys) as FormField
  }

  toViewModel (data: FormField, option?: FormElementTransformOption): FormElementSingleLineTextModel {
    const applyDefaultValue = option?.isCompleted !== true
    const transforms: ModelAttrTransforms<FormField> = {}

    if(applyDefaultValue){
      transforms['value'] = createDefaultValueTransform('')
    }
    return getAttrsFromFormField(data, SingleLineTextDefaultProps, SingleLineTextSpecialKeys, {transforms}) as FormElementSingleLineTextModel
  }

  validate (model: FormElementSingleLineTextModel, option?:FormValidateOptions): Promise<boolean> {
    model.errors = []
    const errors = []
    const isTemplate = option?.isValidateTemplate
    let validateValue = isTemplate ? model.defaultValue : model.value
    if(option?.enableDDR) {
      validateValue = removeDDRSource(validateValue)
    }
    if (!isTemplate && model.required) {
      if(!validateValue) {
        errors.push({
          field: '',
          errorType: FormErrorType.Required
        })
      }
    }



    if(validateValue) {
        if (model.maxLength && validateValue.length > parseInt(model.maxLength, 10)) {
          errors.push({
            field: '',
            errorType: FormErrorType.MaxLimit,
            params: {
              maxLength: model.maxLength
            }
          })
        }
        if (model.minLength && validateValue.length < parseInt(model.minLength, 10)) {
          errors.push({
            field: '',
            errorType: FormErrorType.MinLimit,
            params: {
              minLength: model.minLength,
            }
          })
        }

    }
    return buildValidateResult(model, errors, option)
  }

  resetValue (model: FormElementSingleLineTextModel) {
    model.value = ''
  }

  uiOption (runtimeOption: FormRuntimeOptions): FormUIOption {
    if (runtimeOption.isPDFForm) {
      return {
        view: 'PDFFormSingleLineTextView',
        editor: 'FormInputTextSetting'
      }
    }
    return {
      view: 'FormSingleLineTextView',
      editor: 'FormSingleLineText'
    }
  }

  setValue (
    model: FormElementSingleLineTextModel,
    value: string,
    option: FormChangeValueOption
  ): void {
    model.value = value
  }

  syncValue (
    model: FormElementSingleLineTextModel,
    value: string,
    option: FormChangeValueOption
  ): void {
    syncFormElementSampleValue(model, value, option)
  }
}

export { SingleLineTextFactory }
