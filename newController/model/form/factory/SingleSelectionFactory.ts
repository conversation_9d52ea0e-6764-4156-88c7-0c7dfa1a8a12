import {
    Form<PERSON><PERSON><PERSON>ValueOption,
    FormConditionElementFactory,
    FormElementCreateOption,
    FormElementFactory, FormElementTransformOption, FormRuntimeOptions,
    FormUIOption, FormValidateOptions
} from '../defines/shared'
import {processLangResource} from './shared'
import uuid from 'uuid/v4'
import {FormField} from '@model/form/defines/serverDataStructure'

import {
    FormElementSingleSelectionModel, getValidValueFromOptionsFromField,
    SingleSelectionDefaultProps,
    SingleSelectionSpecialKeys, SingleSelectionToServerTransforms
} from '@model/form/defines/FormSingleSelection'
import {FormErrorType} from '@model/form/defines/base'
import cloneDeep from 'lodash/cloneDeep'
import {ConditionRuleViewModel} from '@model/form/defines/condition'
import {isStringValueMeetExpected} from '@model/form/common/condition'
import { buildValidateResult, syncFormElementSampleValue } from '@model/form/factory/utils'
import {
  createDefaultValueTransform,
  getAttrsFromFormField,
  getAttrsFromViewModel, mergeTransforms,
  ModelAttrTransforms, TransformFunction
} from '@model/form/common/utils'


class SingleSelectionFactory implements FormElementFactory<FormElementSingleSelectionModel, FormField, string>,
    FormConditionElementFactory<FormElementSingleSelectionModel> {
    create (option: FormElementCreateOption): FormElementSingleSelectionModel {
        const result = {
            ...cloneDeep(SingleSelectionDefaultProps),
            id: uuid()
        }
        if (option.$t) {
            processLangResource(result, option.$t)
        }
        return result
    }

    toServerData (model: FormElementSingleSelectionModel): FormField {
        return getAttrsFromViewModel(model, SingleSelectionDefaultProps, SingleSelectionSpecialKeys,{transforms:SingleSelectionToServerTransforms}) as FormField
    }

    toViewModel (data: FormField, option?: FormElementTransformOption): FormElementSingleSelectionModel {
        const applyDefaultValue = option?.isCompleted !== true
        const transforms: ModelAttrTransforms<FormField> = {}
      if(applyDefaultValue){
            transforms['value'] = mergeTransforms(createDefaultValueTransform(''),getValidValueFromOptionsFromField)
        }else{
            transforms['value'] = getValidValueFromOptionsFromField
        }
        return getAttrsFromFormField(data, SingleSelectionDefaultProps, SingleSelectionSpecialKeys, {transforms}) as FormElementSingleSelectionModel
    }

    validate (model: FormElementSingleSelectionModel,option?:FormValidateOptions): Promise<boolean> {

        model.errors = []
      const errors = []
        if(option?.isValidateTemplate){
            return Promise.resolve(true)
        }
        if (model.required && !model.value) {
            errors.push({
                field: '',
                errorType: FormErrorType.Required
            })
        }
      return buildValidateResult(model, errors, option)

    }

    resetValue (model: FormElementSingleSelectionModel) {
        model.value = ''
        model.errors = []
    }

    uiOption (runtimeOption: FormRuntimeOptions): FormUIOption {
        if (runtimeOption.isPDFForm) {
            return {
                view: 'PDFFormSingleSelectionView',
                editor: 'PDFSelectionSetting'
            }
        }
        return {
            view: 'FormSingleSelectionView',
            editor: 'FormSingleSelection'
        }
    }

    meetExpected (model: FormElementSingleSelectionModel, rule: ConditionRuleViewModel): boolean {
        return isStringValueMeetExpected(rule, model.value)
    }

    setValue (
        model: FormElementSingleSelectionModel,
        value: string,
        option: FormChangeValueOption
    ): void {
        model.value = value
    }

    syncValue (
        model: FormElementSingleSelectionModel,
        value: string,
        option: FormChangeValueOption
    ): void {
        syncFormElementSampleValue(model, value, option)
    }
}

export {
    SingleSelectionFactory
}
