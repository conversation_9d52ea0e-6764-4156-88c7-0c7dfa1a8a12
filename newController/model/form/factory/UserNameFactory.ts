import {
    FormChangeValueOption,
    FormElementCreateOption,
    FormElementFactory, FormElementTransformOption,
    FormUIOption, FormValidateOptions
} from '../defines/shared'
import {
    UserNameAlias,
    UserNameDefaultProps,
    UserNameDefaultValue,
    UserNameSpecialKeys,
    UserNameValue
} from '../defines/FormUserName'
import type {FormElementUserNameModel} from '../defines/FormUserName'
import {processLangResource} from './shared'
import uuid from 'uuid/v4'
import {FormField} from '@model/form/defines/serverDataStructure'
import {FormErrorType} from '@model/form/defines/base'
import cloneDeep from 'lodash/cloneDeep'
import {
  buildValidateResult,
  setFormElementValue,
  syncFormElementObjectValue,
  validateFieldMaxLength
} from '@model/form/factory/utils'
import {
    createDefaultValueTransform,
    getAttrsFromFormField,
    getAttrsFromViewModel,
    ModelAttrTransforms,
} from "@model/form/common/utils";


class UserNameFactory implements FormElementFactory<FormElementUserNameModel, FormField, UserNameValue> {
    create (option: FormElementCreateOption): FormElementUserNameModel {
        const result = {
            ...cloneDeep(UserNameDefaultProps),
            id: uuid()
        }
        if (option.$t) {
            processLangResource(result, option.$t)
        }
        return result
    }

    toServerData (model: FormElementUserNameModel): FormField {
        return getAttrsFromViewModel(model, UserNameDefaultProps, UserNameSpecialKeys,{alias: UserNameAlias}) as FormField
    }

    toViewModel (data: FormField, option?: FormElementTransformOption): FormElementUserNameModel {
        const applyDefaultValue = option?.isCompleted !== true
        const transforms: ModelAttrTransforms<FormField> = {}

        if(applyDefaultValue){
            transforms['value'] = createDefaultValueTransform(UserNameDefaultValue)
        }
        return getAttrsFromFormField(data, UserNameDefaultProps, UserNameSpecialKeys,{alias: UserNameAlias, transforms}) as FormElementUserNameModel
    }

    validate (model: FormElementUserNameModel, option?: FormValidateOptions): Promise<boolean> {
        model.errors = []
      const errors = []
        if (model.required) {
            if (!model.value.firstName) {
                errors.push({
                    field: 'firstName',
                    errorType: FormErrorType.Required
                })
            }
            if(!model.value.lastName){
                errors.push({
                    field: 'lastName',
                    errorType: FormErrorType.Required
                })
            }
        }
        const maxLengthMap = {
            firstName: 80,
            lastName: 80
        }
        validateFieldMaxLength(model, maxLengthMap, errors, option)

      return buildValidateResult(model, errors, option)

    }

    resetValue (model: FormElementUserNameModel) {
        model.value = {
            ...cloneDeep(UserNameDefaultValue),
        }
    }

    uiOption (): FormUIOption {
        return {
            view: 'FormUserNameView',
            editor: 'FormUserName'
        }
    }

    setValue (
        model: FormElementUserNameModel,
        value: UserNameValue,
        option: FormChangeValueOption
    ): void {
        setFormElementValue(model, value, option)
    }

    syncValue (
        model: FormElementUserNameModel,
        value: UserNameValue,
        option: FormChangeValueOption
    ): void {
        syncFormElementObjectValue(model, value, option)
    }
}

export {
    UserNameFactory
}