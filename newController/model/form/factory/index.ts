import {
  createFormElementViewModel,
  getFormElementFactory,
  registerFormElementFactory
} from './shared'
import {
  FormElementFactory,
  FormElementId,
  FormElementTransformOption,
  FormElementTransformToServerOption,
  FormElementType,
  FormRuntimeOptions,
  FormUIOption,
  FormValidateOptions,
  FormValidateResult
} from '../defines/shared'
import { CurrencyFactory } from './CurrencyFactory'
import { PageFactory } from './PageFactory'
import { FormFactory } from './FormFactory'
import {
  FormAnyElementValue,
  FormAnyElementViewModel,
  FormAnyViewModel
} from '@model/form/defines/allType'
import { FormField, FormModel, FormPage } from '@model/form/defines/serverDataStructure'
import { buildSearchContent } from '@model/form/transform/form'
import { FormViewModel } from '@model/form/defines/formViewModel'
import { DateFactory } from '@model/form/factory/DateFactory'
import { AddressFactory } from '@model/form/factory/AddressFactory'
import { HeadingFactory } from '@model/form/factory/HeadingFactory'
import { ImageFactory } from '@model/form/factory/ImageFactory'
import { LineSeparatorFactory } from '@model/form/factory/LineSeparatorFactory'
import { MultiLineTextFactory } from '@model/form/factory/MultiLineTextFactory'
import { MultiSelectionFactory } from '@model/form/factory/MultiSelectionFactory'
import { NumberFactory } from '@model/form/factory/NumberFactory'
import { PhoneNumberFactory } from '@model/form/factory/PhoneNumberFactory'
import { SingleLineTextFactory } from '@model/form/factory/SingleLineTextFactory'
import { SignatureFactory } from '@model/form/factory/SignatureFactory'
import { SingleSelectionFactory } from '@model/form/factory/SingleSelectionFactory'
import { UserNameFactory } from '@model/form/factory/UserNameFactory'
import { EmailAddressFactory } from '@model/form/factory/EmailAddressFactory'
import { DropdownListFactory } from '@model/form/factory/DropdownListFactory'
import { FileUploadFactory } from '@model/form/factory/FileUploadFactory'
import { ParagraphFactory } from '@model/form/factory/ParagraphFactory'
import { FormPreprocessor } from '@model/form/factory/FormPreprocessor'
import { PDFFormPreprocessor } from '@model/form/factory/PDFFormPreprocessor'
import { FormElementPageModel } from '@model/form/defines/FormPage'

registerFormElementFactory(FormElementType.Currency, new CurrencyFactory())
registerFormElementFactory(FormElementType.Page, new PageFactory())
registerFormElementFactory(
  FormElementType.Form,
  new FormFactory({
    preprocessor: new FormPreprocessor(),
    type: FormElementType.Form
  })
)
registerFormElementFactory(
  FormElementType.PDFForm,
  new FormFactory({
    preprocessor: new PDFFormPreprocessor(),
    type: FormElementType.PDFForm
  })
)
registerFormElementFactory(FormElementType.Date, new DateFactory())
registerFormElementFactory(FormElementType.Address, new AddressFactory())
registerFormElementFactory(FormElementType.Heading, new HeadingFactory())
registerFormElementFactory(FormElementType.Image, new ImageFactory())
registerFormElementFactory(FormElementType.LineSeparator, new LineSeparatorFactory())
registerFormElementFactory(FormElementType.MultiLineText, new MultiLineTextFactory())
registerFormElementFactory(FormElementType.MultiSelection, new MultiSelectionFactory())
registerFormElementFactory(FormElementType.Number, new NumberFactory())
registerFormElementFactory(FormElementType.PhoneNumber, new PhoneNumberFactory())
registerFormElementFactory(FormElementType.Signature, new SignatureFactory())
registerFormElementFactory(FormElementType.SingleLineText, new SingleLineTextFactory())
registerFormElementFactory(FormElementType.SingleSelection, new SingleSelectionFactory())
registerFormElementFactory(FormElementType.UserName, new UserNameFactory())
registerFormElementFactory(FormElementType.EmailAddress, new EmailAddressFactory())
registerFormElementFactory(FormElementType.DropdownList, new DropdownListFactory())
registerFormElementFactory(FormElementType.FileUpload, new FileUploadFactory())
registerFormElementFactory(FormElementType.Paragraph, new ParagraphFactory())

const FormFactoryManager = {
  createFormElementViewModel,
  getFormElementFactory,
  toServerData (model: FormViewModel, option?: FormElementTransformToServerOption) {
    return getFormElementFactory(model.type).toServerData(model, option)
  },
  toViewModel (element: FormModel, option: FormElementTransformOption): FormViewModel {
    return getFormElementFactory(element.type).toViewModel(element, option) as FormViewModel
  },
  validate (
    model: FormAnyViewModel,
    options: FormValidateOptions
  ): Promise<boolean | FormValidateResult> {
    return getFormElementFactory(model.type).validate(model, options)
  },
  uiOption (model: FormAnyViewModel, runTimeOptions: FormRuntimeOptions): FormUIOption {
    return getFormElementFactory(model.type).uiOption(runTimeOptions)
  },
  getSearchContent (model: FormViewModel): string {
    return buildSearchContent(model)
  },
  resetValue (model: FormViewModel) {
    return getFormElementFactory(model.type).resetValue(model)
  },
  process (
    model: FormViewModel,
    processFn: (
      ele: FormAnyElementViewModel,
      factory: FormElementFactory<
        FormAnyViewModel,
        FormField | FormPage | FormModel,
        FormAnyElementValue
      >,
      page: FormElementPageModel,
      pageIndex: number
    ) => void
  ) {
    model.pages.forEach((page,index) => {
      page.elements.forEach((element) => {
        processFn(element, getFormElementFactory(element.type),page,index)
      })
    })
  },
  getElements (model: FormViewModel, filter?: (ele: FormAnyElementViewModel) => boolean) {
    const elementMap = new Map<FormElementId, FormAnyElementViewModel>()
    model.pages.forEach((page) => {
      page.elements.forEach((element) => {
        let needPush = !filter
        if (filter) {
          needPush = filter(element)
        }
        if (needPush) {
          elementMap.set(element.id, element)
        }
      })
    })
    return elementMap
  }
}
export default FormFactoryManager
