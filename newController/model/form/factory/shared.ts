import {
  FormChangeValueOption,
  FormElementCreateOption,
  FormElementFactory,
  FormElementType,
  FormRuntimeOptions,
  FormUIOption
} from '../defines/shared'
import { FormAnyElementValue, FormAnyElementViewModel, FormAnyViewModel } from '../defines/allType'
import { FormField, FormModel, FormPage } from '../defines/serverDataStructure'
import { throwErrorForDev } from '@commonUtils/app'
import { FormViewModel } from '@model/form/defines/formViewModel'

const factoryMap = {}

/**
 * In the TypeScript code, some language resources are represented using ‘$t(“label”)’ to indicate the need to retrieve language resources. This function is used to replace these placeholders with the corresponding language resources
 * @param model
 * @param $t
 */
function hasLangResource (val: string): boolean {
  return val.startsWith('$t("') && val.endsWith('")')
}

function replaceLangResource (val: string, $t: Function): string {
  if (val && hasLangResource(val)) {
    const key = val.substring(4, val.length - 2)
    return $t(key)
  }
  return val
}

interface ModelWithLangResource {
  label: string;
  placeholder?: string;
  defaultLabel?: string;
  defaultPlaceholder?: string;

  [key: string]: any;
}

function processLangResource<T extends ModelWithLangResource> (model: T, $t: Function): T {
  if (!$t) {
    return model
  }
  model.label = replaceLangResource(model.label, $t)
  if (model.defaultLabel) {
    model.defaultLabel = replaceLangResource(model.defaultLabel, $t)
  }
  if (model.placeholder) {
    model.placeholder = replaceLangResource(model.placeholder, $t)
    if (model.defaultPlaceholder) {
      model.defaultPlaceholder = replaceLangResource(model.defaultPlaceholder, $t)
    }
  }
  return model
}

function createFormElementViewModel (
  type: FormElementType,
  options: FormElementCreateOption
): FormAnyViewModel {
  const factory = factoryMap[type]
  if (factory) {
    return factory.create(options)
  } else {
    throwErrorForDev(new Error(`not found factory: ${type}`))
  }
  //@ts-ignore
  return {}
}

function getFormElementUIOption (
  type: FormElementType,
  runTimeOptions: FormRuntimeOptions
): FormUIOption {
  const factory = factoryMap[type]
  if (factory) {
    return factory.uiOption(runTimeOptions)
  } else {
    throwErrorForDev(new Error(`not found factory: ${type}`))
    return {} as FormUIOption
  }
}

function registerFormElementFactory (
  type: FormElementType,
  factory: FormElementFactory<
    FormAnyViewModel,
    FormField | FormPage | FormModel,
    FormAnyElementValue
  >
): void {
  factoryMap[type] = factory
}

function getFormElementFactory (
  type: FormElementType
): FormElementFactory<
  FormAnyViewModel,
  FormField | FormPage | FormModel,
  FormAnyElementValue
> | null {
  const factory = factoryMap[type]
  if (factory) {
    return factory
  } else {
    throwErrorForDev(new Error(`not found factory: ${type}`))
  }
  return null
}

function isDefine (obj: any): boolean {
  return !(undefined === obj || null === obj)
}

class FormElementBase {
  setValue (model: FormViewModel, value: string, option: FormChangeValueOption): void {}

  syncValue (model: FormViewModel, value: string, option: FormChangeValueOption): void {}
}
const ddrFieldReg = /\$\{([0-9a-fA-F]{8}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{12})(\|json)?\}/g
function removeDDRSource(value: string): string {
  return value.replace(ddrFieldReg, '').trim()
}
function hasDDRSource(value: string): boolean {
  return value.match(ddrFieldReg)?.length > 0
}

export {
  hasDDRSource,
  removeDDRSource,
  isDefine,
  registerFormElementFactory,
  createFormElementViewModel,
  replaceLangResource,
  processLangResource,
  getFormElementUIOption,
  getFormElementFactory
}
