import {FormAnyElementValue, FormElementAnyInputModel} from '@model/form/defines/allType'
import {FormChangeValueOption, FormSyncValueOption, FormValidateOptions} from '@model/form/defines/shared'
import cloneDeep from 'lodash/cloneDeep'
import {MxISDK} from 'isdk';
import moment from 'moment-timezone';
import isObject from 'lodash/isObject';
import {removeDDRSource} from "@model/form/factory/shared";
import { FormElementError, FormErrorType } from '@model/form/defines/base'
import clone from "lodash/clone";

export function setFormElementValue (
    model: FormElementAnyInputModel,
    value: FormAnyElementValue,
    option: FormChangeValueOption
): void {
    const changedProperty = option?.changedProperty
    if (changedProperty) {
        model.value[changedProperty] = value[changedProperty]
    } else {
        //@ts-ignore
        model.value = cloneDeep(value)
    }
}

export function syncFormElementObjectValue (
    model: FormElementAnyInputModel,
    value: FormAnyElementValue,
    option: FormSyncValueOption
) {
    const focusedProperty = option.focusedProperty
    const focusedElementId = option.focusedElementId
    if (!isObject(value) || !isObject(model.value)) {
        throw new Error('value should be an object')
    }
    if (model.id !== focusedElementId) {
        //@ts-ignore
        model.value = {
            ...model.value,
            ...value
        } as FormAnyElementValue
    } else if (focusedProperty) {
        const updateValue = cloneDeep(value)
        delete updateValue[focusedProperty]
        //@ts-ignore
        model.value = {
            ...model.value,
            ...updateValue
        } as FormAnyElementValue
    }
}

export function syncFormElementSampleValue (
    model: FormElementAnyInputModel,
    value: FormAnyElementValue,
    option: FormSyncValueOption
) {
    if (option.focusedElementId === model.id) {
        return
    } else {
        //@ts-ignore
        model.value = clone(value)
    }
}

export function getUserTimezone () {
    return MxISDK.getCurrentUser()?.basicInfo?.timezone || moment.tz.guess()
}
export function validateFieldMaxLength(model: FormElementAnyInputModel, maxLengthMap: Record<string, number>,errors:FormElementError[], options: FormValidateOptions){

    Object.keys(maxLengthMap).forEach((key) => {
        let value = model.value[key]
        if(!value){
            return
        }
        if(options.enableDDR){
            value = removeDDRSource(value)
        }
        if (value.length > maxLengthMap[key]) {
            if(errors.find(item => item.field === key)){
                // the field already has error, skip current error
                return;
            }
            errors.push({
                field: key,
                errorType: FormErrorType.MaxLimit,
                params: {
                    maxLength: maxLengthMap[key]
                }
            })
        }
    })
}
export function buildValidateResult(model: FormElementAnyInputModel, errors: FormElementError[], option: FormValidateOptions): Promise<boolean>{
  return new Promise((resolve, reject) => {
    if (errors.length > 0) {
      if(option?.notSaveError !== true){
        model.errors = errors
      }
      return reject(model)
    } else {
      return resolve(true)
    }
  })
}