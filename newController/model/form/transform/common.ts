import { FieldSpecific, FieldSpecificKeys, FormField, FormPage } from '@model/form/defines/serverDataStructure'

import { FormElementBaseModel,FormElementSpecialKeys } from '@model/form/defines/base'
import { FormElementType} from '../defines/shared'
import { Defines, MxISDK } from 'isdk'
import { FormAnyElementViewModel } from '@model/form/defines/allType'
import pick from 'lodash/pick'
import { ElementConditionViewModel } from '@model/form/defines/condition'


export function getElementBaseViewModel (field: FormField | FormPage): FormElementBaseModel {
  return {
    id: field.id,
    name: field.name,
    type: field.type as FormElementType,
    label: field?.label || '',
    hideLabel: field.fieldSpecific?.hideLabel,
    defaultLabel: '',
    condition: {} as ElementConditionViewModel ,
    isVisible: true
  } as FormElementBaseModel
}

export function getMyStep (steps: Defines.TransactionStep[]): Defines.TransactionStep {
  const groupSteps: Map<string, Defines.TransactionStep> = new Map();
  const mxUser = MxISDK.getCurrentUser()
  let step = steps.find(step => {
    if(step.is_deleted){
      return false
    }
    const groupId = step.assignee?.group?.id
    if(groupId && !groupSteps.has(groupId)){
      groupSteps.set(groupId, step)
      return false
    }
    return step.assignee?.user?.id === mxUser.id
  })
  if (!step && groupSteps.size) {
    const groupIds = mxUser.basicInfo.groups.filter(group => !group.is_deleted && group.group.type !== 'GROUP_TYPE_ORG').map(group => group.group.id)
    step = Array.from(groupSteps.values()).find(groupStep => {
      return groupIds.includes(groupStep.assignee?.group?.id)
    });
  }
  return step
}
export function getFieldSpecificFormViewModel(model: FormAnyElementViewModel, keys: FieldSpecificKeys, overwriteProps?: Partial<FieldSpecific>): Partial<FieldSpecific> {
  return {
    ...pick(model, keys),
    ...(overwriteProps || {})
  } as Partial<FieldSpecific>
}
