import {
  ElementFilledStatusViewModel,
  ElementFilledValueViewModel,
  FormUserStateType,
  FormElementState
} from '@model/form/defines/state'
import { Defines, MxISDK } from 'isdk'
import { FormAnyElementViewModel, FormElementAnyInputModel } from '@model/form/defines/allType'
import moment from 'moment-timezone'
import FormFactoryManager from '@model/form/factory'
import cloneDeep from 'lodash/cloneDeep'
import { FormField } from '@controller/defines/CFormModel'
import {FormElementTransformOption, FormElementType} from "@model/form/defines/shared";
import {getValidLabelsFromOptions} from "@model/form/common/utils";

export function elementStateViewModelToString (field: Partial<ElementFilledStatusViewModel>) {
  const data = field as ElementFilledStatusViewModel
  return JSON.stringify({
    type: data.type,
    fieldId: data.elementId,
    isFilling: data.isFilling,
    focusElement: data.focusElement,
    assigneeId: field.assigneeId
  })
}
export function getFilledContentString (model: FormElementAnyInputModel, options: FormElementTransformOption){
  let currentValue = model.value
  if(!options.isPDFForm){
      /**
       * in UI layer we upgrade the options from string[] to {label:'', value:''}[]
       * We need to convert the new data into the old format before saving.
       * To maintain compatibility with the old version of the data.
       */
      if ([FormElementType.MultiSelection, FormElementType.SingleSelection,FormElementType.DropdownList].includes(model.type)) {
        //@ts-ignore
        currentValue = getValidLabelsFromOptions(model.value, model)
    }
  }
  return JSON.stringify({
    type: FormUserStateType.Value,
    fieldId: model.id,
    protected: model.isProtected,
    value: currentValue
  })
}
export function elementFilledStateViewModelToString (
  field: Partial<ElementFilledValueViewModel>,
  element: FormElementAnyInputModel
) {
  const data = field as ElementFilledValueViewModel
  const newModel = cloneDeep(element)
  newModel.value = data.value
  const serverData = FormFactoryManager.getFormElementFactory(element.type).toServerData(newModel) as FormField

  return JSON.stringify({
    type: data.type,
    fieldId: data.elementId,
    protected: data.protected,
    value: serverData?.value
  })
}

export function contentToUserStateModel (
  element: Defines.TransactionElement,
  step: Defines.TransactionStep
): ElementFilledStatusViewModel | ElementFilledValueViewModel {
  let type: FormUserStateType = FormUserStateType.Value
  let data
  const { created_time, updated_time, creator, sequence } = element

  try {
    data = JSON.parse(element.string_value) as FormElementState

    if (data.type === FormUserStateType.Status) {
      type = FormUserStateType.Status
    }
    if (data.id === 'UserFillStatus') {
      // for old data
      type = FormUserStateType.Status
    }
  } catch (e) {
    console.warn(e)
  }
  const base = {
    stepSequence: step.sequence,
    elementId: data.fieldId,
    contentSequence: sequence,
    updatedTime: updated_time || created_time,
    assigneeId: element.creator?.user?.id
  }
  if (type === FormUserStateType.Status) {
    return {
      ...base,
      type: FormUserStateType.Status,
      focusElement: data.focusElement,
      isFilling: data.isFilling,
      viewedTime: step.viewed_time
    } as ElementFilledStatusViewModel
  } else {
    return {
      ...base,
      type: FormUserStateType.Value,
      value: data.value,
      protected: data.protected
    } as ElementFilledValueViewModel
  }
}
