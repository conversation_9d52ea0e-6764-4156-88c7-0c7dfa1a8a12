import {Defines, MxISDK} from 'isdk'
import {
  FileName,
  FileUUID,
  FormAttachmentBaseInfo,
  FormElementId,
  FormElementTransformOption,
  FormElementType,
  ReferenceSequence,
  UserId
} from '@model/form/defines/shared'
import {FormViewModel} from '@model/form/defines/formViewModel'
import {getMyStep} from '@model/form/transform/common'

import get from 'lodash/get'
import {CBoardUser} from '@controller/defines/CBoardUser'
import {
  ElementFilledFieldValueModel,
  ElementFilledStatusViewModel,
  ElementFilledValueViewModel,
  FormUserStateType
} from '@model/form/defines/state'
import {contentToUserStateModel} from '@model/form/transform/fillStatusAndData'
import isObject from 'lodash/isObject'
import {isDefine} from '@model/form/factory/shared'
import {
  FormAnyElementViewModel,
  FormElementSelectionModels, HasOptionsElement
} from '@model/form/defines/allType'
import {FormElementHeadingModel} from '@model/form/defines/FormHeading'
import {FormElementInputBaseModel} from '@model/form/defines/base'
import {ObjectUtils, throwErrorForDev} from '@commonUtils'
import {FormModel} from '@model/form/defines/serverDataStructure'
import {AttachmentCustomData, FileUploadValue} from '@model/form/defines/FormFileUpload'
import FormFactoryManager from '@model/form/factory'
import {CBoardFile} from "@controller/defines/CBoardFile";
import {FileFormatter} from "@controller/utils/file";
import {getValidValuesFromOptions} from "@model/form/common/utils";

export function getAttachmentsNameMap(board: Defines.Board): Map<FileUUID, FileName> {
  const attachmentsNameMap: Map<FileUUID, FileName> = new Map()
  ;(board.folders || []).forEach((folder) => {
    ;(folder.files || []).forEach((file) => {
      attachmentsNameMap.set(file.client_uuid, file.name)
    })
  })
  return attachmentsNameMap
}

export function getUploadedFileRefs(transaction: Defines.BoardTransaction) {
  const uploadedFileRefs: Map<ReferenceSequence, FormAttachmentBaseInfo> = new Map()
  ;(transaction?.references || [])
    .filter((reference) => {
      if (
        reference.is_deleted ||
        reference.type === Defines.BoardReferenceType.REFERENCE_TYPE_SUPPORT_FILE ||
        reference.type === Defines.BoardReferenceType.REFERENCE_TYPE_FILE_REPLY
      ) {
        return false
      }
      return true
    })
    .forEach((reference) => {
      const customDataStr = reference.custom_data || ''
      if (customDataStr) {
        try {
          const customData = JSON.parse(customDataStr) as AttachmentCustomData
          uploadedFileRefs.set(reference.sequence, {
            stepSequence: customData.step,
            referenceSequence: reference.sequence,
            elementId: customData.elementId
          })
        } catch (err) {}
      }
    })
  return uploadedFileRefs
}



function transactionToFormViewModel(
  transaction: Defines.BoardTransaction,
  boardId: string,
  members: Map<UserId, CBoardUser>,
  options: FormElementTransformOption
): FormViewModel {
  let hasChangeAfterLastView = false
  let actionId = ''
  const ownerIds = []
  const assigneeIds = []
  let isIntegrationForm = false
  let submittedAssigneeId = ''
  const assignees = []
  const myStep = getMyStep(transaction.steps)
  if (myStep) {
    hasChangeAfterLastView = myStep.viewed_time < transaction.last_modified_time
    try {
      const action = JSON.parse(myStep.actions)[0]
      actionId = action.id
    } catch (e) {}
  }

  members.forEach((member) => {
    if (member.isOwner) {
      ownerIds.push(member.id)
    }
  })
  const creatorId = transaction.creator?.user?.id
  if (creatorId) {
    ownerIds.push(creatorId)
  }

  transaction.steps
    .filter((step) => !step.is_deleted)
    .forEach((step) => {
      const userId = get(step, 'assignee.user.id', get(step, 'assignee.group.id'))
      if (userId) {
        assigneeIds.push(userId)
        const user = members.get(userId)
        if (user) {
          assignees.push(user)
        } else {
          console.warn('user not in members', userId)
          /**
           * User not in members maybe is deleted
           * It’s just a safeguard; this issue shouldn’t occur under normal circumstances.
           */
          assignees.push(step.assignee as CBoardUser)
        }
      }
      if (step.status === Defines.TransactionStepStatus.STEP_STATUS_COMPLETED) {
        submittedAssigneeId = userId
      }
    })
  if (transaction.sub_type?.toLowerCase() === 'integration') {
    isIntegrationForm = true
  }
  let baseInfo = {}
  let isEmptyForm = false
  let creator = ''
  const isCompleted = transaction.status === Defines.TransactionStatus.TRANSACTION_STATUS_COMPLETED
  const isPDFForm = transaction.type === Defines.TransactionType.TRANSACTION_TYPE_PDF_FORM
  try {
    if (transaction.card) {
      const cardObj: FormModel = JSON.parse(transaction.card)
      // if (!isCompleted) {
      //   //for template we need upgrade the old value to defaultValue
      //   upgradeDataToSupportDefaultValue(cardObj.data.pages)
      // }
      baseInfo = FormFactoryManager.toViewModel(cardObj, {
        ...options,
        boardId: boardId,
        transactionSequence: transaction.sequence,
        isCompleted
      })
      creator = cardObj.creator
      isEmptyForm = cardObj.isEmptyForm
    } else {
      isEmptyForm = true
    }
  } catch (e) {
    throwErrorForDev(e)
  }
  let downloadPDFUrl = ''
  let downloadMaskedPDFUrl = ''
  let downloadCSVUrl = ''
  let downloadMaskedCSVUrl = ''
  if (isCompleted) {
    if (transaction.original) {
      downloadPDFUrl = `/board/${boardId}/${transaction.original}?d=${transaction.title}.pdf`
    }
    if (transaction.original_masked) {
      downloadMaskedPDFUrl = `/board/${boardId}/${transaction.original_masked}?d=${transaction.title}.pdf`
    }
    if (transaction.original_csv) {
      downloadCSVUrl = `/board/${boardId}/${transaction.original_csv}?d=${transaction.title}.csv`
    }
    if (transaction.original_csv_masked) {
      downloadMaskedCSVUrl = `/board/${boardId}/${transaction.original_csv_masked}?d=${transaction.title}.csv`
    }
  }

  return {
    ...baseInfo,
    creator,
    boardId,
    title: transaction.title,
    transactionSequence: transaction.sequence,
    dueDate: transaction.expiration_date,
    description: transaction.sub_title,
    templateName: transaction.template_name,
    templateDescription: transaction.template_description,
    updatedTime: transaction.updated_time,
    isCompleted,
    isEmptyForm,
    isPDFForm,
    assigneeIds,
    isIntegrationForm,
    hasChangeAfterLastView,
    submittedAssigneeId,
    actionId,
    assignees,
    ownerIds,
    downloadPDFUrl,
    downloadCSVUrl,
    downloadMaskedCSVUrl,
    downloadMaskedPDFUrl
  } as FormViewModel
}
function handleMutualExclusionLogic(existElementsMap:Map<FormElementId, FormAnyElementViewModel>, filledValues:Map<FormElementId, ElementFilledValueViewModel> ){
  const checkedRadio = {}
  /**
   * Calculate the last value of the property in the form by name attribute.
   */
  filledValues.forEach((filledModel: ElementFilledValueViewModel) => {
    const viewModel = existElementsMap.get(filledModel.elementId)
    if(viewModel.type === FormElementType.SingleSelection && viewModel.name) {
      if(!filledModel.value) {
        return
      }
    } else if (viewModel.type === FormElementType.MultiSelection && viewModel.name) {
      if(!filledModel.value?.length) {
        return
      }
    } else{
      return;
    }

    if(checkedRadio[viewModel.name]){
      if(checkedRadio[viewModel.name].updatedTime >  filledModel.updatedTime){
        return
      }
    }
    // save checked radio
    checkedRadio[viewModel.name] = filledModel
  })
  /**
   * uncheck other field by name
   */
  filledValues.forEach((filledModel: ElementFilledValueViewModel) => {
    const viewModel = existElementsMap.get(filledModel.elementId)
    if(!viewModel.name || ![FormElementType.MultiSelection,FormElementType.SingleSelection].includes(viewModel.type)){
      return
    }
    const checkedElement:ElementFilledValueViewModel = checkedRadio[viewModel.name]
    if(checkedElement){
      if(checkedElement.elementId !== filledModel.elementId) {
        filledModel.value = viewModel.type === FormElementType.MultiSelection ? [] : ""
      }
    }
  })
}
export function calcUploadedFile(transaction: Defines.BoardTransaction, board: Defines.Board,formViewModel: FormViewModel): Map<FormElementId, FileUploadValue[]>{
  const uploadedFiles:Map<FormElementId, FileUploadValue[]> = new Map();
  (transaction.references || []).forEach((item) => {
    if (!item.type && !item.is_deleted) {
      try {
        const customData = JSON.parse(item.custom_data)
        const elementId = customData.elementId
        if(elementId){
          const refLink = ObjectUtils.getBySPath(board,`reference_links[sequence=${item.board.reference_links[0].sequence}]`)
          const SPath = ObjectUtils.toSPath(refLink.board)
          const file = ObjectUtils.getBySPath(board, SPath)
          const resource = board.resources?.find(resource => resource.sequence === file.original)
          const fileModel:CBoardFile = FileFormatter.transformFiles([file],board)[0]
          if(!uploadedFiles.has(elementId)){
            uploadedFiles.set(elementId,[])
          }
          uploadedFiles.get(elementId).push({
            refSeq: item.sequence,
            SPath,
            uuid: customData.uuid,
            thumbnail: fileModel.thumbnail,
            type: fileModel.fileType,
            name: fileModel.name,
            size: file.resourceSize || resource?.content_length,
            resSeq: resource?.sequence
          } as FileUploadValue)
        }
      }catch (e) {
        throwErrorForDev(e)
      }
    }
  })
  FormFactoryManager.process(formViewModel,(model)=>{
    if(model.type === FormElementType.FileUpload){
      /**
       * The value of the upload file is computed solely from the references.
       * When there is no value in the references, we need to set the default value to an empty array.
       * case: The UI does not update when the last file is deleted.
       */
      if(!uploadedFiles.has(model.id)){
        uploadedFiles.set(model.id,[])
      }
    }
  })
  return uploadedFiles
}
export interface StepAssigneeBaseInfo {
  isTeamUser: boolean;
  stepSequence: number;
  teamId: string;
}
function transformFillFormModels(
  boardId: string,
  transaction: Defines.BoardTransaction,
  members: Map<UserId, CBoardUser>,
  formViewModel: FormViewModel
) {
  const currentUser = MxISDK.getCurrentUser().basicInfo
  const assignee = members.get(currentUser.id)
  const filledStatus: Map<UserId, ElementFilledStatusViewModel> = new Map()
  const filledValues: Map<FormElementId, ElementFilledValueViewModel> = new Map()
  const myFilledValues: Map<FormElementId, ElementFilledValueViewModel> = new Map()
  const userIds = Array.from(members.keys())
  const stepOfCurrentUser = getMyStep(transaction.steps)
  const uploadedFileRefs: Map<ReferenceSequence, FormAttachmentBaseInfo> = getUploadedFileRefs(
    transaction
  )
  let urlCallbackInfo
  if (transaction.callback_url) {
    let action
    try {
      action = JSON.parse(stepOfCurrentUser.actions)[0]
    } catch (e) {}

    urlCallbackInfo = {
      url: transaction.callback_url,
      payload: {
        binder_id: boardId,
        transobject_id: transaction.sequence,
        step_id: stepOfCurrentUser.sequence,
        button_type: 'submit_form',
        payload: action && action.payload,
        assignee: {
          user_id: currentUser.id,
          name: assignee?.displayName,
          email: currentUser.email || undefined,
          unique_id: currentUser.unique_id || undefined,
          phone_number: currentUser.phone_number || undefined
        }
      }
    }
  }

  const existElementsMap = FormFactoryManager.getElements(formViewModel)

  transaction.steps
    .filter((step) => !step.is_deleted)
    .forEach((step) => {
      ;(step.contents || []).forEach((content) => {
        const userStateModel = contentToUserStateModel(content, step)
        let stateMap
        let mapKey

        if (userStateModel.type === FormUserStateType.Status) {
          const currModel = userStateModel as ElementFilledStatusViewModel
          currModel.orderIndex = userIds.indexOf(userStateModel.assigneeId)
          mapKey = userStateModel.assigneeId
          const assignee = members.get(userStateModel.assigneeId)
          currModel.name = assignee?.displayName
          currModel.avatar = assignee?.avatar
          /**
           * If the user has multiple fill statuses, take the latest one.
           */
          if (filledStatus.has(userStateModel.assigneeId)) {
            const existModel = filledStatus.get(userStateModel.assigneeId)
            if (existModel.updatedTime > userStateModel.updatedTime) {
              return
            }
          }
          /**
           * A user may have previously been an assignee for Step 1 and filled in values,
           * but if they are not the current assignee of Step 1,
           * they should not be able to modify the status within Step 1.
           * For the current user, we can only store the data from the steps they are currently assigned to in filledStatus.
           */
          if (userStateModel.assigneeId === currentUser.id) {
            if(userStateModel.stepSequence !== stepOfCurrentUser.sequence) {
              return
            }
          }
          filledStatus.set(userStateModel.assigneeId, currModel)
        } else if(userStateModel.type === FormUserStateType.Value) {
          const currModel = userStateModel as ElementFilledValueViewModel
          mapKey = userStateModel.elementId
          if (!existElementsMap.has(mapKey)) {
            // ignore not exist element
            return
          }
          if (filledValues.has(mapKey)) {
            const existModel = filledValues.get(mapKey) as ElementFilledValueViewModel
            if (existModel.updatedTime > userStateModel.updatedTime) {
              return
            }
          }
          const element = existElementsMap.get(mapKey)
          if ([FormElementType.MultiSelection, FormElementType.SingleSelection,FormElementType.DropdownList].includes(element.type)) {
            /**
             * saved value is label in option
             * ui layer need use value in option
             */
            currModel.value = getValidValuesFromOptions(currModel.value, element as HasOptionsElement)
          }
          console.debug('the value of filled',currModel.value)
          filledValues.set(mapKey, currModel)
          if (currModel.assigneeId === currentUser.id) {
            /**
             * The values of current user entered in other steps case.
             * ignore this value. The current user can only edit the steps that belong to them.
             */
            if (currModel.stepSequence === stepOfCurrentUser.sequence) {
              myFilledValues.set(mapKey, currModel)
            }
          }
        }

      })
    })
  const teamAssignee = get(stepOfCurrentUser, 'assignee.group')
  const stepInfo: StepAssigneeBaseInfo = {
    isTeamUser: false,
    stepSequence: stepOfCurrentUser.sequence,
    teamId: ''
  }
  if (teamAssignee) {
    stepInfo.isTeamUser = true
    stepInfo.teamId = teamAssignee.id
  }

  return {
    stepOfCurrentUser: stepInfo,
    filledStatus,
    filledValues,
    myFilledValues,
    urlCallbackInfo,
    uploadedFileRefs
  }
}


function buildSearchContent(model: FormViewModel) {
  const result = []
  model.pages.forEach((page) => {
    if (!page.isVisible) {
      return
    }
    page.elements.forEach((element: FormAnyElementViewModel) => {
      if (!element.isVisible) {
        return
      }
      switch (element.type) {
        case FormElementType.Heading:
          const formHeading = element as FormElementHeadingModel
          if (formHeading.label) {
            result.push(formHeading.label)
          }
          if (formHeading.supporting) {
            result.push(formHeading.supporting)
          }
          break
        case FormElementType.SingleLineText:
        case FormElementType.MultiLineText:
          if (element.label) {
            result.push(element.label)
          }
          break
        case FormElementType.MultiSelection:
        case FormElementType.DropdownList:
        case FormElementType.SingleSelection:
          result.push((element as FormElementSelectionModels).options?.join(' '))
          break
      }
      const elementModel = element as FormElementInputBaseModel<any>
      if (!elementModel.isProtected) {
        const value = elementModel.value
        if (isObject(value) || Array.isArray(value)) {
          result.push(
            Object.values(value)
              .filter((v) => isDefine(v))
              .join(' ')
          )
        } else if (isDefine(value)) {
          result.push(value)
        }
      }
    })
  })
  return result.join(' ')
}

function applyUserFilledValues(
  formViewModel: FormViewModel,
  filledValues: Map<FormElementId, Partial<ElementFilledValueViewModel>>,
  submittingElements: Map<FormElementId, Partial<ElementFilledFieldValueModel>>,
  focusedElement: Partial<ElementFilledFieldValueModel>,
  currentUserId: UserId,
  options: FormElementTransformOption
) {
  FormFactoryManager.process(formViewModel, (model, factory) => {
    const filledElement = filledValues.get(model.id)
    const submittingElement = submittingElements?.get(model.id)
    const latestModelValue = submittingElement?.value || filledElement?.value

    const focusedElementId = focusedElement?.elementId

    if (filledElement || submittingElement) {
      factory.syncValue(model, latestModelValue, {
        ...options,
        focusedElementId,
        focusedProperty: focusedElement?.attrName
      })
    }
  })
}



export {
  transactionToFormViewModel,
  transformFillFormModels,
  buildSearchContent,
  applyUserFilledValues
}
