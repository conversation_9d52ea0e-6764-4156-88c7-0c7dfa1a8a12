import {FormElementType, FormRuntimeOptions, FormScenes} from "@model/form/defines/shared";
import {CFormModel} from "@controller/defines/CFormModel";
import {FormViewModel} from "@model/form/defines/formViewModel";
import {MxISDK} from "isdk";
import {isMyTeam} from "@newController/utils";
import {isDefine} from "@model/form/factory/shared";
import {isMultipleAssignee} from "@model/form/common/utils";

/**
 * Find the UUID of the file currently filled in the FileUpload element in the form.
 * @param model
 */
export function getFormExistUploadFileIds(model: CFormModel) {
  const uploadFileIds = [];
  model.pages.forEach((page) => {
    page.fields.forEach((field) => {
      if (field.type === FormElementType.FileUpload && field?.value?.uuid) {
        uploadFileIds.push(field.value.uuid);
      }
    })

  });
  return uploadFileIds;
}
export function canShowProtected(formViewModel:FormViewModel){
  let iAmAssignee = false
  if (!formViewModel) {
    return false
  }
  const currentUserId = MxISDK.getCurrentUser().id
  const iAmOwner = formViewModel.ownerIds?.includes(currentUserId)
  if (iAmOwner) {
    return true
  }
  formViewModel.assignees?.forEach((assignee) => {
    if (assignee.id === currentUserId) {
      iAmAssignee = true
    }
    if (assignee.isTeam && isMyTeam(assignee.id)) {
      iAmAssignee = true
    }
  })
  return iAmAssignee
}
export function getBaseRuntimeOption(formViewModel: FormViewModel,baseOptions:Partial<FormRuntimeOptions>): Partial<FormRuntimeOptions>{
  let showProtected = false
  if(!isDefine(baseOptions.scenes)){
    throw  new Error('scenes is required')
  }
  let isTemplate = formViewModel.isTemplate
  if(!isDefine(isTemplate) && isDefine(baseOptions.isTemplate)){
    isTemplate = baseOptions.isTemplate
  }
  let isPDFForm = baseOptions.isPDFForm
  if(!isDefine(isPDFForm)){
    isPDFForm = formViewModel.isPDFForm
  }
  const isPreviewTemplate = isTemplate && (baseOptions.scenes === FormScenes.Preview)
  if((baseOptions.scenes === FormScenes.FillForm) || isPreviewTemplate){
    showProtected = true
  }else{
    showProtected = canShowProtected(formViewModel)
  }
  let isMultipleAssignees = baseOptions.isMultipleAssignees
  if(!isDefine(isMultipleAssignees) && !isPDFForm){
    isMultipleAssignees = isMultipleAssignee(formViewModel)
  }
  return {
    ...baseOptions,
    showProtected,
    isTemplate: isTemplate,
    isMultipleAssignees,
    isPDFForm,
    isCompleted: formViewModel.isCompleted,
  }
}