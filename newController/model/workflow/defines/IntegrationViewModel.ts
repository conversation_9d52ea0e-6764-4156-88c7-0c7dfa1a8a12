import { Defines, IntegrationDefines } from '@commonUtils'
import { Board, DueTimeFrameType } from 'isdk/src/api/defines';

const FlowType = Defines.WorkflowStepType

export enum OAuthFromType {
  ACCOUNT = 'ACCOUNT',
  MARKETPLACE = 'MARKETPLACE',
}

export interface SupportAppViewModel {
  app_id?: string;
  auth_id?: string;
  icon?: string;
  label?: string;
  labelCreate?: string;
  type?: Defines.WorkflowStepType;
  subType?: string;
  color?: string;
  bgColor?: string;
  iconPath?: string;

  // is reconnect case
  isMock?: boolean;
}

export enum IntegrationSubtype {
  INTEGRATIION = 'Integration',
  MARKETPLACE = 'marketPlace',
}

export enum DefaultIntegrationAppType {
  WORKFLOW_STEP_TYPE_DOCUSIGN = FlowType.WORKFLOW_STEP_TYPE_DOCUSIGN,
  WORKFLOW_STEP_TYPE_INTEGRATION = FlowType.WORKFLOW_STEP_TYPE_INTEGRATION
}

export enum IntegrationAppType {
  APP_TYPE_ACTION = 'APP_TYPE_ACTION',
  APP_TYPE_AUTOMATION = 'APP_TYPE_AUTOMATION',
}

export interface AppAction {
  key: string;
  title: string;
}

export interface IntegrationAppViewModel {
  app_id?: string;
  auth_id?: string;
  name?: string;
  type?: Defines.WorkflowStepType;
  subType?: string;
  description?: string;
  created_time?: string;
  updated_time?: string;
  last_access_time?: string;
  // isAdded?: boolean;
  categoryLabel?: string;
  category?: string;
  creatorId?: string;
  actions?: AppAction[];
  isDefault?: boolean;
  icon?: string;
  
  // default docusign and jumio needs
  label?: string;
  color?: string;
  bgColor?: string;
  labelCreate?: string;

  // is reconnect case
  isMock?: boolean;
}

export interface AppConnectionViewModel extends IntegrationDefines.AppConnection {
  // is reconnect case
  isMock?: boolean;
  login_account?: string;
  lastAccessTime?: string;
  //after do oauth, temporarily record the creator of this action for later validateAccount
  //don't need it for editing
  creatorId?: string;
}

interface AssigneeRole {
  role: {
    id: string;
  };
}

interface AssigneeUser {
  user: {
    id: string;
    name?: string;
    email?: string;
    phone_number?: string;
  };
}

interface BuildFunctionInput {
  // input: {
  title: string;
  description: string;
  assignees: (AssigneeRole | AssigneeUser)[];
  [anyCustomFormProp: string]: any;
  // };
}

interface BuildFunctionStep {
  assignee: AssigneeRole | AssigneeUser;
  buttons: {
    title: string;
    style: string; // 'default' or 'branding', display style
    // input will be carried when user clicks this button
    input: {
      [anyCustomFormProp: string]: any;
    };
  };
  order_number: number;
  //any other props
  [propName: string]: any;
}
export interface BuildFunctionFormViewModelOption{
  page_group?: string;
  watch?: string [];
}
export interface BuildFunctionFormViewModel {
  output: {
    form: any[];
    no_more_forms: boolean;
    options?: BuildFunctionFormViewModelOption;
  };
}

export interface BuildFunctionActionViewModel {
  output: {
    action: {
      title: string;
      description: string;
      steps: BuildFunctionStep[];
    };
  };
}

interface ActionFunctionInput {
  [anyCustomFormProp: string]: any;
}

export interface ActionFunctionModel {
  board_id: string;
  transaction_sequence: number;
  step_sequence: number;
  button_id: string;
  input?: ActionFunctionInput;

  app_id: string;
  auth_id: string;
  view_token: string;
  access_token: string;
  creator_user_id: string;
  assignees: AssigneeUser[];
}

//create transaction start
export interface BasicForm {
  title: string;
  description?: string;
  dueDateTimestamp?: number | null;
  dueInTimeframe?: DueTimeFrameType;
  excludeWeekends?: boolean;
  assignee?: object;
}

export interface IntegrationTransaction {
  basicForm?: BasicForm;
  invokedData?: any;
  destBinderId?: string;
  isTemplate?: boolean;
  customData?: {
    app_id?: string;
    app_name?: string;
    display_id?: string;
    auth_id?: string;
    action_key?: string;
    action_title?: string;
    creator_user_name?: string;
    creator_user_id?: string;
  };
}

export interface CreateTransactionViewModel {
  board?: Board;
  viewToken?: string;
}



//create transaction end
