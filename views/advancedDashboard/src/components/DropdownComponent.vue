<template>
  <div
    class="drp-main-container mx-branding-border-action"
    tabindex="0"
    @click="toggleDropdown"
    @focus="onFocus"
    @blur="onBlur"
    @keydown.stop="handleKeyDown"
    @mouseenter="clearableItem = true"
    @mouseleave="clearableItem = false"
  >
    <div v-if="isMultiple" class="multiple-container">
      <div class="chip-container" ref="scrollContainer">
        <div
          v-for="(item, index) in selectedItems"
          :key="item.value"
          class="chip"
          :class="{ 'marked': markedIndex === index }"
        >
          <el-tooltip
            v-if="item.title.length > 8"
            popper-class="my-custom-tooltip"
            :content="item.title"
            effect="dark"
            placement="top"
          >
            <span class="chip-label mx-ellipsis">{{ item.title }}</span>
          </el-tooltip>
          <span v-else :title="item.title" class="chip-label mx-ellipsis">
            {{ item.title }}
          </span>
          <span
            class="remove-chip micon-close"
            :title="$t('remove')"
            @click.stop="removeItem(index)"
          ></span>
        </div>
        <input
          v-model="searchText"
          class="dropdown-search-input"
          @keydown.stop="handleKeyDown"
          @click.stop="toggleDropdown"
          @input="highlightedIndex = -1"
          :placeholder="!selectedItems.length && placeholder"
        />
      </div>
    </div>

    <div v-else class="single-container">
      <div class="chip-container" v-if="selectedItems.length">
        <div class="non-chip-item" :class="{ 'marked': markedIndex === 0 }">
          <el-tooltip
            v-if="selectedItems[0].title.length > 8"
            popper-class="my-custom-tooltip"
            :content="selectedItems[0].title"
            effect="dark"
            placement="top"
          >
            <span class="single-label mx-ellipsis">{{ selectedItems[0].title }}</span>
          </el-tooltip>
          <span v-else :title="selectedItems[0].title" class="single-label mx-ellipsis">
            {{ selectedItems[0].title }}
          </span>
        </div>
      </div>
      <div v-else class="placeholder">
        <input
          v-if="!isMultiple"
          v-model="searchText"
          class="dropdown-search-input single-search"
          @input="highlightedIndex = -1"
          :placeholder="placeholder"
          @keydown.stop="handleKeyDown"
          @click.stop="toggleDropdown"
        />
      </div>
    </div>

    <div v-if="!isMultiple" class="dropdown-wrapper">
      <span
        v-if="!isLoading && clearable && clearableItem && selectedItems.length"
        class="micon-close clear-all-icon"
        @click.stop="clearSelection"
        :title="$t('remove')"
      ></span>

      <div v-if="isLoading" class="report-dropdown-loading">
        <span v-mx-loading-spinner />
      </div>
      <div v-else>
        <span v-if="showDropdown" class="micon-mep-arrow-up dropdown-arrow-indicator"></span>
        <span v-else class="micon-mep-arrow-down dropdown-arrow-indicator"></span>
      </div>
    </div>
    <div
      v-if="showDropdown"
      ref="dropdownContainer"
      class="dropdown-container"
      :style="popupStyle"
      v-clickoutside="handleDropdownClose"
    >
      <ul class="dropdown-list" ref="dropdownList" @scroll="handleScroll">
        <li
          v-for="(item, index) in filteredListItems"
          :key="item.value"
          tabindex="0"
          @click.stop="
            item.value != 'NO_DATA' && item.value != 'MAXIMUM_ITEMS_SELECTED' && selectItem(item)
          "
          :class="{
            selected: isSelected(item),
            'highlighted': highlightedIndex === index,
            'no-data-li': item.value === 'NO_DATA' || item.value === 'MAXIMUM_ITEMS_SELECTED'
          }"
          @mouseover="highlightedIndex = index"
        >
          <span
            v-if="
              type == 'LIST' && item.value != 'NO_DATA' && item.value != 'MAXIMUM_ITEMS_SELECTED'
            "
            class="list-container-div"
            >{{ item.title }}
            <i v-if="isSelected(item)" class="micon-tick selected-indicator-icon"></i>
          </span>
          <span v-else-if="item.value == 'NO_DATA'" class="no-data">{{ $t('no_data') }}</span>
          <span v-else-if="item.value == 'MAXIMUM_ITEMS_SELECTED'" class="no-data">{{
            $t('maximum_of_6_values_allowed')
          }}</span>
          <div v-else class="dropdown-data-container" :title="item.title">
            <div v-if="item.icon" class="icon-container">
              <mx-flow-thumbnail
                v-if="type == 'TEMPLATES'"
                :xWidth="32"
                :width="32"
                :height="36"
                :avatar="item.icon"
              ></mx-flow-thumbnail>
              <img v-if="type == 'USERS'" :src="item.icon" class="icon-cls" />
              <img v-if="type == 'ACTION_TYPES'" :src="item.icon" class="action-icon-cls" />
              <img
                v-if="type == 'TEMPLATE_MILESTONES' || type == 'TEMPLATE_STEPS'"
                :src="item.icon"
                class="milestone-icon-cls"
              />
            </div>
            <span v-if="type == 'ACTION_TYPES'" class="label-text mx-text-c2 mx-ellipsis">{{
              item.title
            }}</span>
            <div v-else class="details-container">
              <div class="title-container">
                <span
                  v-if="type == 'USERS' && item.is_external"
                  :style="imageStyle"
                  class="mep-external-flag"
                ></span>
                <span class="title-name mx-ellipsis" :title="item.title">{{ item.title }}</span>
                <span v-if="type == 'USERS' && item.value == currentUser.id" class="you-indicator"
                  >({{ $t('You') }})</span
                >
              </div>
              <div class="user-sub-title-container mx-ellipsis mx-text-c4">
                {{ item.sub_title }}
              </div>
            </div>

            <i v-if="isSelected(item)" class="micon-tick selected-indicator-icon"></i>
          </div>
        </li>
      </ul>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import MxFlowThumbnail from '@views/common/components/thumbnail/MxFlowThumbnail.vue'
import defaultProfile from '@views/theme/src/images/default/default_profile_2x.png'
import extBadge from '@views/theme/src/images/ext_badge_mep.svg'
import Clickoutside from 'element-ui/src/utils/clickoutside'
export default {
  name: 'DropdownComponent',
  directives: { Clickoutside },
  components: {
    MxFlowThumbnail
  },
  props: {
    value: {
      type: [String, Number],
      default: ''
    },
    listItems: {
      type: Array,
      default: () => []
    },
    isMultiple: {
      type: Boolean,
      default: false
    },
    placeholder: {
      type: String,
      default: 'Select...'
    },
    isFirstSelected: {
      type: Boolean,
      default: false
    },
    isLoading: {
      type: Boolean,
      default: false
    },
    clearable: {
      type: Boolean,
      default: false
    },
    type: {
      type: String,
      default: 'LIST' //TEMPLATES, TEMPLATE_STEPS, TEMPLATE_MILESTONES, USERS, LIST, ACTION_TYPES
    }
  },
  data() {
    return {
      reRenderer: 0,
      defaultProfile,
      markedIndex: null,
      showDropdown: false,
      searchFocused: false,
      highlightedIndex: -1,
      searchText: '',
      // Track selection order for multiselect
      selectionOrder: [],
      // isScrollEnd: false,
      imageStyle: {
        'background-image': 'url(' + extBadge + ')'
      },
      clearableItem: false
    }
  },
  computed: {
    ...mapGetters('user', ['currentUser']),
    selectedItems() {
      if (!this.value || !this.listItems?.length) return []

      if (this.isMultiple) {
        const selected = this.value?.split(',') || []

        // Use selectionOrder to maintain the order of selection
        const orderedItems = []

        // First, add items in the order they were selected
        this.selectionOrder.forEach((orderedValue) => {
          if (selected.includes(orderedValue)) {
            const item = this.listItems.find((item) => item.value === orderedValue)
            if (item) {
              orderedItems.push(item)
            }
          }
        })

        // Then add any remaining selected items that might not be in selectionOrder
        // (this handles edge cases where value prop is updated externally)
        selected.forEach((value) => {
          if (!this.selectionOrder.includes(value)) {
            const item = this.listItems.find((item) => item.value === value)
            if (item && !orderedItems.find((ordered) => ordered.value === value)) {
              orderedItems.push(item)
            }
          }
        })

        return orderedItems
      } else {
        const item = this.listItems.find((item) => item.value === this.value)
        return item ? [item] : []
      }
    },
    filteredListItems() {
      if (this.isMultiple && this.selectedItems.length >= 6) {
        return [
          {
            title: this.$t('maximum_of_6_values_allowed'),
            value: 'MAXIMUM_ITEMS_SELECTED'
          }
        ]
      }

      const text = this.searchText?.toLowerCase() || ''
      let returnValue = []

      if (this.isMultiple) {
        const selectedIds = (this.value || '').split(',')
        returnValue = (this.listItems || []).filter(
          (item) =>
            item?.title?.toLowerCase?.().includes(text) ||
            item?.sub_title?.toLowerCase?.().includes(text) ||
            (item?.value?.toLowerCase?.().includes(text) && !selectedIds.includes(item?.value))
        )
      } else {
        returnValue = (this.listItems || []).filter(
          (item) =>
            item?.title?.toLowerCase?.().includes(text) ||
            item?.sub_title?.toLowerCase?.().includes(text) ||
            (item?.value?.toLowerCase?.().includes(text) && this.value !== item?.value)
        )
      }

      if (!returnValue.length) {
        returnValue = [
          {
            title: this.$t('no_data'),
            value: 'NO_DATA'
          }
        ]
      }

      return returnValue
    },
    popupStyle() {
      if (!this.$el) return {}

      if (this.showDropdown && this.reRenderer) {
      }

      const rect = this.$el.getBoundingClientRect()
      const itemCount = this.filteredListItems.length
      const itemHeight = 52
      const maxHeight = 350
      const popupHeight = Math.min(itemCount * itemHeight, maxHeight)

      const spaceBelow = window.innerHeight - rect.bottom
      const spaceAbove = rect.top
      const showAbove = spaceBelow < popupHeight && spaceAbove >= popupHeight

      return {
        position: 'fixed',
        top: `${showAbove ? rect.top - popupHeight - 8 : rect.bottom + 2}px`,
        left: `${rect.left}px`,
        zIndex: 1000
      }
    }
  },
  watch: {
    showDropdown(val) {
      if (val) {
        this.$nextTick(() => {
          // Find the index of the selected item when dropdown opens
          if (!this.isMultiple && this.selectedItems.length) {
            const index = this.filteredListItems.findIndex(
              (item) => item.value === this.selectedItems[0].value
            )
            if (index !== -1) {
              this.highlightedIndex = index
            }
          }
          // Focus on search input when dropdown opens for single select
          if (!this.isMultiple) {
            const searchInput = this.$el.querySelector('.single-search')
            if (searchInput) {
              searchInput.focus()
            }
          }
        })
        // Add event listener to detect clicks outside
        document.addEventListener('click', this.closeDropdown)
      } else {
        document.removeEventListener('click', this.closeDropdown)
        // Reset search text when dropdown closes
        this.searchText = ''
        // this.isScrollEnd = false
      }
    },
    searchText() {
      this.$emit('onSearch', this.searchText)
    },
    // Watch for external changes to value prop and sync selectionOrder
    value: {
      immediate: true,
      handler(newValue) {
        // Handle empty/falsy values
        if (!newValue || newValue === '' || newValue === '0') {
          this.selectionOrder = []
          return
        }

        newValue = String(newValue)
        if (this.isMultiple && newValue) {
          const newSelected = newValue.includes(',') ? newValue.split(',') : [newValue]

          // Clear and rebuild selectionOrder to prevent duplicates
          this.selectionOrder = []

          // Update selectionOrder to include any new values not already tracked
          newSelected.forEach((value) => {
            if (value && value.trim() !== '') {
              this.selectionOrder.push(value)
            }
          })

          // Remove from selectionOrder any values that are no longer selected
          // this.selectionOrder = this.selectionOrder.filter((value) => newSelected.includes(value))
        } else if (!this.isMultiple) {
          // For single select, clear the selection order
          this.selectionOrder = []
        }
      }
    }
  },
  mounted() {
    if (this.isFirstSelected && this.listItems.length > 0) {
      this.$emit('input', String(this.listItems[0].value))
      this.$emit('change', this.listItems[0])
    }

    // Initialize selectionOrder from existing value
    if (this.isMultiple && this.value) {
      this.selectionOrder = this.value.split(',')
    }
  },
  beforeDestroy() {
    document.removeEventListener('click', this.closeDropdown)
  },
  methods: {
    findScrollableParent(el) {
      let parent = el.parentElement
      while (parent) {
        const style = window.getComputedStyle(parent)
        const overflowY = style.overflowY

        // Checking for scrollable conditions
        if (
          (overflowY === 'auto' || overflowY === 'scroll') &&
          parent.scrollHeight > parent.clientHeight
        ) {
          return parent
        }

        // Move to the next parent in the chain
        parent = parent.parentElement
      }

      // No scrollable parent found
      return null
    },
    // Scroll event handler
    handleScrollevent(event) {
      const rect = this.$el.getBoundingClientRect()
      const isVisible = rect.top >= 0 && rect.bottom <= window.innerHeight - 100

      if (!isVisible) {
        this.showDropdown = false
      }
      this.reRenderer++
    },
    onPositionChange() {
      this.$forceUpdate()
    },
    toggleDropdown() {
      if (this.isMultiple) {
        this.scrollToRight()
      }

      this.showDropdown = !this.showDropdown

      if (this.showDropdown) {
        // 👇 initialize highlight
        this.highlightedIndex = this.filteredListItems.length > 0 ? 0 : -1

        const scrollContainer = this.findScrollableParent(this.$el)
        if (scrollContainer) {
          scrollContainer.addEventListener('scroll', this.handleScrollevent)
        }
      }
    },
    onFocus() {
      this.searchFocused = true
    },
    onBlur() {
      this.searchFocused = false
    },
    closeDropdown(event) {
      // Check if click is outside the component
      if (!this.$el.contains(event.target)) {
        this.showDropdown = false
        // Find the scrollable parent container
        const scrollContainer = this.findScrollableParent(this.$el)
        if (scrollContainer) {
          scrollContainer.removeEventListener('scroll', this.handleScrollevent)
        }
      }
    },
    handleDropdownClose() {
      this.showDropdown = false
    },
    removeItem(index) {
      // Check if selectedItems exists and has the correct length
      if (!this.selectedItems || this.selectedItems.length === 0) {
        return // No items to remove, exit early
      }

      if (this.isMultiple) {
        // Check if index is valid before proceeding
        if (index < 0 || index >= this.selectedItems.length) {
          return // Invalid index, exit early
        }

        const itemToRemove = this.selectedItems[index]
        const updated = [...this.selectedItems] // Create a copy of selectedItems
        updated.splice(index, 1) // Remove the item at the specified index

        // Remove from selectionOrder as well
        this.selectionOrder = this.selectionOrder.filter((value) => value !== itemToRemove.value)

        // If no items are left, close the dropdown
        if (updated.length === 0) {
          this.handleDropdownClose()
        }

        // Emit updated values as a comma-separated string of values
        const updatedValues = updated.map((item) => item.value)
        this.$emit('input', updatedValues.join(','))
        this.$emit('change', updatedValues.join(','))
      } else {
        // For single selection, reset the value to an empty string
        this.$emit('input', '')
        this.$emit('change', '')
      }

      // Reset marked index if it matches the removed item's index
      if (this.markedIndex === index) {
        this.markedIndex = null
      }
    },
    selectItem(item) {
      this.clearableItem = false
      if (this.isMultiple) {
        this.searchText = ''
        const existing = this.selectedItems.map((i) => i.value)
        if (!existing.includes(item.value)) {
          // Add to selectionOrder to maintain order
          this.selectionOrder.push(item.value)

          const updatedItems = [...this.selectedItems, item]
          const values = updatedItems.map((i) => i.value).join(',')
          this.$emit('input', String(values))
          this.$emit('change', updatedItems)
        }
        this.showDropdown = false
      } else {
        this.$emit('input', String(item.value))
        this.$emit('change', item)
        this.showDropdown = false
      }
    },
    isSelected(item) {
      if (this.isMultiple) {
        return this.selectedItems.some((selected) => selected.value === item.value)
      } else {
        return this.value === item.value
      }
    },
    scrollToRight() {
      const el = this.$refs.scrollContainer
      if (el) el.scrollLeft = el.scrollWidth
    },
    handleKeyDown(e) {
      if (this.showDropdown) {
        switch (e.key) {
          case 'ArrowDown':
            e.preventDefault()
            this.highlightedIndex = this.highlightedIndex === -1 ? 0 : Math.min(this.highlightedIndex + 1, this.filteredListItems.length - 1)
            this.scrollToHighlighted()
            break

          case 'ArrowUp':
            e.preventDefault()
            this.highlightedIndex =
              this.highlightedIndex === -1
                ? this.filteredListItems.length - 1
                : Math.max(this.highlightedIndex - 1, 0)
            this.scrollToHighlighted()
            break

          case 'Enter':
            e.preventDefault()
            if (this.highlightedIndex >= 0 && this.filteredListItems[this.highlightedIndex]) {
              const item = this.filteredListItems[this.highlightedIndex]
              if (item.value !== 'NO_DATA' && item.value !== 'MAXIMUM_ITEMS_SELECTED') {
                this.selectItem(item)
              }
            }
            break
          case 'Escape':
            e.preventDefault()
            this.showDropdown = false
            this.markedIndex = null
            break
          case 'Backspace':
            this.handleBackspaceInDropdown(e)
            break
        }
      } else {
        switch (e.key) {
          case 'Enter':
          case ' ':
          case 'ArrowDown':
            e.preventDefault()
            this.showDropdown = true
            break
          case 'Backspace':
            this.handleBackspaceWhenClosed(e)
            break
        }
      }
    },
    handleBackspaceInDropdown(e) {
      if (this.isMultiple && this.searchText === '' && this.selectedItems.length > 0) {
        e.preventDefault()
        if (this.markedIndex !== null) {
          // Second backspace: remove the marked item
          this.removeItem(this.markedIndex)
        } else {
          // First backspace: mark the last selected item
          this.markedIndex = this.selectedItems.length - 1
        }
      }
    },
    handleBackspaceWhenClosed(e) {
      if (this.isMultiple && this.selectedItems.length > 0) {
        e.preventDefault()
        if (this.markedIndex !== null) {
          // Second backspace: remove the marked item
          this.removeItem(this.markedIndex)
        } else {
          // First backspace: mark the last selected item
          this.markedIndex = this.selectedItems.length - 1
        }
      }
    },
    scrollToHighlighted() {
      this.$nextTick(() => {
        if (this.$refs.dropdownList && this.highlightedIndex >= 0) {
          const highlighted = this.$refs.dropdownList.children[this.highlightedIndex]
          if (highlighted) {
            const listRect = this.$refs.dropdownList.getBoundingClientRect()
            const itemRect = highlighted.getBoundingClientRect()

            if (itemRect.bottom > listRect.bottom) {
              this.$refs.dropdownList.scrollTop += itemRect.bottom - listRect.bottom
            } else if (itemRect.top < listRect.top) {
              this.$refs.dropdownList.scrollTop -= listRect.top - itemRect.top
            }
          }
        }
      })
    },
    // New method to handle scroll events
    handleScroll(e) {
      const target = e.target
      // Check if scrolled to bottom
      if (target.scrollHeight - target.scrollTop <= target.clientHeight + 1) {
        this.$emit('scroll-end')
      }
    },
    // Clear all selections
    clearSelection() {
      if (this.isMultiple) {
        this.selectionOrder = [] // Clear selection order
        this.$emit('input', [])
        this.$emit('change', [])
      } else {
        this.$emit('input', '')
        this.$emit('change', '')
      }
      this.markedIndex = null
    }
  }
}
</script>

<style lang="scss" scoped>
.drp-main-container {
  border: 1px solid #8a8a8a;
  background-color: white;
  border-radius: 6px;
  width: 100%;
  min-width: 170px;
  width: 100%;
  height: 36px;
  position: relative;
  cursor: pointer;
  display: flex;
  align-items: center;
  outline: none;
  padding: 2px;
}

.drp-main-container:hover {
  border-width: 2px;
  padding: 1px;
}

.drp-main-container:focus {
  border-width: 2px;
  padding: 1px;
}

.chip-container {
  display: flex;
  align-items: center;
  flex: 1;
  height: 100%;
  flex-direction: row;
  overflow-x: auto;
}

.chip {
  background: #f4f4f4;
  border: 0.5px solid #e0e0e0;
  border-radius: 6px;
  padding: 2px 4px 2px 6px;
  margin: 0 4px;
  font-size: 12px;
  display: flex;
  align-items: center;
  user-select: none;
}

.chip:hover {
  background: #e0e0e0;
}

.chip-label {
  max-width: 70px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.chip.marked {
  border: 1px solid #e0e0e0;
  background: #e0e0e0;
}

.remove-chip {
  margin-left: 6px;
  cursor: pointer;
  color: #888;
  font-size: 16px;
  padding: 1px;
  border-radius: 4px;
}

.remove-chip:hover {
  background-color: #f4f4f4;
}

.dropdown-container {
  position: absolute;
  top: 34px;
  left: 0;
  max-height: 350px;
  min-width: 250px;
  max-width: 285px;
  z-index: 2000;
  margin-top: 5px;
  background: white;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
}

.dropdown-container:hover {
  top: 32px;
}

.dropdown-list {
  width: 100%;
  border: 1px solid #dcdfe6;
  border-radius: 6px;
  background: white;
  overflow-x: hidden;
  overflow-y: auto;
  padding: 5px 0;
  list-style: none;
  margin: 0;
}

.dropdown-list li {
  padding: 8px 10px;
  cursor: pointer;
}

.dropdown-list li:hover {
  background-color: #f5f7fa;
}

.placeholder {
  color: #c0c4cc;
  font-size: 14px;
}

.dropdown-icon {
  font-size: 12px;
  color: #c0c4cc;
  transition: transform 0.3s;
}

.dropdown-icon.is-reverse {
  transform: rotate(-180deg);
}

.single-label {
  max-width: 160px;
  min-width: 90px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.dropdown-arrow-indicator {
  font-size: 16px;
  color: #8d9199;
  position: absolute;
  right: 10px;
  bottom: -7px;
}

.dropdown-search-input {
  border: none;
  outline: none;
  width: 100%;
  min-width: 150px;
  color: #000;
  font-size: 14px;
  border-radius: 6px;
  box-sizing: border-box;
}

.dropdown-search-input.single-search {
  background-color: #fff;
}

.multiple-container {
  width: 100%;
  padding: 0px 6px;
}

.single-container {
  width: 100%;
  position: relative;
  padding: 0px 6px;
}

.dropdown-wrapper {
  position: relative;
  display: inline-block;
}

.clear-all-icon {
  position: absolute;
  right: 8px;
  border-radius: 20px;
  top: -8px;
  z-index: 1000;
  font-size: 12px;
  padding: 1px;
  transition: opacity 0.2s ease;
  cursor: pointer;
  background-color: #616161;
  color: #fff;
  opacity: 1;
}

::-webkit-scrollbar {
  height: 4px;
  width: 4px;
}

::-webkit-scrollbar-thumb {
  background: #999;
  border-radius: 2px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.dropdown-data-container {
  display: flex;
  gap: 12px;
  align-items: center;
  flex-direction: row;
}

.icon-cls {
  height: 28px;
  width: 28px;
  border-radius: 4px;
}

.action-icon-cls {
  height: 28px;
  width: 28px;
  border-radius: 4px;
}

.title-container {
  display: flex;
  gap: 4px;
  align-items: center;
}

.mep-external-flag {
  height: 16px;
  width: 16px;
  background-repeat: no-repeat;
  display: flex;
  justify-content: center;
  align-items: center;
}

.title-name {
  max-width: 190px;
}

.user-sub-title-container {
  width: 220px;
  color: #616161;
}

.selected-indicator-icon {
  font-size: 12px;
  margin-left: auto;
  display: flex;
  align-items: center;
}

.list-container-div {
  display: flex;
}

.milestone-icon-cls {
  height: 36px;
  width: 40px;
  border-radius: 4px;
}

.report-dropdown-loading {
  margin-right: 8px;
  margin-top: -3px;

  div {
    margin: auto !important;
  }

  .mx-spinner-border {
    height: 14px !important;
    width: 14px !important;
  }
}

.no-data {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  color: #8a8a8a;
  font-size: 14px;
  background-color: white !important;
  user-select: none;
  cursor: auto;
}

.no-data-li {
  background-color: white !important;
  cursor: auto;
}

.highlighted {
  background-color: #f5f7fa;
}
</style>
