<template>
  <div :class="hasSpaceAndMax ? 'kanban-container-section-min' : 'kanban-container-section-max'"
    class="calculator-container">
    <div v-show="getShowSpinner" id="overlaye">
      <span v-mx-loading-spinner="getShowSpinner" />
    </div>
    <div class="kanban-title">
      <div class="kanban-title-child">
        <span :class="bindStatusColor(kanbanData)" class="titl mx-ellipsis" :title="processTitle(kanbanData.title)"> {{
          processTitle(kanbanData.title) }}</span>
        <span class="gray-color mx-text-c2"> ({{ kanbanData.total_records || 0 }})</span>
      </div>
      <el-popover popper-class="dark-mode-flow btn-normal" effect="dark" class="drag-icon-holder" placement="top"
        trigger="hover">
        <div class="mx-text-c3" style="color: #FFFFFF">
          {{ $t("drag_column_to_reorder") }}
        </div>
        <i slot="reference" class="micon-handle drag-handle" />
      </el-popover>
    </div>
    <div v-if="kanbanData.list.length" class="kanban-card-list" @scroll="handleScroll">
      <KanbanCard v-for="(item, index) in kanbanData.list" :item="item" :key="index"
        @openActionPreview="openActionPreview" />
    </div>
    <span style="position: absolute; bottom: 0; left: 50%;" v-show="isLoadingMore"
      v-mx-loading-spinner="isLoadingMore" />
  </div>
</template>
<script>
import { mapGetters, mapActions } from 'vuex'
import KanbanCard from '@views/advancedDashboard/src/components/KanbanCard'
export default {
  name: 'KanbanContainerSection',
  components: {
    KanbanCard,
  },
  props: ['kanbanData', 'numberOfColumns'],
  data() {
    return {
      isLoadingMore: false
    }
  },
  computed: {
    ...mapGetters('user', ['currentUser']),
    ...mapGetters('advancedDashboard', ['currentTabConfig', 'getDashboardType', 'getSearchSize','getShowSpinner']),
    hasSpaceAndMax() {
      if ((window.innerWidth - 253) / this.numberOfColumns < 400) {
        return true
      }
      else {
        return false
      }
    }
  },
  methods: {
    ...mapActions('advancedDashboard', ['fetchWorkspaces', 'fetchActions']),
    openActionPreview(row) {
      if (this.getDashboardType === 'advancedWorkspaceReport') {
        this.openWorkspace(row)
      }
      else {
        this.$emit('openActionPreview', row)
      }
    },
    handleScroll(event) {
      const { scrollTop, scrollHeight, clientHeight } = event.target;
      if (scrollTop + clientHeight >= scrollHeight) {
        if (this.kanbanData.total_records > this.kanbanData.list.length) {
          this.isLoadingMore = true
          const page_number = Math.floor((this.kanbanData.list.length / this.getSearchSize)) + 1

          const hj = this;
          if (this.getDashboardType === 'advancedWorkspaceReport') {
            this.fetchWorkspaces({ is_kanban_fetching: true, page_number: page_number })
              .catch(() => {
                hj.$mxMessage.error(hj.$t("system_unknown_error"));
              }).finally(() => {
                this.isLoadingMore = false
              })
          }
          else {
            this.fetchActions({ is_kanban_fetching: true, page_number: page_number })
              .catch(() => {
                hj.$mxMessage.error(hj.$t("system_unknown_error"));
              }).finally(() => {
                this.isLoadingMore = false
              })
          }
        }
      }
    },
    processTitle(str) {
      if (typeof str === 'string' && str.includes('d@$hbo@rd')) {
        str = str.replace(/d@\$\hbo@rd(?!.*d@\$\hbo@rd)/, '');
      }
      if (str) {
        if (typeof str === 'string') {
          str = str.toUpperCase()
          if (str === 'FLOW_BINDER') {
            return this.$t('Flow_conversation')
          }
          else if (str === 'GROUP_BINDER') {
            return this.$t('Group_Workspace')
          }
          else if (str === 'DIRECT_BINDER') {
            return this.$t('one_on_one_workspace')
          }
          else if (str === 'OPEN') {
            return this.$t('In_Progress')
          }
          else if (str === 'OVERDUE') {
            return this.$t('Overdue')
          }
          else if (str === 'DUE_TODAY' || str === 'DUE TODAY') {
            return this.$t('Due_Today_upper')
          }
          else if (str === 'DUE_TOMORROW' || str === 'DUE TOMORROW') {
            return this.$t('Due_Tomorrow_upper')
          }
          else if (str === 'DUE_IN_7_DAYS' || str === 'DUE IN 7 DAYS') {
            return this.$t('due_in_seven_days')
          }
          else if (str === 'COMPLETED') {
            return this.$t('Completed')
          }
          else if (str === 'CANCELED' || str === 'CANCELLED') {
            return this.$t('Canceled')
          }
          else {
            return str.replace(/_/g, ' ')
              .replace(/\w\S*/g, txt => txt.charAt(0).toUpperCase() + txt.slice(1).toLowerCase());
          }
        }
        else {
          return this.$t('no_tag')
        }
      }
      else {
        return ''
      }
    },
    bindStatusColor(item) {
      if (this.currentTabConfig.kanban_column === 'workspace_status' || this.currentTabConfig.kanban_column === 'action_status') {
        const status = item.title.trim().toUpperCase();
        const statusColors = {
          OPEN: 'status-blue',
          COMPLETED: 'status-green',
          DUE_TODAY: 'status-orange',
          DUE_TOMORROW: 'status-orange',
          DUE_IN_7_DAYS: 'status-orange',
          OVERDUE: 'status-red',
          CANCELLED: 'status-grey',
        };
        return 'mx-text-c1 ' + (statusColors[status] || 'status-blue');
      }
      return 'mx-text-c1';
    }

  }
}
</script>
<style lang="scss" scoped>
.kanban-container-section-min {
  height: calc(100vh - 168px);
  width: 350px;
  border-radius: 8px;
  display: flex;
  border: 2px solid #f4f4f4;
  flex-direction: column;
  background-color: #f5f5f5;
  user-select: none;
  position: relative;
}

.kanban-container-section-max {
  height: calc(100vh - 168px);
  width: 400px;
  border-radius: 8px;
  display: flex;
  border: 2px solid #f4f4f4;
  flex-direction: column;
  background-color: #f5f5f5;
  user-select: none;
  position: relative;
}

.kanban-card-list {
  overflow-y: auto;
  height: 100%;
  gap: 12px;
  display: flex;
  flex-direction: column;
  padding-top: 8px;
  padding-left: 16px;
  padding-right: 16px;
  padding-bottom: 16px;
  border-radius: 8px;
}

.kanban-title {
  padding: 8px 16px 0px 16px;
  display: flex;
  justify-content: space-between;

  .kanban-title-child {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 4px;
  }

  .gray-color {
    color: $mx-color-var-label-secondary;
  }
}

.kanban-title:hover {
  .drag-handle {
    display: flex;
    align-items: center;
    cursor: grab !important;
    color: #bcc2cc;
    font-size: 18px;
  }
}

.drag-handle {
  display: none;
}

.status-blue {
  color: $mx-color-blue;
}

.status-orange {
  color: #da500b;
}

.status-red {
  color: $mx-color-var-negative;
}

.status-green {
  color: $mx-color-var-positive;
}

.status-grey {
  color: #616161;
}

.titl {
  text-transform: capitalize;
  max-width: 280px;
}

.drag-icon-holder {
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
