<template>
  <div
    class="automation-app-data">
    <div v-if="isLoading" v-mx-loading-spinner="isLoading" class="data-loading" />
      <template v-else>
        <div v-if="isIntegrationAutomation" class="integration-data-wrap">
          <AmPreview
            v-if="previewData"
            :integration-options="integrationOptions"
            :invoked-data="previewData"
            @back="handlePreviewBack">
            <ElButton
              type="primary"
              :debounce-click="500"
              :data-ta="'send_btn'"
              size="medium"
              class="mx-ellipsis flex-1"
              :title="$t('add')"
              :disabled="isSending"
              :loading="isSending"
              @button-click="$emit('gotoNext')">
              {{ isEditMode? $t('save_changes'): $t('Add_Automation') }}
            </ElButton>
          </AmPreview>
          <IntegrationFormView
            v-show="!previewData"
            class="automation-form"
            :key="connector ? connector.auth_id : 'integrationKey'"
            :integrationOptions="integrationOptions"
            :cachedValues.sync="cachedFormValues"
            @success="handleFormSubmit"
          >
          </IntegrationFormView>
        </div>
        <ContentWrapper v-else :cusStyle="contentStyle"
                        :footerStyle="footerStyle"
                        :style="cWrapperStyle"
                        class="automation-app-data">
          <template slot="content">
        <MoxoAppAction
          v-if="isMoxoAutomation"
          ref="moxoApp"
          :activeTabPane="activeTabPane"
          :disable-role-assignee="disableRoleAssignee"
          :template="template" />
        <AmSendGmail
          v-else-if="isGmailAutomation"
          ref="gmailApp"
          :disable-role-assignee="disableRoleAssignee" />
        <AmSlack
          v-else-if="isSlackAutomation"
          ref="slackApp" />
        <AmWebhook
          v-else-if="isWebHookAutomation"
          ref="webhookApp"
          @updateServiceOwnAction="updateServiceOwnAction" />
        <AmBox
          v-else-if="isBoxAutomation"
          ref="boxApp" />
          </template>
          <template v-if="!isLoading"
                    slot="footer">
            <ElButton
              v-if="inputTabShowingActionOwnButton"
              :type="serviceOwnActionButton.buttonType"
              size="medium"
              class="mx-ellipsis flex-1"
              style="margin-right: 12px;"
              :loading="serviceOwnActionButton.isLoading"
              @click="serviceOwnActionButton.clickEvent()">
              {{ serviceOwnActionButton.buttonText }}
            </ElButton>

            <ElButton
              type="primary"
              :debounce-click="500"
              :data-ta="'send_btn'"
              size="medium"
              class="mx-ellipsis flex-1"
              :title="$t('add')"
              :disabled="isSending"
              :loading="isSending"
              @button-click="$emit('gotoNext')">
              {{ isLastTab ? (isEditMode? $t('save_changes'): $t('Add_Automation') ) : $t('next') }}
            </ElButton>
          </template>
        </ContentWrapper>
      </template>

  </div>
</template>

<script>
import MoxoAppAction from '@views/automationBuilder/components/moxo/MoxoAppAction.vue'
import AmSendGmail from '@views/automationBuilder/components/gmail/AmSendGmail.vue'
import AmSlack from '@views/automationBuilder/components/slack/AmSlack.vue'
import AmWebhook from '@views/automationBuilder/components/webhook/AmWebhook.vue'
import AmBox from '@views/automationBuilder/components/box/AmBox.vue'
import ContentWrapper from '@views/integrations/integrationCenter/components/ContentWrapper'
import AmPreview from '@views/integrations/integrationCenter/views/AutomationPreview.vue'
import { useAutomationBuilderStore } from '@views/stores/automationBuilder'
import { mapState, mapActions } from 'pinia'
import { mapGetters as mapVuexGetters } from 'vuex'
import { AmServiceType } from '@model/automation/defines'
import IntegrationFormView from '@views/integrations/integrationCenter/views/IntegrationFormView.vue'
export default {
  name: 'AutomationAppData',
  components: {
    IntegrationFormView,
    AmPreview,
    MoxoAppAction,
    AmSendGmail,
    AmSlack,
    AmWebhook,
    AmBox,
    ContentWrapper
  }, // AmIntegration
  props: {
    isLastTab: {
      type: Boolean,
      default: false
    },
    isEditMode: {
      type: Boolean,
      default: false
    },
    activeTabPane: {
      type: String,
      default: ''
    },
    disableRoleAssignee: {
      type: Boolean,
      default: false
    },
    template: {
      type: Object,
      default: () => ({})
    },
    isSending: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      connector: null,
      inputData: {},
      isLoading: false,
      cachedFormValues:{},
      serviceOwnActionButton: null,
      previewData: null,
      validateTime: 0
    }
  },
  computed: {
    ...mapState(useAutomationBuilderStore, [
      'amICreator',
      'actions',
      'isEdit',
      'isLastIntegrationPage',
      'availableSourceSteps',
      'isAuthValid'
    ]),
    ...mapVuexGetters('user', ['currentUser']),
    action() {
      return this.actions?.[0] || {}
    },
    integrationOptions(){
      return {
        steps: this.availableSourceSteps,
        actionData: this.action.data ||{},
        enableDDR: true,
        appId: this.connector?.app_id,
        authId: this.connector?.auth_id,
        appName: this.action.data?.app_name,
        actionKey: this.action.data?.action_key || ''
      }
    },
    contentStyle() {
      if (this.action.service === AmServiceType.integration) {
        if (this.showFooterBtn) {
          return {
            minHeight: '287px',
            overflow: 'auto',
            maxHeight: 'none',
            height: 'calc(-371px + 100vh)',
            padding: '23px 28px 24px'
          }
        } else {
          return {
            minHeight: '360px',
            overflow: 'auto',
            maxHeight: 'none',
            height: 'calc(100vh - 309px)'
          }
        }
      }
      return {
        overflow: 'auto',
        maxHeight: 'none',
        minHeight: '287px'
      }
    },
    footerStyle() {
      return {
        border: '0px',
        padding: '12px 28px 20px'
      }
    },
    cWrapperStyle() {
      return {
        maxHeight: 'calc(100vh - 309px)',
        display: 'flex',
        flexDirection: 'column'
      }
    },
    inputTabShowingActionOwnButton() {
      return this.serviceOwnActionButton && Object.keys(this.serviceOwnActionButton).length
    },
    showFooterBtn() {
      if (this.action.service === AmServiceType.integration) {
        // return this.action.data?.preview_desc
        return this.isLastIntegrationPage
      }
      return true
    },
    isIntegrationAutomation() {
      return this.action?.service === AmServiceType.integration
    },
    isMoxoAutomation() {
      return this.action?.service === AmServiceType.moxo
    },
    isGmailAutomation() {
      return this.action?.service === AmServiceType.gmail
    },
    isSlackAutomation() {
      return this.action?.service === AmServiceType.slack
    },
    isWebHookAutomation() {
      return this.action?.service === AmServiceType.sendawebhook
    },
    isBoxAutomation() {
      return this.action?.service === AmServiceType.box
    },
    appOauthKey() {
      const { auth_id, data } = this.action || {}
      const { action_key } = data || {}
      return {
        authId: auth_id,
        actionKey: action_key,
        //use isAuthValid: when 'AutomationAppData' is rendered, and "this.validateAccount" fails,
        //the user goes to 'AutomationAccount.vue' view reconnect the account (authId is the same),
        //and after success, the data needs to be reloaded here
        isAuthValid: this.isAuthValid
      }
    }
  },
  watch: {
    appOauthKey: {
      async handler(nVal, oldValue) {
        if (
          nVal.authId === oldValue?.authId &&
          nVal.actionKey === oldValue?.actionKey &&
          nVal.isAuthValid === oldValue?.isAuthValid
        ) {
          return
        }

        if (nVal?.authId && this.isIntegrationAutomation) {
          if (!nVal.isAuthValid) {
            this.connector = null
            return
          }
          const currentValidateTime = Date.now()

          try {
            this.validateTime = currentValidateTime

            this.isLoading = true
            const _connector = await this.validateAccount(
              this.action.auth_id,
              this.action.auth_owner
            )
            if (currentValidateTime < this.validateTime) {
              return
            }
            this.connector = _connector
            // remove cachedFormValues when switch user
            this.cachedFormValues = {}
            this.setAuthValid(true)
          } catch (error) {
            if (currentValidateTime < this.validateTime) {
              return
            }
            this.connector = null
            console.error(error)
            // set auth failed
            this.setAuthValid(false)
          } finally {
            this.isLoading = false
          }
        }
      },
      immediate: true
    },
    'action.service': {
      handler(nVal, oVal) {
        if (nVal !== oVal && nVal === AmServiceType.sendawebhook) {
          this.serviceOwnActionButton = {
            buttonType: 'secondary',
            buttonText: this.$t('test'),
            isLoading: false,
            clickEvent: () => ({})
          }
        } else {
          this.serviceOwnActionButton = null
        }
      },
      immediate: true
    }
  },
  created() {
    //do not include 'isIntegrationAutomation', otherwise the 'isLoading' value will affect each other
    if (this.isEdit && (this.isGmailAutomation || this.isMoxoAutomation)) {
      this.readAutomationDetail()
    }
  },

  methods: {
    ...mapActions(useAutomationBuilderStore, [
      'validateAccount',
      'updateAction',
      'readWorkflowStepDetail',
      'setDDRSourceFields',
      'setAuthValid',
      'invokeBuildFunction',
      'addValidateEvent'
    ]),
    async readAutomationDetail() {
      this.isLoading = true
      await this.readWorkflowStepDetail()
      this.isLoading = false
    },
    handlePreviewBack(){
      this.previewData = null
    },
    async handleFormSubmit ({formArray, invokedData}) {
      this.previewData = invokedData
      const preview_desc = invokedData?.automation?.description
      this.updateAction({
        data: {
          user_input: formArray,
          preview_desc
        }
      })

      const ddrSourceFields = invokedData?.variables?.map(v => {
        return {
          key: v.key,
          type: v.type,
          label: v.label
        }
      })
      // //The fields will be used as DDR Source
      this.setDDRSourceFields(ddrSourceFields || [])
    },
    handleDispatchEvent(eventName, payload) {
      switch (eventName) {
        case 'updateAction':
          this.updateAction(payload)
          break
        case 'addValidateEvent':
          this.addValidateEvent(payload)
          break
        case 'setDDRSourceFields':
          this.setDDRSourceFields(payload)
          break
        case 'setLastIntegrationPage':
          this.setLastIntegrationPage(payload)
          break
        case 'setInputsError':
          this.setInputsError(payload)
          break
      }
    },
    updateServiceOwnAction(actionButtonInfo) {
      Object.assign(this.serviceOwnActionButton, actionButtonInfo)
    }
  }
}
</script>

<style lang="scss" scoped>
.app-data-wrapper {
  height: 100%;
}

.data-loading {
  height: 100%;
  border-radius: 6px;
  background: $mx-color-var-white;
  padding-top: 40px;
}
.automation-form{
  background: #F4F4F4;

  ::v-deep{
    .integration-fill-form-wrap{
      margin: 23px 28px;
      background: #fff;
      border-radius: 6px;
    }
  }
}
.integration-data-wrap {
  height: 100%;
  border-radius: 6px;
  background-color: $mx-color-var-white;

  .go-back-nav-icon {
    display: flex;
    align-items: center;
    color: $mx-color-var-text-secondary;
  }
}
.automation-app-data {
  height: 100%;
}
@media screen and (max-height: 666px) {
  .automation-app-data {
    height: 356px;
    max-height: none !important;
  }
}
</style>