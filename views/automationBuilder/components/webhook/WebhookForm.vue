<template>
  <ElForm
    ref="commonForm"
    class="new-form"
    :model="formData"
    :rules="rules">
    <URLFormItem 
      prop="webhook_url"
      style="margin-bottom: 24px;"
      :label="`${$t(labelText)}*`"
      :disabled="disabled"
      :readonly="readonly"
      :placeholder="webhookUrlPlaceholder"
      :url.sync="formData.webhook_url" />
    <HeadersFormItem 
      v-if="enableHeaders"
      prop="headers"
      style="margin-bottom: 24px;"
      :disabled="disabled"
      :readonly="readonly"
      :label="$t('headers')"
      :headers.sync="formData.headers"
      :placeholder="headersPlaceholder" />
  </ElForm>
</template>

<script>
import { StringUtils } from '@commonUtils/string'
import URLFormItem from './components/URLFormItem'
import HeadersFormItem from './components/HeadersFormItem'

export default {
  name: 'WebhookForm',
  components: {
    URLFormItem,
    HeadersFormItem
  },
  props: {
    presetWebhook: {
      type: Object,
      default: () => ({})
    },
    disabled: {
      type: Boolean,
      default: false
    },
    readonly: {
      type: <PERSON><PERSON><PERSON>,
      default: false
    },
    labelText: {
      type: String,
      default: 'webhook_url'
    },
    enableHeaders: {
      type: Boolean,
      default: true
    },
    urlPlaceholder: {
      type: String,
      default: 'https://example.webhook.com'
    }
  },
  data () {
    return {
      formData: {
        webhook_url: '',
        headers: ''
      },
      // isVerifyUrlError: false,
      rules: {
        webhook_url: {
          validator: (rule, value, callback) => {
            if (!value?.trim()) {
              // this.isVerifyUrlError = true
              callback(new Error(this.$t('Required_field')))
            } else {
              if (!StringUtils.isValidURL(value?.trim())) {
                // this.isVerifyUrlError = true
                callback(new Error(this.$t('invalid_url')))
              } else {
                // this.isVerifyUrlError = false
                callback()
              }
            }
          },
          trigger: 'blur'
        },
        headers: {
          validator: (rule, value, callback) => {
            if (!value?.trim()) {
              callback()
            } else {
              if (this.isValidJSONArrayString(value)) {
                callback()
              } else {
                callback(new Error(this.$t('use_json_array_format')))
              }
            }
          },
          trigger: 'blur'
        }
      }
    }
  },
  computed: {
    webhookUrlPlaceholder () {
      return this.readonly ? '' : this.urlPlaceholder
    },
    headersPlaceholder () {
      return this.readonly ? '' : '["name1: value1", "name2: value2"]'
    }
  },

  mounted () {
    if (Object.keys(this.presetWebhook).length > 0) {
      const tempData = this.presetWebhook
      const { webhook_url, headers } = tempData

      this.formData.webhook_url = webhook_url?.trim()?.substr(0, 256) || ''
      this.formData.headers     = headers?.trim()?.substr(0, 1200) || ''
    }
  },
  methods: {
    clearValidate (prop){
      this.$refs.commonForm.clearValidate(prop)
    },
    isValidJSONString (str) {
      try {
          JSON.parse(str)
      } catch (e) {
          return false
      }
      return true
    },

    isValidJSONArrayString (str) {
      if (!this.isValidJSONString(str)) {
        return false
      }

      let arr
      try {
        arr = JSON.parse(str)
      } catch (e) {
        return false
      }

      if (!Array.isArray(arr) || arr.length <= 0) {
        return false
      }

      for (const item of arr) {
        if (typeof item !== 'string') {
          return false
        }

        // 检查字符串是否由唯一的 ':' 隔开的两段非空子串组成
        const parts = item.split(':')
        if (parts.length !== 2 || parts[0] === '' || parts[1] === '') {
          return false
        }
      }

      return true

      // 测试
      // console.log(isValidJSONArrayString('["key1:value1", "key2:value2"]')); // true
      // console.log(isValidJSONArrayString('["key1:value1", "key2:"]')); // false
      // console.log(isValidJSONArrayString('["key1:value1", "key2:value2:value3"]')); // false
      // console.log(isValidJSONArrayString('["key1:value1", 123]')); // false
      // console.log(isValidJSONArrayString('{"key1":"value1"}')); // false
      // console.log(isValidJSONArrayString('not a json')); // false
    },
    getFormData () {
      return new Promise((resolve, reject) => {
        this.$refs.commonForm.validate((valid) => {
          if (valid) {
            resolve(this.formData)
          } else {
            reject()
          }
        })
      })
    }
  },
  watch: {
    'formData.webhook_url': {
      handler(newVal) {
        this.$emit('update:webhook_url', newVal)
      },
      deep: true
    }
  }
}
</script>

<style scoped lang="scss">

  ::v-deep {
    .el-form-item__label {
      margin: 0px 0px 7px 0px !important;
      padding: 1px 4px 0px 4px !important;
    }
    .el-form-item__content {
      .el-form-item__error {
        display: flex;
        flex-direction: row;
        justify-content: flex-start;
        align-items: center;

        margin: 4px 8px 0px 4px;
        position: relative;
      }
    }
  }

</style>
