<template>
  <BaseModal
    v-mx-ta="{ page: 'resendFlowBinderInvitation', id: 'dialog' }"
    :visible.sync="visibleProxy"
    :modal-option="{
      width: '540px'
    }"
    :title="$t('Resend_Invite')">
    <template 
      slot="content">
      <ElCheckbox
        class="select-all-checkbox"
        v-if="pendingUsers.length > 1"
        :indeterminate="isIndeterminate"
        :disabled="isSending"
        @change="handleSelectAll"
        v-model="isSelectedAll">
        {{ $t('select_all')}}
      </ElCheckbox>
      <div 
        class="user-basic-info">
        <template v-if="pendingUsers.length > 1">
          <ul>
            <li
              v-for="user in pendingUsers"
              :class="{'is-selected': selectedUsers[user.userId]}"
              class="mx-flex-container list-item"
              :key="user.id|| user.userId">
              <ElCheckbox
                @change="handleSelect(user)"
                :disabled="isSending"
                v-model="selectedUsers[user.userId]">
              </ElCheckbox>
              <div class="mx-flex-container align-item-center">
                <MxUserAvatar
                  :user-avatar="user.avatar"
                  :alt="userOrTeamDisplayName(user)" />
                <div
                  class="mx-margin-left-sm display-info"
                  style="overflow-x: hidden; flex: 1;">
                  <div class="mx-text-c2 mx-ellipsis" style="display: flex; align-items: end; gap: 2px;">
                    <div
                      v-if="showClientBadge(user)"
                      :style="{'background-image': `url(${extBadge})`}"
                      class="mep-external-flag" />
                    <span class="user-name"> {{ userOrTeamDisplayName(user) }}</span>
                  </div>
                  <div class="mx-text-c4 mx-color-secondary mx-ellipsis">
                    {{ getSubTitle(user) }}
                  </div>
                </div>
              </div>
            </li>
          </ul>
        </template>
        <div
          v-else
          class="mx-flex-container item">
          <MxUserAvatar
            :user-avatar="pendingUsers[0].avatar"
            :alt="userOrTeamDisplayName(pendingUsers[0])" />
          <div
            class="mx-margin-left-sm display-info"
            style="overflow-x: hidden; flex: 1;">
            <div class="mx-text-c2 mx-ellipsis" style="display: flex; align-items: end; gap: 2px;">
              <div
                v-if="showClientBadge(pendingUsers[0])"
                :style="{'background-image': `url(${extBadge})`}"
                class="mep-external-flag" />
              <span class="user-name"> {{ userOrTeamDisplayName(pendingUsers[0]) }}</span>
            </div>
            <div class="mx-text-c4 mx-color-secondary mx-ellipsis">
              {{ getSubTitle(pendingUsers[0]) }}
            </div>
          </div>
        </div>
      </div>
    </template>
    <template 
      slot="footer">
      <el-button
        type="primary"
        style="width: 100%;"
        :loading="isSending"
        :disabled="isSending"
        @click="resendInvitation">
        {{ $t('resend_invitation') }}
      </el-button>
    </template>
  </BaseModal>
</template>

<script>
import { visibleMixin } from '@views/common/components/modals/mixins'
import { useBoardCommonActionsStore } from '@views/stores/boardCommonActions'
import {mapActions} from 'pinia'
import extBadge from '@views/theme/src/images/ext_badge_mep.svg';

export default {
  name: 'ResendFlowBinderInvitation',
  mixins: [visibleMixin],
  props: {
    user: {
      type: Object,
      default: () => ({})
    },
    boardId: {
      type:String,
      required:true
    },
    boardUsers: {
      type: Array,
      default: () => []
    }
  },
  emits: ['close'],
  data () {
    return {
      isSending: false,
      selectedUsers: {},
      isSelectedAll: false,
      isIndeterminate: false,
      extBadge
    }
  },
  computed: {
    pendingUsers () {
      if(this.user.isTeam) {
        return this.boardUsers.filter(bu => {
          if(bu.isFromTeam && bu.fromTeamIds.includes(this.user.id || this.user.teamId) && !bu.isJoined) {
            return true
          }
        })
      } else {
        return [this.user]
      }
    }
  },
  created () {
    this.pendingUsers.forEach(user => {
      this.selectedUsers[user.userId] = false
    })
  },
  methods: {
    ...mapActions(useBoardCommonActionsStore, ['resendInvite', 'bulkResendInvite']),
    showClientBadge (user) {
      // MV-16514 for preparer being converted to AssigneeViewModel, use isClient property explictly
      if(user.hasOwnProperty('isClient')) {
        return user.isClient
      }
      return !user.isInternalUser
    },
    getSubTitle (user) {
      return user.subTitle || user.title
    },
    handleSelect () {
      const keys = Object.keys(this.selectedUsers)
      const len = keys.filter(key => this.selectedUsers[key]).length
      if(len > 0) {
        if(len === keys.length) {
          this.isSelectedAll = true
          this.isIndeterminate = false
        } else {
          this.isIndeterminate = true
          this.isSelectedAll = false
        }
      } else {
        this.isSelectedAll = false
        this.isIndeterminate = false
      }
    },
    userOrTeamDisplayName (user) {
      // MV-16022 : boardUserViewModel new data type
      if (user.isFromTeam){
        return user.name;
      } else{
        return user.displayName || user.name;
      }
    },
    handleSelectAll (val) {
      Object.keys(this.selectedUsers).forEach(key => {
        this.selectedUsers[key] = val
      })
      this.isIndeterminate = false
    },
    resendInvitation () {
      this.isSending = true
      if(this.pendingUsers.length > 1) {
        const users = []
        this.pendingUsers.forEach(pu => {
          if(this.selectedUsers[pu.userId]) {
            users.push({
              id: pu.userId,
              email: pu.email,
              phone_number: pu.phoneNum || pu.phone_number || pu.display_phone_number,
              unique_id: pu.uniqueId || pu.unique_id
            })
          }
        })
        this.bulkResendInvite(this.boardId, users).then(() => {
          this.$mxMessage.success(this.$t('Invitation_sent'))
          this.$emit('close')
        })
          .catch(() => {
            this.$mxMessage.error(this.$t('unable_to_resend_invitation'), {
              retryTxt: this.$t('retry'),
              callback: this.resendInvitation
            })
          })
          .finally(() => {
            this.isSending = false
          })
      } else {
        const user = this.pendingUsers[0]
        const requestUser = {
          id: user.userId || user.id,
          email: user.email,
          unique_id: user.unique_id || user.uniqueId,
          phone_number: user.phone_number || user.phoneNum || user.display_phone_number
        }
        this.resendInvite(this.boardId, requestUser)
          //binderStore.dispatch('resendInvite', props.user)
          .then(() => {
            this.$mxMessage.success(this.$t('Invitation_sent'))
            this.$emit('close')
          })
          .catch(() => {
            this.$mxMessage.error(this.$t('unable_to_resend_invitation'), {
              retryTxt: this.$t('retry'),
              callback: this.resendInvitation
            })
          })
          .finally(() => {
            this.isSending = false
          })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
  ::v-deep {
    .el-dialog__footer {
      box-shadow: none;
      margin-top: 74px;
      padding: 12px 28px;
    }
    .el-dialog__body {
      padding-left: 28px;
    }

    .user-basic-info {
      ul {
        display: flex;
        flex-direction: column;
        gap: 12px;
      }
    }
  }
  .select-all-checkbox {
    margin-left: 12px;
    margin-bottom: 12px;
  }
  .item {
    border-radius: 6px;
    align-items: center;
    padding: 12px 12px 12px 24px;
    background-color: $mx-color-var-fill-quaternary;
  }
  .list-item {
    padding: 12px 11px;
    gap: 12px;
    border: 1px solid $mx-color-var-card-border;
    border-radius: 6px;
    height: 60px;
    align-items: center;
    &.is-selected {
      background-color: $mx-color-var-fill-quaternary;
      border-color: $mx-color-var-fill-quaternary;
    }
  }
  .mep-external-flag {
    background-repeat: no-repeat;
    width: 16px;
    height: 16px;
    display: inline-block;
    vertical-align: middle;
    flex-shrink: 0;
    margin-right: 2px
  }
  .user-name {
    overflow: hidden;
    text-overflow: ellipsis;
    -o-text-overflow: ellipsis;
    -ms-text-overflow: ellipsis;
    white-space: nowrap;
    display: block;
  }
</style>
