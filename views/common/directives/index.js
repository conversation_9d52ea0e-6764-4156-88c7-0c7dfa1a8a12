import Spinner from './spinner/spinner';
import ImageLoaded from './imagesLoaded/imagesLoaded';
import ClickOutside from './clickOutside/clickOutside';
import Keyboard from './keyboard';
import Hammer from './hammer';
import NotifyElemStatus from './notifyElemStatus/notifyElemStatus';
import TA from './ta/ta'
import Scroll from './scroll/scroll'
import Focus from './focus/focus'
import Debounce from './debounce/debounce'
import Skeleton from './skeleton/skeleton'
import Throttle from './throttle/throttle'
import Loading from './loading/loading'
import LoadingWrap from './loading/loading-wrap'
import A11yGrid from './accessibility/grid'
import A11yDropdown from './accessibility/dropdown'
import A11yFeed from './accessibility/feed'
import A11yTabLoop from './accessibility/tabloop'
import A11yShowHover from './accessibility/showHover'
import A11yList from './accessibility/list'
import A11yCombobox from './accessibility/combobox'
import A11yListKeyboard from './accessibility/listKeyboard'
import HasScrollBar from './scroll/hasScrollBar'
import {BrowserUtils} from '@commonUtils'
import SafeHtml from './safeHtml'
import marked from './marked'
import SafeText from './safeText'
import SafeHref from './safeHref'
import missingField from './missingField'
import verifyForm from './verifyForm'
import ToolTip from './tooltip'
const isMobileMode = BrowserUtils.isMobile || BrowserUtils.isPad || BrowserUtils.isAndroid || BrowserUtils.isIOS || BrowserUtils.isTablet


const directives = {};

directives.install = function (Vue) {
  Vue.directive('mx-spinner', Spinner)
  Vue.directive('mx-image-loaded', ImageLoaded)
  Vue.directive('mx-click-outside', ClickOutside)
  Vue.directive('mx-keyboard', Keyboard)
  Vue.directive('mx-hammer', Hammer)
  Vue.directive('mx-notify-element-status', NotifyElemStatus)
  Vue.directive('mx-ta', TA)
  Vue.directive('mx-scroll', Scroll)
  Vue.directive('mx-focus', Focus)
  Vue.directive('mx-debounce', Debounce)
  Vue.directive('mx-throttle', Throttle)
  Vue.directive('mx-skeleton', Skeleton)
  Vue.directive('mx-loading-spinner', Loading)
  Vue.directive('a11y-grid', A11yGrid)
  Vue.directive('a11y-dropdown', A11yDropdown)
  Vue.directive('a11y-feed', A11yFeed)
  Vue.directive('a11y-tabloop', A11yTabLoop)
  Vue.directive('a11y-show-hover', A11yShowHover)
  Vue.directive('a11y-list', A11yList)
  Vue.directive('a11y-combobox', A11yCombobox)
  Vue.directive('a11y-list-keyboard', A11yListKeyboard)
  Vue.directive('mx-has-scrollbar', HasScrollBar)
  Vue.directive('safe-html', SafeHtml)
  Vue.directive('safe-text', SafeText)
  Vue.directive('safe-href', SafeHref)
  Vue.directive('safe-marked', marked)
  Vue.directive('mx-missing-field', missingField)
  Vue.directive('mx-verify-form', verifyForm)
  Vue.directive('mx-tooltip', ToolTip)
  Vue.directive('mx-loading', LoadingWrap)
};


export default directives;
