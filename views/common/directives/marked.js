import xss from 'xss'
import options from './xssOption'
import MarkdownIt from 'markdown-it';
import mkSanitizer from 'markdown-it-sanitizer'
const md = new MarkdownIt({html: true, linkify: true, typographer: true}).use(mkSanitizer)

const defaultRender = md.renderer.rules.link_open || function (tokens, idx, options, env, self) {
  return self.renderToken(tokens, idx, options);
};
md.renderer.rules.paragraph_open = () => '';
md.renderer.rules.paragraph_close = () => '';
md.renderer.rules.link_open = function (tokens, idx, options, env, self) {
  const token = tokens[idx];
  const aIndex = token.attrIndex('target');
  if (aIndex < 0) {
    token.attrPush(['target', '_blank']);
  } else {
    token.attrs[aIndex][1] = '_blank';
  }
  const relIndex = token.attrIndex('rel');
  if (relIndex < 0) {
    token.attrPush(['rel', 'noopener noreferrer']);
  }
  return defaultRender(tokens, idx, options, env, self);
};


export function safeMarkedHtml(text) {
   const html = md.render(text)
  console.debug(html)
  return xss(html, options)
}

function setHtml(el, text) {
  el.innerHTML = safeMarkedHtml(text)
}
function handleImageClick(ev) {
  if(ev.target.nodeName === 'IMG'){
    const imgElement = ev.target
    const event = new CustomEvent('clickImage', {
      detail:{
        imgUrl: imgElement.src
      },
      bubbles: true
    })
    ev.currentTarget.dispatchEvent(event)
  }

}
const count = 0
export default {
  inserted: function (el, binding, vnode) {
    setHtml(el, binding.value);
    el.addEventListener('click', handleImageClick)
  },
  update: function (el, binding) {
    setHtml(el, binding.value);
  },
  unbind: function (el) {
    if (el) {
      el.innerHTML = ''
      el.removeEventListener('click', handleImageClick)
    }
  }
}


