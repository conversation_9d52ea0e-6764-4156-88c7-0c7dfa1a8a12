import xss from 'xss'
import options from './xssOption'

export function safeHtml(text) {
    return xss(text, options)
}

function setHtml(el, text) {
    el.innerHTML = safeHtml(text)
}

export default {
    inserted: function (el, binding, vnode) {
        setHtml(el, binding.value);
    },
    update: function (el, binding) {
        setHtml(el, binding.value);
    },
    unbind: function (el) {
        if (el) {
            el.innerHTML = ""
        }
    }
}


