
const options = {
  whiteList: {
    a: ["target", "href", "style", "title", "class"],
    abbr: ["style", "title"],
    address: ["style"],
    area: ["shape", "style", "coords", "href", "alt"],
    article: ["style"],
    aside: ["style"],
    audio: [
      "autoplay",
      "controls",
      "crossorigin",
      "loop",
      "muted",
      "preload",
      "style",
      "src",
    ],
    b: ["style"],
    bdi: ["style", "dir"],
    bdo: ["style", "dir"],
    big: ["style"],
    blockquote: ["style", "cite"],
    br: ["style"],
    caption: ["style"],
    center: ["style"],
    cite: ["style"],
    code: ["style"],
    col: ["style","align", "valign", "span", "width"],
    colgroup: ["style","align", "valign", "span", "width"],
    dd: ["style"],
    del: ["style","datetime"],
    details: ["style","open"],
    div: ["style"],
    dl: ["style"],
    dt: ["style"],
    em: ["style"],
    figcaption: ["style"],
    figure: ["style"],
    font: ["style","color", "size", "face"],
    footer: ["style"],
    h1: ["style"],
    h2: ["style"],
    h3: ["style"],
    h4: ["style"],
    h5: ["style"],
    h6: ["style"],
    header: ["style"],
    hr: ["style"],
    i: ["style"],
    img: ["src", "style", "alt", "title", "width", "height"],
    ins: ["style","datetime"],
    li: ["style"],
    mark: ["style"],
    nav: ["style"],
    ol: ["style"],
    p: ["style"],
    pre: ["style"],
    s: ["style"],
    section: ["style"],
    small: ["style"],
    span: ["style", "class"],
    sub: ["style"],
    summary: ["style"],
    sup: ["style"],
    strong: ["style"],
    strike: ["style"],
    table: ["width","style", "border", "align", "valign"],
    tbody: ["align","style", "valign"],
    td: ["width", "rowspan","style", "colspan", "align", "valign"],
    tfoot: ["align", "valign","style"],
    th: ["width", "rowspan", "style","colspan", "align", "valign"],
    thead: ["align", "valign","style"],
    tr: ["rowspan", "align", "valign","style"],
    tt: ["style"],
    u: ["style"],
    ul: ["style"],
    video: [
      "style",
      "autoplay",
      "controls",
      "crossorigin",
      "loop",
      "muted",
      "playsinline",
      "poster",
      "preload",
      "src",
      "height",
      "width",
    ]
  },
  css: false,
  // empty, means filter out all tags
}
export default  options