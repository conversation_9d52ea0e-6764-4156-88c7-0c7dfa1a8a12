import {computed,getCurrentInstance as _getCurrentInstance, reactive, ref, watch} from '@vue/composition-api';
import Vue from 'vue';
import { MxConsts, ObjectUtils } from '../../commonUtils'
import { MxISDK } from 'isdk'
import { PopupManager } from 'element-ui/src/utils/popup'

let globalOpts = null
let rootVm
export function createApplication (opts) {

  const {router, i18n, store, App, container, pinia, props } = opts
  globalOpts = {router, i18n, store}
  rootVm =  new Vue({
    el: container,
    router,
    i18n,
    store,
    pinia,
    components: {App},
    data () {
      return {
        isModalOpened: false,
        appProps: {...props}
      }
    },
    methods: {
      updateAppProps (newProps) {
        this.appProps = {
          ...this.appProps,
          ...newProps
        };
      }
    },
    render (h) {
      return h(App, {
        props: this.appProps
      })
    }
  })
  return rootVm
}

export function getRootVM () {
  return rootVm
}

const popupRoots = new Map()

export function clearAll () {
  popupRoots.forEach(popupRoot => {
    popupRoot.removeAll()
    popupRoot?.$destroy()
  })
  popupRoots.clear()
}
MxISDK.subscribeUserState((type)=>{
  if(type === MxConsts.MxUserState.LOGGED_OUT){
    clearAll()
  }
})

function initPopupContainer (events) {
  // if (!popupRoot) {
  const custProvide = events?.setProvide && events.setProvide() || {}
  const provideObj = {
    viewUserId:'',
    ...custProvide
  }
  let popupRoot = null
  function getVueContainer() {
    if(popupRoot){
      return popupRoot
    }
    const container = document.createElement('div');
    window.mxBodyContainer.appendChild(container)
    popupRoot = new Vue({
      ...globalOpts,
      el: container,
      provide() {
        return provideObj
      },
      data() {
        return {
          childs: []
        }
      },
      methods: {
        addComponent(component, props, events, attrs) {
          if (attrs) {
            const zIndex = PopupManager.nextZIndex()
            attrs.style = `z-index: ${zIndex};`
          }
          this.childs.push({component, props, events, attrs})
        },
        removeComponent(item) {
          let index = -1
          const _scopeId = item._scopeId
          this.childs.forEach(({component}, inx) => {
            if (component._scopeId === _scopeId) {
              index = inx
            }
          })
          if (index >= 0) {
            this.childs.splice(index, 1)
          }
          if (!this.childs.length) {
            this.removeAll()
          }
        },
        removeAll() {
          this.childs.splice(0)
          setTimeout(() => {
            popupRoot?.$destroy()
            popupRoot?.$el?.parentNode && popupRoot.$el.parentNode.removeChild(popupRoot.$el)
            popupRoots.delete(popupRoot?._uid)
            popupRoot = null
          }, 500)
        }
      },
      render (h) {
        return h('div', {
          class: 'popup-container',
          style: {'position': 'absolute'}
        }, this.childs.map(child => {
          const attrs = child.attrs || {}
          return h(child.component, {
            key: child.name,
            on: child.events,
            props: child.props,
            style: attrs.style
          })
        }))
      }
    })
    popupRoots.set(popupRoot._uid, popupRoot)
    return popupRoot
  }

  // window.$root = popupRoot
  // }
  function addComponent (component,props, events, attrs) {
    getVueContainer()?.addComponent( component,props, events, attrs)
  }
  function removeComponent (component) {
    popupRoot?.removeComponent(component);
  }

  return {addComponent,removeComponent}
}
export function popupFactory (Component) {
  return function usePopup (events,opts) {
    let prevIsShow = false
    let propsFn = null
    if(!events){
      events = {}
    }
    const option = {visible: true}
    if (ObjectUtils.isFunction(opts)) {
      propsFn = opts
      opts = {}
    }
    const props = reactive({...opts,option})
    const attrs = reactive({})

    let builderVm = null
    let userCloseEvent = () => {}
    if(events.close) {
      userCloseEvent = events.close
    }

    const {removeComponent, addComponent} = initPopupContainer(events)
    const handleVisibilityChange = async (newVal) => {
      if(newVal === prevIsShow){
        return
      }
      prevIsShow = newVal
      let instance = Component
      if (ObjectUtils.isFunction(Component)){
        instance = (await Component()).default
      }
      if(newVal){
        addComponent(instance, props, events, attrs)
      }else{
        removeComponent(instance)
        builderVm = null
      }
      props.visible = newVal
    }
    events.close = events.closed = function (payload){
      handleVisibilityChange(false)
      userCloseEvent(payload)
    }
    const showPopup = (opts) => {
      if(opts && !(opts instanceof Event)) {
        ObjectUtils.mergeObject(props, opts)
      }
      if (propsFn) {
        ObjectUtils.mergeObject(props, propsFn())
      }
      handleVisibilityChange(true)
    }
    const hidePopup = () => {
      handleVisibilityChange(false)
      removeComponent(Component)
      builderVm = null
      props.visible = false
    }
    return [showPopup, hidePopup]

  }
}

export function hasComponents () {
  let hasChild = false
   popupRoots.forEach(popupRoot => {
     if(popupRoot && !!popupRoot.$el.children?.length){
      hasChild = true
     }
   })
  return hasChild
}


export function getCurrentInstance () {
  return  _getCurrentInstance() || rootVm
}

export function useStore () {
  return globalOpts.store
}

export function useRouter () {
  return globalOpts.router
}

export function useRoute () {
  const route = reactive({params: {},query:{},...globalOpts.router.app.$route})
  watch(() => globalOpts.router.app.$route,($route) =>{
    Object.keys($route).forEach(key =>{
      route[key] = $route[key]
    })
  })
  return route
}
