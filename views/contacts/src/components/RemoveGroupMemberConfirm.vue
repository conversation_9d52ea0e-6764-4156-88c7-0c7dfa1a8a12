<template>
  <PlainModal
    :visible="true"
    :title="title">
    <template
      slot="content">
      <span v-if="isMe">{{ $t('Remove_Yourself_as_manager_info')}}</span>
      <ul
        v-else
        v-a11y-list-keyboard="{listLength: 1}">
        <li
          role="listitem">
          <MxUserAvatar
            size="36"
            :lazyload="true"
            :alt="member.name"
            :user-avatar="member.avatar" />
          <div class="name-title mx-padding-left-sm">
            <div
              class="mx-text-c2 mx-ellipsis"
              :title="member.name">
              {{ member.name }}
            </div>
            <div class="mx-text-c4 mx-color-secondary">
              <span>{{ memberTitle }}</span>
            </div>
          </div>
        </li>
      </ul>
    </template>
    <template
      slot="footer">
      <el-button
        type="gray"
        size="small"
        @click="$emit('close')">
        {{ $t('cancel') }}
      </el-button>
      <el-button
        type="danger-primary"
        :loading="isRemoving"
        size="small"
        @click="remove">
        {{ $t('remove') }}
      </el-button>
    </template>
  </PlainModal>
</template>

<script>
import PlainModal from '@views/common/components/modals/PlainModal'

export default {
  name: 'RemoveGroupMemberConfirm',
  components: {
    PlainModal
  },
  props: {
    member: {
      type: Object,
      default: () => ({})
    },
    teamObject: {
      type: Object,
      default: () => ({})
    },
    memberTitle: {
      type: String,
      default: ''
    }
  },
  data () {
    return {
      isRemoving: false
    }
  },
  computed: {
    isMe() {
      return this.member.isMe
    },
    title () {
      if(this.member.isManager) {
        if(!this.isMe) {
          return this.$t('Are_you_sure_you_want_to_remove_this_user_from_the_manager_role_in_team', {teamName: this.teamObject.name})
        } else {
          return this.$t('Remove_yourself_as_manager')
        }
      } else {
        return this.$t('Are_you_sure_you_want_to_remove_this_client_from_team', {teamName: this.teamObject.name})
      }
    }
  },
  methods: {
    remove () {
      this.$emit('onRemove')
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-dialog {
  .pm-title {
    white-space: normal;
  }
  li {
    display: flex;
    padding: 8px 12px;
    border-radius: 6px;
    background-color: $mx-color-var-fill-quaternary;
    .name-title {
      overflow: hidden;
    }
  }
  .el-dialog__body {
    .pm-content {
      padding: 18px 0 16px;
    }
    .pm-footer {
      display: flex;
      justify-content: right;
      button + button {
        margin-left: 8px;
      }
    }
  }
}
</style>