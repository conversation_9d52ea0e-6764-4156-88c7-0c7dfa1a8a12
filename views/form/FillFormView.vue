<script lang="ts">
import {defineComponent} from '@vue/composition-api'
import FullScreenDialog from '@views/common/components/FullScreenDialog'
import FormOnlineAssignee from './components/FormOnlineAssignee.vue'
import FormSaveStatus from './components/FormSaveStatus.vue'
import {mapActions as mapNewActions, mapState as mapNewState} from 'pinia'
import {useFillFormStore} from '@views/stores/fillFormStore.ts'
import {FormElementPageModel} from '@model/form/defines/FormPage.ts'
import {FormAnyElementViewModel} from '@model/form/defines/allType'
import FillFormActionPanel from './components/FillFormActionPanel.vue'
import FormPageEmptyView from './components/FormPageEmptyView.vue'

import {FormElementCommonProps, FormElementEventParams, FormEventCallback} from './common/types'
import {computedHighlightProps} from './common/highlightProps'
import {
  FormAllDisplayElements,
  FormElementType,
  FormRuntimeOptions,
  FormScenes,
  FormValidateResult
} from '@model/form/defines/shared'
import FormFactoryManager from '@model/form/factory'
import FillFormMobileLayout from './FillFormMobileLayout.vue'
import FillFormActionPanelForMobile from './components/FillFormActionPanelForMobile.vue'
import {BrowserUtils, ObjectUtils} from '@commonUtils'
import FormCanvasView from '@views/form/components/FormCanvasView.vue'
import FormTheme from '@views/form/components/FormTheme.vue'
import type {PropType} from 'vue';
import PDFFormCanvasView from '@views/form/pdfForm/PDFFormCanvasView.vue';


export default defineComponent({
  name: 'FillFormView',
  components: {
    FormTheme,
    FormCanvasView,
    PDFFormCanvasView,
    FillFormActionPanel,
    FormSaveStatus,
    FormOnlineAssignee,
    FullScreenDialog,
    FormPageEmptyView,
    FillFormMobileLayout,
    FillFormActionPanelForMobile
  },
  props: {
    transactionSequence: {
      type: Number,
      default: 0
    },
    binderId: {
      type: String,
      default: ''
    },
    isMobile: {
      type: Boolean,
      default: false
    },
    runtimeOptions: {
      type: Object as PropType<FormRuntimeOptions>,
      default: () => ({})
    },
  },
  data () {
    return {
      inited: false,
      currentPageIndex: -1,
      submitting: false,
      pageBackgroundRendered: false,
      // isMobile: BrowserUtils.isMobile,
      /**
       * just for save user focused element, we need remove the background for this item
       * This value is not as precise as when there is an HTML form with focus.
       * It will only update when the user refocuses on a specific element.
       */
      currentFocusId: ''
    }
  },
  computed: {
    currRuntimeOptions () {
      return {
        ...this.runtimeOptions,
        binderId: this.binderId,
        transactionSequence: this.transactionSequence,
        showProtected: true,
        scenes: FormScenes.FillForm
      }
    },
    elementViewName () {
      let name = 'FormElementView'
      if (this.formViewModel.type === FormElementType.PDFForm) {
        name = 'PDFFormElementView'
      }
      return name
    },
    effectivePages () {
      return (this.formViewModel.pages || []).filter((page) => page.isVisible)
    },
    totalPage () {
      return this.effectivePages.length
    },
    currentPageLabel () {
      return this.currentPageIndex + 1
    },
    currentPage (): FormElementPageModel {
      if (!this.inited) return null

      const total = this.effectivePages.length
      if (this.currentPageIndex >= 0 && this.currentPageIndex <= total) {
        return this.effectivePages[this.currentPageIndex]
      }
      return null
    },
    mainComponent () {
      const isMobileApp = location.pathname.indexOf('/form/') > 0
      if (isMobileApp || this.isMobile) {
        return 'FillFormMobileLayout'
      }
      return 'FullScreenDialog'
    },
    actionPanelComponent () {
      return (BrowserUtils.isMobile || BrowserUtils.isTablet) ? 'FillFormActionPanelForMobile' : 'FillFormActionPanel'
    },
    ...mapNewState(useFillFormStore, [
      'filledValues',
      'onlineAssignees',
      'currentAssignee',
      'focusedField',
      'submittedAssignee',
      'isMultipleAssignee',
      'formViewModel',
      'currentUserId',
      'uploadingElements'
    ])
  },
  created () {
    this.initFillFormStore(this.binderId, this.transactionSequence, (err) => {
      this.handleError(err)
    }).then(() => {
      this.currentPageIndex = 0
      this.setUserOnlineStatus(true)
      this.$nextTick(() => {
        this.inited = true
      })
    })
  },
  beforeDestroy () {
    this.destroyFormStore()
  },
  methods: {
    ...mapNewActions(useFillFormStore, [
      'initFillFormStore',
      'destroyFormStore',
      'setUserOnlineStatus',
      'submitForm',
      'validateForm',
      'canSyncFromServer',
      'updateFillStatus',
      'markChange',
      'saveFilledValue',
      'uploadFileToElement',
      'removeFileFromElement',
      'clearSignatureElement',
      'uploadResourceToElement'
    ]),

    getFormElementProps (element: FormAnyElementViewModel): FormElementCommonProps {
      const props: FormElementCommonProps = {}
      if (!FormAllDisplayElements.includes(element.type)) {
        props.highlight = computedHighlightProps(
            element,
            this.onlineAssignees,
            this.focusedField,
            this.currentUserId
        )
      }
      if (this.currentFocusId === element.id) {
        props.isFocused = true
      }
      return props
    },
    closeFillForm () {
      this.confirmExit().then(() => {
        this.$emit('close')
      })
    },
    confirmExit () {
      return new Promise((resolve, reject) => {
        if (this.uploadingElements.size) {
          this.$mxConfirm(this.$t('Files_are_currently_being_uploaded_'), this.$t('exit'), {
            showClose: false,
            confirmButtonText: this.$t('leave'),
            cancelButtonText: this.$t('cancel'),
            customClass: 'new-style'
          })
              .then(() => {
                resolve()
              })
              .catch(reject)
        } else {
          resolve()
        }
      })
    },
    confirmSubmit () {
      return new Promise((resolve, reject) => {
        if (this.isMultipleAssignee) {
          this.$mxConfirm(
              this.$t('Are_you_sure_you_want_to_submit_form_', {actionName: this.$t('PdfForm')}),
              this.$t('submit_form', {actionName: this.$t('PdfForm')}),
              {
                showClose: false,
                // showConfirmButton: false,
                confirmButtonText: this.$t('Confirm'),
                customClass: 'new-style'
              }
          )
              .then(() => {
                resolve()
              })
              .catch(reject)
        } else {
          resolve()
        }
      })
    },
    confirmConditionNextPage (n) {
      this.$mxConfirm(
          this.$t('Some_required_radio_button_checkbox_sections_are_on_the_next_page'),
          this.$t('Friendly_Reminder'),
          {
            showClose: false,
            // showConfirmButton: false,
            confirmButtonText: this.$t('continue'),
            customClass: 'new-style'
          }
      )
          .then(() => {
            this.currentPageIndex = n
            this.pageBackgroundRendered = false
            this.scrollToTop()
          })
          .catch(() => {
            this.scrollToErrorField()
          })
    },
    _gotoPage (n) {
      this.currentPageIndex = n
      this.pageBackgroundRendered = false
      this.scrollToTop()
    },
    handleGotoPage (n) {
      if (n < this.currentPageIndex) {
        // back to prev page do not need validate
        this._gotoPage(n)
        return
      }
      if (this.currentPage) {
        this.validateForm(this.currentPage)
            .then(() => {
              this._gotoPage(n)
            })
            .catch((validateResult: FormValidateResult) => {
              if (validateResult.nextPageHasRelatedElement) {
                this.confirmConditionNextPage(n)
                return
              }
              this.$mxMessage.error(
                  this.$t('please_ensure_all_errors_are_resolved_before_continuing')
              )
              this.scrollToErrorField()
            })
      }
    },
    handleElementFocus (eventInfo: FormElementEventParams) {
      this.currentFocusId = eventInfo.element.id
      if (!eventInfo.silentMode) {
        this.updateFillStatus(eventInfo)
      }
    },
    handleElementBlur (eventInfo: FormElementEventParams, resultCallback: FormEventCallback) {
      const type = eventInfo.element.type
      this.currentFocusId = ''
      const wrapCallback = (promise: Promise<unknown>) => {
        if (ObjectUtils.isFunction(resultCallback)) {
          resultCallback(promise)
          return true
        }
      }
      if (type === FormElementType.Signature) {
        if (eventInfo.removeFile) {
          return wrapCallback(this.clearSignatureElement(eventInfo))
        } else if (eventInfo.uploadFile) {
          return wrapCallback(this.uploadResourceToElement(eventInfo))
        }
      }
      if (type === FormElementType.FileUpload) {
        if (eventInfo.removeFile) {
          // do remove form file action
          return wrapCallback(this.removeFileFromElement(eventInfo))
        } else if (eventInfo.uploadFile) {
          // to upload form file action
          return wrapCallback(this.uploadFileToElement(eventInfo))
        }
      }
      return wrapCallback(this.saveFilledValue(eventInfo))
    },
    handleElementChange (eventInfo: FormElementEventParams) {

      this.markChange(eventInfo)
    },
    handleSubmitForm () {
      if (this.formViewModel.isCompleted) {
        return this.$mxMessage.error(this.$t('already_submit_form'))
      }
      this.validateForm()
          .then(() => {
            this.loading = true
            this.confirmSubmit().then(async () => {
              this.$emit('beforeSubmit')
              this.submitForm()
                  .then(() => {
                    this.loading = false
                    this.closeFillForm()
                  })
                  .catch((err) => {
                    //handle error
                    this.loading = false
                  })
            })
          })
          .catch((e) => {
            this.jumpToTheFirstErrorPage()
            this.$mxMessage.error(this.$t('please_ensure_all_errors_are_resolved_before_continuing'))
            this.scrollToErrorField()
          })
    },
    jumpToTheFirstErrorPage () {
      const errorElement = (this.currentPage?.elements || []).find(
          (element) => element.errors?.length
      )
      let errorPageIndex = -1
      if (!errorElement) {
        this.effectivePages.forEach((page, index) => {
          const error = page.elements.find((element) => element.errors?.length)
          if (error && errorPageIndex == -1) {
            errorPageIndex = index
          }
        })
      }
      if (errorPageIndex != -1) {
        this.currentPageIndex = errorPageIndex
      }
    },

    scrollToErrorField () {
      const dialogBodyEl = this.$el.querySelector('.fill-form-container')
      const erritem = dialogBodyEl.querySelector('.is-field-error')
      erritem?.scrollIntoView({
        block: 'center',
        behavior: 'smooth'
      })
    },
    scrollToTop () {
      this.$nextTick(() => {
        const formPageEl = this.$el.querySelector('.form-top-actor')
        formPageEl?.scrollIntoView({
          block: 'center',
          behavior: 'smooth'
        })
      })
    },
    handlePageRendered () {
      this.pageBackgroundRendered = true
    },
    onInitialized () {
      this.inited = true
    },
    handleError (err) {
      if (err.formIsDeleted) {
        this.$emit('close')
      } else if (err.formIsCompleted) {
        this.showFormCompleted()
      } else if (err.contentUpdated) {
        this.showContentUpdated()
      } else {
        this.$mxMessage.error(this.$t('system_unknown_error'))
      }
    },
    showFormCompleted () {
      const msgTitle = this.$t('Unable_to_fill_out', {actionName: this.$t('PdfForm')})
      const msgContent = this.$t('This_Form_has_already_been_completed', {
        actionName: this.$t('PdfForm'),
        userName: this.submittedAssignee?.name
      })
      this.showWarningDialog(msgTitle, msgContent)
    },
    showContentUpdated () {
      const msgTitle = this.$t('Content_updated', {
        Type: this.$t('PdfForm')
      })
      const msgContent = !this.isWorkflow
          ? this.$t('content_updated_tips')
          : `${this.$t('this_step_has_been_updated_by_owner')} ${this.$t(
              'You_will_be_returned_to_the_flow_conversation'
          )}`
      this.showWarningDialog(msgTitle, msgContent)
    },
    showWarningDialog (msgTitle, msgContent) {
      const contentTipCls = 'content-updated-under-form'

      if (!document.querySelector(`.${contentTipCls}`)) {
        this.$mxConfirm(msgContent, msgTitle, {
          showClose: false,
          showCancelButton: false,
          confirmButtonText: this.$t('dismiss'),
          customClass: `new-style ${contentTipCls}`
        }).then(() => {
          this.$emit('close')
        })
      }
    },
    getElementRenderComponent (element) {
      return FormFactoryManager.uiOption(element, this.runtimeOptions).view
    }
  }
})
</script>
<template>
  <component
    :is="mainComponent"
    class="fill-form-view fill-out-form"
    :class="{ 'is-mobile-view': isMobile }"
    :title="$t('Fill_Out_PDF_Form')"
    @close="closeFillForm">
    <template #append>
      <FormSaveStatus />
    </template>
    <template #action>
      <FormOnlineAssignee v-if="inited" />
    </template>

    <el-form
      v-if="inited"
      v-loading="!inited"
      class="fill-form-container"
      @submit.native.prevent>
      <div class="form-top-actor" />
      <div class="form-page-wrapper">
        <component
          :is="currRuntimeOptions.isPDFForm? 'PDFFormCanvasView': 'FormCanvasView'"
          v-slot="viewInfo"
          :page="currentPageLabel"
          :runtime-options="currRuntimeOptions"
          :form-view-model="formViewModel"
          @initialized="pageBackgroundRendered = true">
          <FormTheme
            v-if="pageBackgroundRendered"
            :scale="viewInfo.scale"
            :runtime-options="currRuntimeOptions">
            <component
              :is="elementViewName"
              v-for="element in currentPage.elements"
              :key="element.id"
              :element="element"
              :runtime-options="currRuntimeOptions"
              :pageWidth="currentPage.width"
              :scale="viewInfo.scale">
              <component
                :is="getElementRenderComponent(element)"
                :key="element.id + '-comp'"
                v-bind="getFormElementProps(element)"
                :element="element"
                :scale="viewInfo.scale"
                :runtime-options="currRuntimeOptions"
                @focus="handleElementFocus"
                @change="handleElementChange"
                @blur="handleElementBlur" />
            </component>
          </FormTheme>
        </component>
      </div>
      <div class="form-actions">
        <component
          :is="actionPanelComponent"
          :runtime-options="currRuntimeOptions"
          v-model="currentPageIndex"
          :total-page="totalPage"
          :loading="submitting"
          @saveForLater="closeFillForm"
          @gotoPage="handleGotoPage"
          @submit="handleSubmitForm" />
      </div>
    </el-form>
  </component>
</template>

<style scoped lang="scss">
.fill-form-view {
  display: flex;
  flex-direction: column;

  &.is-mobile-view {
    display: block;
    padding: 0 !important;

    .form-actions {
      margin: 0 auto !important;
    }

    ::v-deep {
      .mobile-fill-body {
        height: calc(100vh - 53px);
      }
    }
  }
}

.fill-form-container {
  display: flex;
  justify-content: center;
  background: #f4f4f4;
  height: 100%;
  flex-direction: column;

  .form-page-wrapper {
    width: 100%;
    height: 100%;
    overflow: auto;
  }

  .fill-form-action-panel {
    display: flex;
    justify-content: space-between;
  }

  .form-actions {
    width: 100%;
    margin: auto;
    height: 92px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }

  .fill-form-page {
    border-radius: 10px;
    border: 2px solid transparent;
    padding: 0 40px;
    flex-grow: 1;
    overflow-y: auto;
    height: 100%;

    .form-element:first-child {
      margin-top: 30px;
    }
  }
}
</style>
