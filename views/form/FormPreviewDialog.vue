<template>
  <full-screen-dialog
    class="form-preview"
    :title="previewTitle"
    @close="$emit('close')">
    <template
      v-if="formViewModel.isCompleted && canDownloadFile"
      #action>
      <el-button
        v-if="formViewModel.isPDFForm"
        type="secondary download-btn"
        @click="handleDownloadPDF">
        <SvgIcon icon-class="PdfIcon" size="24px"></SvgIcon>
        {{ $t('download_as_pdf') }}
      </el-button>
      <el-dropdown
        v-else
        trigger="click"
        @command="handleDownload">
        <el-button
          :square="true"
          type="gray">
          <i class="micon-more" />
        </el-button>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item command="downloadFormPDF">
            {{ $t('download_as_pdf') }}
          </el-dropdown-item>
          <el-dropdown-item command="downloadFormCSV">
            {{ $t('download_as_csv') }}
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
    </template>
    <div class="preview-wrapper">
      <div
        v-if="isReadOnlyPreview && !formViewModel.isCompleted"
        class="preview-mode">
        <div
          class="container mx-text-c3 mx-branding-border mx-branding-background-lighter important">
          <i class="micon-preview-button mx-branding-text" />
          <span>{{ $t('preview_mode_message') }}</span>
        </div>
      </div>
      <PDFFormPreviewView
        :form-view-model="formViewModel"
        :enable-full-screen="true"
        :runtime-options="computedRuntimeOptions" />
    </div>
  </full-screen-dialog>
</template>

<script lang="ts">
import FullScreenDialog from '@views/common/components/FullScreenDialog'
import type {PropType} from 'vue'
import downloadActionsMixin from '@views/common/mixins/downloadActions'
import {mapGetters} from 'vuex'
import {defineComponent} from '@vue/composition-api'
import {FormViewModel} from '@model/form/defines/formViewModel'
import PDFFormPreviewView from '@views/form/pdfForm/PDFFormPreviewView.vue';
import {FormRuntimeOptions, FormScenes} from '@model/form/defines/shared';
import SvgIcon from "@views/common/components/svgIcon.vue";


export default defineComponent({
  name: 'FormPreviewDialog',
  components: {SvgIcon, PDFFormPreviewView, FullScreenDialog,},
  mixins: [downloadActionsMixin],
  props: {
    formViewModel: {
      type: Object as PropType<FormViewModel>,
      default: () => ({})
    },
    runtimeOptions: {
      type: Object as PropType<FormRuntimeOptions>,
      default: () => ({})
    },
  },
  data () {
    return {}
  },
  methods: {
    handleDownloadPDF () {
      this.downloadFormPDF()
    },
    handleDownload (command) {
      this[command]()
    },
    downloadFormPDF () {
      if (this.showProtectedValue) {
        this.downloadUrl(this.formViewModel.downloadPDFUrl)
      } else {
        this.downloadUrl(this.formViewModel.downloadMaskedPDFUrl)
      }
    },
    downloadFormCSV () {
      if (this.showProtectedValue) {
        this.downloadUrl(this.formViewModel.downloadCSVUrl)
      } else {
        this.downloadUrl(this.formViewModel.downloadMaskedCSVUrl)
      }
    }
  },
  computed: {
    ...mapGetters('user', ['currentUser', 'userTimezone']),
    ...mapGetters('privileges', ['canDownloadFile']),
    previewTitle () {
      return this.formViewModel.isCompleted ? this.$t('pdf_form_responses') : this.$t('PDF_Form_Preview')
    },
    computedRuntimeOptions () {
      return {
        ...this.runtimeOptions,
        isPDFForm: this.formViewModel.isPDFForm,
      }
    },
    isReadOnlyPreview () {
      return this.formPreviewContext === 'READ_ONLY_PREVIEW' || this.formViewModel.isCompleted
    },
    isPreviewTemplate () {
      return this.formPreviewContext === 'TEMPLATE_PREVIEW'
    },
    showProtectedValue () {
      if (this.formViewModel.isPDFForm) {
        return true
      }
      const ids = [this.formViewModel.creator, ...this.formViewModel?.ownerIds, ...this.formViewModel.assigneeIds]
      if (ids.includes(this.currentUser.id)) {
        return true
      } else {
        return false
      }
    },
  }
})
</script>

<style lang="scss" scoped>
.form-preview {
  .download-btn{
    color: #616161 !important;
    text-transform: none!important;
  }
  ::v-deep {
    .full-screen-body {
      height: calc(100% - 52px);
      background: #e5e5e5;
    }

    // icon style on AuditThreadView have an effect on it
    .header .title .micon-close-d3 {
      border-radius: 0;
      background: none;
      color: $mx-color-var-label-secondary;
    }
  }

  .preview-mode {
    padding: 0 30px;

    .container {
      border-radius: 6px;
      padding: 8px 16px 8px 8px;
      gap: 12px;
      margin: 24px 0 0 0;
      align-items: center;
      display: flex;
      border: 1px solid transparent;
    }

    .micon-preview-button {
      font-size: 16px;
    }
  }

  .preview-wrapper {
    width: 100%;
    height: 100%;
  }
}

.bw-mobile {
  .form-preview .preview-wrapper {
    width: 100%;
  }
}
</style>
