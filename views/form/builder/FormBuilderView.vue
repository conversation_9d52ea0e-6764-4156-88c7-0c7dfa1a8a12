<script lang="ts">

import { defineComponent } from '@vue/composition-api'
import type { PropType } from 'vue'
import { FormRuntimeOptions } from '@model/form/defines/shared'
import PDFFormPreviewView from '@views/form/pdfForm/PDFFormPreviewView.vue'

export default defineComponent({
  name: 'FillFormView',
  components: { PDFFormPreviewView },
  props: {
    runtimeOptions: {
      type: Object as PropType<FormRuntimeOptions>,
      default: () => ({})
    },
  }
})
</script>

<template>
  <div class="form-builder">
    <div class="form-builder-left"></div>
    <div class="form-builder-main">
      <PDFFormPreviewView v-model="formViewModel" :selectable="true"></PDFFormPreviewView>
    </div>
    <div class="form-builder-right"></div>
  </div>
</template>

<style scoped lang="scss">
.form-builder {
  display: flex;
  .form-builder-left {
    width: 300px;
  }
  .form-builder-main {
    flex-grow: 1;
    width: 100%;
  }
  .form-builder-right {
    width: 400px;
  }
}
</style>