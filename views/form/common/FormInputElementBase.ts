import type {PropType} from 'vue'
import {defineComponent} from '@vue/composition-api'
import {FormRuntimeOptions, FormScenes} from '@model/form/defines/shared'
import {ElementHighlightProps} from '../common/types'
import {FormElementAnyInputModel} from '@model/form/defines/allType'
import {getElementErrors} from './errorProps'
import MxDDRInput from '@views/ddr/MxDDRInput.vue';

export default defineComponent({
    props: {
        element: {
            type: Object as PropType<FormElementAnyInputModel>,
            required: true,
            default: null
        },
        runtimeOptions: {
            type: Object as PropType<FormRuntimeOptions>,
            default: () => ({})
        },
        highlight: {
            type: Object as PropType<ElementHighlightProps>,
            default: () => ({})
        },
        selectable: {
            type: Boolean,
            default: false
        },
        readonly: {
            type: Boolean,
            default: false
        },
        scale: {
            type: Number,
            default: 1
        },
        /**
         * if current user focused this element , we need remove bg color
         */
        isFocused: {
            type: Boolean,
            default: false
        }
    },
    data () {
        return {
            errorMessage: '',
            errors: {}
        }
    },
    computed: {
        showDDRInput(){
            if(this.runtimeOptions.enableDDR){
                return !this.isPreviewCompleted
            }
            return false
        },
        isNeedShowMask () {
            if(this.isPreview){

            }
            return this.element.isProtected && !this.runtimeOptions.showProtected
        },
        isPreviewCompleted () {
            return this.runtimeOptions.isCompleted
        },
        isPreviewTemplate () {
            return this.runtimeOptions.scenes === FormScenes.Preview && this.runtimeOptions.isTemplate
        },
        isFillMode () {
            return this.runtimeOptions.scenes === FormScenes.FillForm
        },
        isEditMode () {
            return this.runtimeOptions.scenes === FormScenes.EditForm
        },

        isPreview () {
            return this.runtimeOptions.scenes === FormScenes.Preview
        },
        placeholder () {
            if (this.isPreviewCompleted) {
                return ''
            }
            return this.element.placeholder
        },
        isReadonly () {
            if (this.element.readonly) {
                return true
            }
            return [FormScenes.Preview].includes(this.runtimeOptions.scenes)
        },
        isDisabled () {
            return this.isReadonly
        },
        /**
         * only for pdf form
         */
        tooltip () {
            let tooltip = ''
            if (this.element.required) {
                tooltip = this.$t('required') as string
            }
            if (this.element.label) {
                if (tooltip) {
                    tooltip += ' - '
                }
                tooltip += `${this.element.label}`
            }
            return tooltip
        },
        /**
         * for ddr
         */
        inputComponent(){
            return this.runtimeOptions.enableDDR ? MxDDRInput : 'ElInput'
        },
        inputOptions(){
            if(this.runtimeOptions.enableDDR) {
                return {
                    jsonEscape: true,
                    hideDDRSelector: this.isPreviewTemplate
                }
            }else{
                return ''
            }
        },
        isShowAutoFillLabel(){
            if(this.runtimeOptions.isMultipleAssignees){
                return false
            }
            if(!this.isPreviewTemplate){
                return false
            }
            return this.element.enableAutoFill
        }
    },
    watch: {
        'element.errors': {
            handler: function (val) {
                this.calcErrorMessage()
            },
            immediate: true,
            // deep: true
        }
    },
    methods: {
        getInputOptions(attrs){
            const props = {
                trim: true,
                readonly: this.isReadonly,
                disabled :this.isDisabled,
                options: {}
            }
            const isDDRInput = this.runtimeOptions.enableDDR
            if(isDDRInput) {
                props.options = {
                    jsonEscape: true,
                    hideDDRSelector: this.isPreviewTemplate
                }
            }
            Object.keys(attrs).forEach(key => {
                let realKey = key
                if(key === 'maxLength' && !isDDRInput){
                    realKey = 'maxlength'
                }
                if(key === 'options' && isDDRInput){
                    props.options = {...props.options, ...attrs.options}
                }else {
                    props[realKey] = attrs[key]
                }
            })
            return props;
        },
        calcErrorMessage(){
            const error = getElementErrors(this.element, this.$t)
            this.errorMessage = error.errorMessage
            this.errors = error.errors
        },
        needUseDefaultValue () {
            const scenes = this.runtimeOptions.scenes
            if(this.runtimeOptions.isTemplate && scenes === FormScenes.Preview){
                return true
            }
            return [FormScenes.EditForm, FormScenes.CreateForm].includes(scenes)
        }
    }
})