import {FormElementAnyInputModel} from '@model/form/defines/allType'
import {FormElementError, FormErrorType} from '@model/form/defines/base'
import {FormElementType} from "@model/form/defines/shared";

export function getErrorMessage ($t: Function, error: FormElementError, type: FormErrorType) {
  switch (error.errorType) {
    case FormErrorType.Required:
      return $t('Required_field')
      break
    case FormErrorType.AnswerLimit:
      return $t('answer_character_limit_error')
      break
    case FormErrorType.InvalidTime:
      return $t('invalid_time')
      break
    case FormErrorType.Precision:
      return $t('enter_only_up_to_n_decimals', error.params || {})
      break
    case FormErrorType.Limit:
      return $t('value_not_fit_the_limit')
      break
    case FormErrorType.PhoneNumber:
      return $t('invalid_phone_number_error')
      break
    case FormErrorType.MaxLimit:
      if(type == FormElementType.FileUpload){
        return $t('The_selected_file_exceeds_max_limit_count', error.params || {})
      }
      if([FormElementType.Address,FormElementType.UserName,FormElementType.EmailAddress].includes(type)){
        return $t('maximum_characters_error',{
          number: error.params?.maxLength
        })
      }
      if(error.params.maxLength === 1){
        return $t('the_answer_must_be_up_to_one')
      }
      return $t('the_answer_must_be_up_to', error.params || {})
      break
    case FormErrorType.MinLimit:
      if(error.params.minLength === 1){
        return $t('the_answer_must_be_at_least_one')
      }
      return $t('the_answer_must_be_at_least', error.params || {})
      break
  }
}

export function getElementErrors (element: FormElementAnyInputModel, $t: Function) {
  const errors = element.errors
  const result = {}
  let errorMessage = ''
  errors.forEach((error) => {
    result[error.field] = getErrorMessage($t, error,element.type)
    if (!errorMessage) {
      //first error
      errorMessage = getErrorMessage($t, error, element.type)
    }
  })
  return {
    errors: result,
    errorMessage
  }
}
