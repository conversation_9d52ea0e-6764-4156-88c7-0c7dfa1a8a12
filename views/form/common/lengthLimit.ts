import {FormElementType} from "@model/form/defines/shared";

export function getLengthLimitMessage(type: FormElementType, minLength: string, maxLength: string,$t: Function) {
    const fMinLength = parseFloat(minLength)
    const fMaxLength = parseFloat(maxLength)
    const isTextInput = [FormElementType.SingleLineText, FormElementType.MultiLineText].includes(type)
    let str
    const params= {
        minLength,
        maxLength
    }

    if (minLength && maxLength) {
        str = $t('enter_value_between',params)
        if (isTextInput) {
            str = $t('the_answer_must_be_between',params)
        }
    } else if (minLength) {
        str = $t('enter_value_no_less_than',params)
        if (isTextInput) {
            str = fMinLength === 1 ? $t('the_answer_must_be_at_least_one',params) : $t('the_answer_must_be_at_least',params)
        }
    } else if (maxLength) {
        str = $t('enter_value_up_to',params)
        if (isTextInput) {
            str = fMaxLength === 1 ? $t('the_answer_must_be_up_to_one',params) : $t('the_answer_must_be_up_to',params)
        }

    }
    return str
}
