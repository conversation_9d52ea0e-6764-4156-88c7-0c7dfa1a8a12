import { FormElementId } from '@model/form/defines/shared'
import { FormAnyElementValue, FormElementAnyInputModel } from '@model/form/defines/allType'
import { PickedFile } from '../../common/components/FilePicker'
import {FileUploadValue} from "@model/form/defines/FormFileUpload";

type ElementFieldId = string

/**
 * highlight props is for mark online user fill status feature
 */
interface FieldHighlightProps {
  /**
   * The highlight border and text color that needs to be displayed.
   */
  highlightColor: string;
  /**
   * this option will control the border for the input  in form element.
   * set true will show the border with the highlightColor.
   * when current user focus the input need to set false.
   */
  highlightBorder: boolean;
  /**
   * The label text to be displayed in the top-right corner of the input.
   */
  highlightLabels: string[];
}

type ElementHighlightProps = Record<ElementFieldId, FieldHighlightProps>

type FormElementCommonProps = {
  /**
   * only input element has this attribute for mark online user fill status feature
   */
  highlight?: ElementHighlightProps;
}

interface FormElementEventParams {
  element: FormElementAnyInputModel;
  currentValue: FormAnyElementValue;
  /**
   * user changed properties
   */
  changedProperty: string;
  /**
   * After the user modifies a property in the UI, multiple property values need to be updated simultaneously.
   * The affected properties are defined by affectedProperties.
   */
  affectedProperties?: string[];
  enforce?: boolean;
  /**
   * set true this event will not send to store layer. it's only for fill form ui layer.
   */
  silentMode?: boolean;
  withFillStatus?: boolean;
  /**
   * only for file upload,Indicates that the current event is a file deletion.
   */
  removeFile?: FileUploadValue;
  /**
   * only for file upload,Indicates that the current event is a file upload.
   */
  uploadFile?: PickedFile;
}

type FormEventCallback = (promise: Promise<unknown>) => void;


export type {
  FormElementCommonProps, ElementHighlightProps, FieldHighlightProps, FormElementEventParams, FormEventCallback
}