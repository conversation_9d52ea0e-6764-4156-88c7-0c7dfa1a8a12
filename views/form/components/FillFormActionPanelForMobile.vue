<script lang="ts">
import { defineComponent } from '@vue/composition-api'
import { BrowserUtils } from '@commonUtils'
import type {PropType} from "vue";
import {FormRuntimeOptions} from "@model/form/defines/shared";

export default defineComponent({
  name: 'FillFormActionPanelForMobile',
  props: {
    runtimeOptions: {
      type: Object as PropType<FormRuntimeOptions>,
      default: () => ({})
    },
    value: {
      type: Number,
      default: 0
    },
    totalPage: {
      type: Number,
      default: 0
    },
    hideActionBtn: {
      type: Boolean,
      default: false
    },
    readonly: {
      type: Boolean,
      default: false
    },
    loading: {
      type: Boolean,
      default: false
    },
    showWhiteBackBtn: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      currentPageIndex: this.value
    }
  },
  computed: {
    showAction () {
      return !this.hideActionBtn
    },
    currentPageLabel () {
      return this.currentPageIndex + 1
    },
    btnSize () {
      return BrowserUtils.isMobile ? 'large' : ''
    },
    isSinglePage () {
      return this.totalPage === 1
    },
    isLastPage () {
      return this.currentPageIndex === this.totalPage - 1
    },
    isFirstPage () {
      return this.currentPageIndex === 0
    },
    pageMenus () {
      const menus = []
      for (let i = 0; i < this.totalPage; i++) {
        menus.push({
          label: this.$t('page_nth_of_n', { total: this.totalPage, number: i + 1 }),
          value: i
        })
      }
      return menus
    },
    saveLaterBtnType () {
      if (this.isSinglePage) {
        return 'white'
      } else {
        return 'text'
      }
    },
    backBtnType () {
      if (this.showWhiteBackBtn) {
        return 'white'
      }
      return 'raised'
    }
  },
  watch: {
    value (val) {
      this.currentPageIndex = val
    }
  },
  methods: {
    handleSubmitForm () {
      if (this.isLastPage) {
        this.$emit('submit')
      } else {
        this.goToPage(this.currentPageIndex + 1)
      }
    },
    goToPage (n) {
      this.$emit('gotoPage', n)
    },
    goToPrevPage () {
      this.goToPage(this.currentPageIndex - 1)
    },
    saveForLater () {
      this.$emit('saveForLater')
    },
    handlePageChange (page) {
      this.goToPage(page.value)
    }
  }
})
</script>

<template>
  <div
    :class="{
      'fill-form-action-panel-mobile': true,
      'single-page': isSinglePage
    }">
    <div class="form-paging-wrap"  v-if="!isSinglePage">
      <el-dropdown
        trigger="click"
        class="form-paging"
        @command="handlePageChange">
        <span class="el-dropdown-link">
          {{ $t('page_nth_of_n', { total: totalPage, number: currentPageLabel }) }}
          <i class="el-icon-arrow-down el-icon--right" />
        </span>
        <el-dropdown-menu
          slot="dropdown"
          style="max-height: 200px">
          <el-dropdown-item
            v-for="item in pageMenus"
            :key="item.value"
            :command="item">
            {{ item.label }}
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
    </div>
    <div class="form-action-btns">
      <div class="panel-left">
        <el-button
          v-if="showAction"
          :type="saveLaterBtnType"
          :size="btnSize"
          class="el-button no-text-transform"
          :class="{ 'mx-branding-text-action': isSinglePage }"
          @click="saveForLater">
          {{ $t('Save_for_Later') }}
        </el-button>
      </div>
      <div class="panel-right"  v-if="showAction">
          <el-button
            v-if="!isFirstPage"
            :size="btnSize"
            :type="backBtnType"
            @click="goToPrevPage">
            {{ $t('back') }}
          </el-button>
          <el-button
            v-mx-ta="{ page: 'fillForm', id: 'submitBtn' }"
            type="primary"
            :loading="loading"
            :size="btnSize"
            :disabled="readonly"
            @click="handleSubmitForm">
            {{ isLastPage ? $t('submit') : $t('next') }}
          </el-button>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.fill-form-action-panel-mobile {
  display: flex;
  flex-direction: column;
  width: 100%;
  padding: 0 16px;

  &.single-page {
    .form-paging-wrap {
      display: none;
    }
    .form-action-btns{
      grid-gap: 10px;
      .panel-right,
      .panel-left {
        width: 50%;
      }
    }
  }

  .form-paging-wrap {
    height: 44px;
    display: flex;
    align-content: center;
    align-items: center;
    justify-content: center;
  }

  .form-action-btns {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
  }

  .el-dropdown-link {
    white-space: nowrap;
    cursor: pointer;
  }
}

.right-group {
  display: flex;
}

.panel-right,
.panel-left {
  max-width: 320px;
  display: flex;
  align-items: center;
  grid-gap: 10px;
  flex-grow: 1;

  ::v-deep {
    > div {
      width: 100%;
    }

    .el-button {
      width: 100%;
      display: block;
    }
  }
}
</style>
