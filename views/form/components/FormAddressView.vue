<script lang="ts">
import type {PropType} from 'vue'
import {defineComponent} from '@vue/composition-api'
import FormUserFocusStatusMarker from './FormUserFocusStatusMarker.vue'
import {nameMaxLength} from '@views/common/appConst.js'
import {FormElementEventParams} from '../common/types'
import {AddressValue, FormElementAddressModel} from '@model/form/defines/FormAddress'
import FormInputElementBase from '@views/form/common/FormInputElementBase'
import CountrySelect from "@views/form/common/CountrySelect.vue";
import FormElementLabel from "@views/form/components/FormElementLabel.vue";
import MxDDRInput from "@views/ddr/MxDDRInput.vue";
import {getCountryList} from "@views/form/common/country";

export default defineComponent({
  name: 'FormAddressView',
  components: {MxDDRInput, FormElementLabel, CountrySelect, FormUserFocusStatusMarker},
  extends: FormInputElementBase,
  props: {
    element: {
      type: Object as PropType<FormElementAddressModel>,
      required: true,
      default: null
    }

  },
  data() {
    return {
      modelValue: {...this.element.value} as AddressValue,
      nameMaxLength
    }
  },
  computed: {},
  watch: {
    'element.value': function (val) {
      Object.keys(val).forEach(key => {
        this.modelValue[key] = val[key]
      })
    }
  },
  methods: {
    handleUserChange(event, changedProperty: string) {
      this.$emit('change', this.getEventInfo(event, changedProperty))
    },
    handleUserBlur(event, changedProperty, enforce) {
      let affectedProperties = []
      if (changedProperty === 'countryCode') {
        this.modelValue.countryDisplayName = this.getDisplayName()
        affectedProperties = ['countryDisplayName']
      }
      this.$emit('blur', this.getEventInfo(event, changedProperty, affectedProperties, enforce))

    },
    handleUserFocus(event, changedProperty: string) {
      this.$emit('focus', this.getEventInfo(event, changedProperty, []))
    },
    getEventInfo(event, changedProperty: string, affectedProperties: string[], enforce = false) {
      return {
        element: this.element,
        currentValue: this.modelValue,
        enforce,
        changedProperty,
        affectedProperties
      } as FormElementEventParams
    },
    getDisplayName() {
      const countries = getCountryList({supportOther: true,extraForm: true})
      const country = countries.find(item => item.code === this.modelValue.countryCode)
      return country.name as string
    }
  }
})
</script>

<template>
  <div class="form-address-wrapper">
    <FormElementLabel :element="element"></FormElementLabel>
    <div class="form-address-view flex-column">
      <div class="margin-bottom">
        <div class="sub-label">
          {{ element.lineOneLabel || $t('address_line_one') }}
        </div>
        <el-form-item :error="errors.addressLineOne">
          <FormUserFocusStatusMarker :highlight="highlight.addressLineOne">
            <component :is="inputComponent"
                       v-model="modelValue.addressLineOne"
                       v-bind="getInputOptions({maxLength: 300})"
                       @input="handleUserChange($event, 'addressLineOne')"
                       @focus="handleUserFocus($event, 'addressLineOne')"
                       @blur="handleUserBlur($event, 'addressLineOne')"/>
          </FormUserFocusStatusMarker>
        </el-form-item>
      </div>

      <div
          v-if="element.showAddressLineTwo"
          class="margin-bottom">
        <div class="sub-label">
          {{ element.lineTwoLabel || $t('address_line_two') }}
        </div>
        <FormUserFocusStatusMarker :highlight="highlight.addressLineTwo">
          <component :is="inputComponent"
                     v-bind="getInputOptions({maxLength: 300})"
                     v-model="modelValue.addressLineTwo"
                     @input="handleUserChange($event, 'addressLineTwo')"
                     @focus="handleUserFocus($event, 'addressLineTwo')"
                     @blur="handleUserBlur($event, 'addressLineTwo')"/>
        </FormUserFocusStatusMarker>
      </div>
      <div class="flex-row margin-bottom">
        <div v-if="element.showCity">
          <div class="sub-label">
            {{ element.cityLabel || $t('city') }}
          </div>
          <el-form-item :error="errors.city">
            <FormUserFocusStatusMarker :highlight="highlight.city">
              <component :is="inputComponent"
                         v-model="modelValue.city"
                         v-bind="getInputOptions({maxLength: 200})"
                         @input="handleUserChange($event, 'city')"
                         @focus="handleUserFocus($event, 'city')"
                         @blur="handleUserBlur($event, 'city')"/>
            </FormUserFocusStatusMarker>
          </el-form-item>
        </div>
        <div v-if="element.showState">
          <div class="sub-label">
            {{ element.stateLabel || $t('state_province') }}
          </div>
          <el-form-item :error="errors.state">
            <FormUserFocusStatusMarker :highlight="highlight.state">
              <component :is="inputComponent"
                         v-model="modelValue.state"
                         v-bind="getInputOptions({maxLength: 200})"
                         @input="handleUserChange($event, 'state')"
                         @focus="handleUserFocus($event, 'state')"
                         @blur="handleUserBlur($event, 'state')"/>
            </FormUserFocusStatusMarker>
          </el-form-item>
        </div>
      </div>
      <div class="flex-row margin-bottom">
        <div
            v-if="element.showZipcode"
            :class="{ 'two-column': element.showZipcode && element.showCountry }">
          <div class="sub-label">
            {{ element.zipCodeLabel || $t('zip_code') }}
          </div>
          <el-form-item :error="errors.zipcode">
            <FormUserFocusStatusMarker :highlight="highlight.zipcode">
              <component :is="inputComponent"
                         v-bind="getInputOptions({maxLength: 10})"
                         v-model="modelValue.zipcode"
                         @input="handleUserChange($event, 'zipcode')"
                         @focus="handleUserFocus($event, 'zipcode')"
                         @blur="handleUserBlur($event, 'zipcode')"/>
            </FormUserFocusStatusMarker>
          </el-form-item>
        </div>
        <div
            v-if="element.showCountry"
            :class="{ 'two-column': element.showZipcode && element.showCountry }">
          <div class="sub-label">
            {{ element.countryLabel || $t('country_region') }}
          </div>
          <el-form-item :error="errors.countryCode">
            <FormUserFocusStatusMarker :highlight="highlight.countryCode">
              <CountrySelect
                  v-model="modelValue.countryCode"
                  :clearable="true"
                  :class="{
                  'disabled-with-normal-style': isPreview && !element.lockCountry
                }"
                  :disabled="element.lockCountry || isReadonly"
                  class="country-selector"
                  @focus="handleUserBlur($event, 'countryCode', true)"
                  @change="handleUserBlur($event, 'countryCode', true)"
                  size="medium"
              />
            </FormUserFocusStatusMarker>
          </el-form-item>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.form-address-view {
  width: 100%;
  border-collapse: separate;


  ::v-deep {
    .el-select {
      width: 100%;
    }

    .country-selector {
      max-width: inherit !important;
    }
  }

  .two-column {
    width: 273px;
  }
}

.margin-bottom {
  margin-bottom: 10px;
}

.sub-label {
  height: 20px;
  line-height: 20px;
  color: #616161;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

.flex-row {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  grid-gap: 10px;

  > div {
    flex-grow: 1;
  }
}
</style>
