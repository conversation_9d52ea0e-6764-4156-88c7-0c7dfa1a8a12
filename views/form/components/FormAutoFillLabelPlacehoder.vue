<script lang="ts">
export default{
  name: 'FormAutoFillLabelPlacehoder',
  props: {
    text: {
      type: String,
      default: ''
    },
    tooltip:{
      type: String,
      default: ''
    },
    disableTooltip:{
      type: Boolean,
      default: false
    },
    showBG: {
      type: Boolean,
      default: true
    },
    mask: {
      type: Boolean,
      default: false
    },
    layout:{
      type: String,
      validator: (value) => {
        return ['select', 'input'].includes(value);
      },
      default: 'input'
    }
  },
  methods:{
    content() {
      if(this.mask){
        return String(this.text).replace(/\S/g, '*')
      }
      return this.text
    }
  }
}
</script>

<template>
  <div :class="['form-autofill-label-wrap','layout-for-'+layout]">
  <el-tooltip
      :content="tooltip"
      placement="bottom"
      popper-class="normal-dark_tooltip"
      :disabled="disableTooltip || !tooltip">
      <div class="form-autofill-label-inner" >
        <div class="form-autofill-label mx-ellipsis" :class="{'show-bg': showBG,'use-mask': mask}"> {{content()}}</div>
      </div>

  </el-tooltip>
</div>
</template>

<style scoped lang="scss">
.show-bg{
  background: rgb(232 240 250);
  border-radius: 3px;
}
.use-mask{
  line-height: 30px!important;
}
.layout-for-select{
  padding-right: 25px!important;
}
.form-autofill-label{
  height: 24px;
  padding: 0 5px;
  line-height: 24px;
  font-size: 14px
}
.form-autofill-label-inner{
  display: flex;
  align-items: center;
  width: 100%;
  background: #fff;
}
.form-autofill-label-wrap{
  position: absolute;
  z-index: 1;
  display: flex;
  align-items: center;
  padding: 5px 8px;
  width: 100%;
}
</style>