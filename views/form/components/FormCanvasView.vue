<script lang='ts'>
import Vue, {type PropType} from 'vue';
import {FormRuntimeOptions} from '@model/form/defines/shared';
import {FormViewModel} from '@model/form/defines/formViewModel';
import {FormElementPageModel} from '@model/form/defines/FormPage';
import moment from 'moment-timezone';
import {getCurrentInstance, onUnmounted, ref} from "@vue/composition-api";
const mockHeight = {
  Image: 150,
  Signature: 144,
  Heading: 88,
  SingleLineText: 100,
  UserName: 120,
  MultiSelection: 160,
  SingleSelection: 160,
  MultiLineText: 160,
  Date:100,
  EmailAddress: 100,
  Number: 100,
  PhoneNumber: 100,
  DropdownList:200,
  Address: 344,
  Currency:100,
  FileUpload: 88,
  Paragraph: 88,
}
export default Vue.extend({
  components: {},
  props: {
    /**
     * The default display scale.
     */
    defaultScale: {
      type: String,
      default: 'fit'
    },
    pageModel: {
      type: Object as PropType<FormElementPageModel>,
    },
    scaleable: {
      type: Boolean,
      default: false
    },
    runtimeOptions: {
      type: Object as PropType<FormRuntimeOptions>,
      default: () => ({})
    },
    formViewModel: {
      type: Object as PropType<FormViewModel>,
      default: () => ({})
    },
    enableFullScreen: {
      type: Boolean,
      default: false
    },
    enablePager: {
      type: Boolean,
      default: false
    },
    page: {
      type: Number,
      default: 0
    },
    keepShowFooterBar: {
      type: Boolean,
      default: false
    },
  },
  data() {
    return {
      layoutReady: false,
      currentPage: this.page,
      containerHeight: 0,
      containerWidth: 0,
      initialized: true,
      canOperate: true,
    }
  },
  created() {
    this.$emit('initialized')
  },
  setup(){
    const showPages = ref({})
    const vm = getCurrentInstance()
    let intersectionObserver = new IntersectionObserver((entries) =>{
      entries.forEach(entry =>{
        const isShow = !entry.intersectionRatio <= 0;
        const pageId = entry.target.dataset.id
        if(!pageId){
          return;
        }
        const currState = showPages.value[pageId]
        if(currState !== isShow) {
          if(!isShow){
            pagesState.set(pageId, {height: entry.target.offsetHeight})

          }
          const showItems = {...showPages.value}
          showItems[pageId] = isShow
          showPages.value = showItems
        }
      })
    }, {
      root: vm.proxy.$el
    })
    onUnmounted(()=>{
      intersectionObserver = null
    })
    const observerEl = (el)=>{

      intersectionObserver && intersectionObserver.observe(el)
    }
    const unobserverEl = (el)=>{
      intersectionObserver && intersectionObserver.unobserve(el)
    }
    return {
      observerEl,
      unobserverEl,
      showPages
    }
  },
  computed: {
    isEmptyPage() {
      return this.pageModel?.elements?.length <= 0
    }
  },
  methods: {
    getSubmittedTime() {
      return moment(this.formViewModel?.updatedTime).format('L, hh:mm A')
    },
    getSubmitedName() {
      const submittedId = this.formViewModel.submittedAssigneeId
      const user = this.formViewModel.assignees.find(user => user.id === submittedId)
      return user?.displayName
    },
  }
})
</script>

<template>
  <div class='form-canvas-view'>
    <div class='form-canvas-view-page'>
      <div class='form-canvas-content'>
        <div v-if='isEmptyPage' class='empty-page mx-color-secondary mx-text-c2'>
          <i class='micon-file'/>
          {{ $t('this_page_is_empty') }}
        </div>
        <slot :scale='1'/>
      </div>
    </div>
    <div v-if='formViewModel.isCompleted'
         class='form-canvas-footer mx-secondary'>
      <div>{{ $t('submit_by') }} {{ getSubmitedName() }}</div>
      <div>{{ getSubmittedTime() }}</div>
    </div>
  </div>
</template>

<style scoped lang='scss'>
.form-canvas-view {
  display: flex;
  flex-direction: column;
  justify-content: center;
  width: 100%;
  height: 100%;
  overflow: hidden;
  align-items: center;
}

.empty-page {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  height: 100%;
  flex: 1;
}

.form-canvas-view-page {
  width: 648px;
  height: 100%;
  margin-top: 20px;
  overflow-y: auto;
  background: #fff;
  border: 2px solid transparent;
  border-radius: 10px;
}

.form-canvas-content {

  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  gap: 8px;
  min-height: 100%;
}

.form-canvas-footer {
  align-items: center;
  display: flex;
  justify-content: space-between;
  margin-top: 12px;
  overflow: hidden;
  width: 648px;

  &:first-child {
    flex: 1 1 auto;
    overflow: hidden;
    padding-right: 12px;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  &:last-child {
    flex: 0 0 auto;
  }
}
</style>
