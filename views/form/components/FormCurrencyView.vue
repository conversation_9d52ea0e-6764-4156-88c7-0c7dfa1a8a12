<script lang="ts">
import {defineComponent} from '@vue/composition-api'
import type {PropType} from 'vue'
import {FormElementCurrencyModel} from '@model/form/defines/FormCurrency'
import FormUserFocusStatusMarker from './FormUserFocusStatusMarker.vue'
import MxPreciseNumber from '@views/common/components/input/MxPreciseNumber'
import FormInputElementBase from '@views/form/common/FormInputElementBase'
import {FormRuntimeOptions} from '@model/form/defines/shared'
import {ElementHighlightProps, FormElementEventParams} from '../common/types'
import FormElementLabel from "@views/form/components/FormElementLabel.vue";
import FormElementError from "@views/form/components/FormElementError.vue";
import FormElementSupporting from "@views/form/components/FormElementSupporting.vue";
import {getLengthLimitMessage} from "@views/form/common/lengthLimit";

export default defineComponent({
  name: 'FormCurrencyView',
  components: {FormElementSupporting, FormElementError, FormElementLabel, MxPreciseNumber, FormUserFocusStatusMarker},
  extends: FormInputElementBase,
  props: {
    element: {
      type: Object as PropType<FormElementCurrencyModel>,
      required: true,
      default: null
    },
    runtimeOptions: {
      type: Object as PropType<FormRuntimeOptions>,
      default: () => ({})
    },
    highlight: {
      type: Object as PropType<ElementHighlightProps>,
      default: () => ({})
    }
  },
  data() {
    return {
      modelValue: {...this.element.value},
      focusedField: '',
      focusedValue: ''
    }
  },
  watch: {
    'element.value': function (val) {
      if (this.userIsFocused) {
        // current user is focused this item, when value has changed we need ignore it.
        return
      }

      this.modelValue = {...val}
    }
  },
  computed: {
    lengthLimitMessage () {
      return getLengthLimitMessage(this.element.type, this.element.minLength, this.element.maxLength, this.$t)
    }
  },
  methods: {
    handleUserChange(event, changedProperty: string) {
      this.$emit('change', this.getEventInfo(event, changedProperty))
    },
    handleUserBlur(event, changedProperty: string, enforce: boolean) {

      this.focusedField = ''
      this.focusedValue = ''
      this.$emit('blur', this.getEventInfo(event, changedProperty, enforce))

    },
    handleUserFocus(event, changedProperty: string) {
      this.focusedField = changedProperty
      this.focusedValue = this.modelValue[changedProperty]
      this.$emit('focus', this.getEventInfo(event, changedProperty))
    },
    getEventInfo(event, changedProperty: string, enforce = false) {
      return {
        element: this.element,
        currentValue: this.modelValue,
        enforce,
        changedProperty
      } as FormElementEventParams
    }
  }
})
</script>
<template>
  <div class="form-currency-field">
    <FormElementLabel :element="element"></FormElementLabel>

    <div class="currency-value">
      <FormUserFocusStatusMarker :highlight="highlight.amount">
        <MxPreciseNumber
            v-model="modelValue.amount"
            :disable-auto-update="true"
            :prevent-invalid-char="true"
            :only-pad="true"
            :precision="element.precision"
            :placeholder="placeholder"
            :disabled="isReadonly"
            @change="handleUserChange($event, 'amount')"
            @focus="handleUserFocus($event, 'amount')"
            @blur="handleUserBlur($event, 'amount')"/>
      </FormUserFocusStatusMarker>
      <div class="currency-unit">
        {{ modelValue.code }}
      </div>
    </div>
    <FormElementError :error-message="errorMessage" v-if="errorMessage"></FormElementError>
    <FormElementSupporting v-if="lengthLimitMessage" :content="lengthLimitMessage"></FormElementSupporting>

  </div>
</template>

<style scoped lang="scss">
.form-currency-field {
  .currency-unit {
    flex-shrink: 0;
  }

  .currency-value {
    display: flex;
    justify-content: space-around;
    align-items: center;
    gap: 8px;
  }

  ::v-deep {
    .el-form-item__error {
      position: relative;
      padding: 0;
      margin-left: 1px;
      margin-top: -5px;
    }

    .el-input-number {
      width: 100%;

      input {
        padding: 7px !important;
        text-align: left !important;
      }
    }

    .el-input-number__decrease,
    .el-input-number__increase {
      display: none;
    }
  }
}
</style>
