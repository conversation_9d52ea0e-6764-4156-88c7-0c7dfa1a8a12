<script lang="ts">
import type {PropType} from 'vue'
import {defineComponent} from '@vue/composition-api'
import {FormElementEventParams} from '../common/types'
import {DefaultHighlightFieldName} from '../common/highlightProps'
import {FormElementDropdownListModel} from '@model/form/defines/FormDropdownList'
import isString from 'lodash/isString'
import FormElementHotspot from '@views/form/pdfForm/FormElementHotspot.vue'
import FormInputElementBase from '@views/form/common/FormInputElementBase'
import FormUserFocusStatusMarker from "@views/form/components/FormUserFocusStatusMarker.vue";
import FormElementLabel from "@views/form/components/FormElementLabel.vue";
import FormElementSupporting from "@views/form/components/FormElementSupporting.vue";
import FormElementError from '@views/form/components/FormElementError.vue'

export default defineComponent({
  name: 'FormDropdownListView',
  components: { FormElementError, FormElementSupporting, FormElementLabel, FormUserFocusStatusMarker, FormElementHotspot},
  extends: FormInputElementBase,
  props: {
    element: {
      type: Object as PropType<FormElementDropdownListModel>,
      required: true,
      default: null
    }
  },
  data () {
    return {
      modelValue: this.element.value,
      DefaultHighlightFieldName
    }
  },
  computed: {
    placeholder () {
      if (this.isPreviewCompleted) {
        return ''
      }
      return this.element.placeholder
    }
  },
  watch: {
    'element.value': function (val) {

      this.modelValue = val
    }
  },
  methods: {
    handleUserChange (event) {
      this.$emit('change', this.getEventInfo())
      this.$emit('blur', this.getEventInfo())
    },
    handleUserFocus () {
      this.$emit('focus', this.getEventInfo())
    },
    getEventInfo () {
      return {
        element: this.element,
        currentValue: this.modelValue,
        changedProperty: '',
        enforce: true
      } as FormElementEventParams
    },
  }
})
</script>

<template>
  <div class="form-single-selection-field" :class="{'has-error': !!errorMessage}">
    <FormElementLabel :element="element"></FormElementLabel>
    <FormElementSupporting
      v-if="element.supporting"
      :content="element.supporting"
    ></FormElementSupporting>
    <FormUserFocusStatusMarker :highlight="highlight[DefaultHighlightFieldName]">
      <el-select
        v-model="modelValue"
        :trim="true"
        :placeholder="placeholder"
        :disabled="isReadonly"
        :readonly="isReadonly"
        :popper-append-to-body="true"
        @focus="handleUserFocus"
        @change="handleUserChange"
      >
        <el-option
          v-for="(item, index) in element.options"
          :key="index"
          class="form-dropdown-item"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
    </FormUserFocusStatusMarker>
    <FormElementError :error-message="errorMessage" v-if="errorMessage"></FormElementError>
    <FormElementHotspot v-if="selectable" :selected="selected" @click="$emit('select', element)" />
  </div>
</template>
<style scoped lang="scss">
.form-single-selection-field {
  .el-select {
    display: block;
  }
}
  .has-error {
    ::v-deep {
      .el-input__inner {
        border: 1px solid #B22424!important;
      }
    }
  }
</style>
