<script lang="ts">
export default {
  name: 'FormElementError',
  props: {
    errorMessage: {
      type: String,
      default: ''
    }
  }
}
</script>

<template>
  <div class="form-error-msg mx-text-c2">
    <i class="micon-close-d3" />
    <span>{{ errorMessage }}</span>
  </div>
</template>

<style scoped lang="scss">
.form-error-msg {
  display: flex;
  align-items: center;
  margin: 4px;
  color: $mx-color-var-negative;

  i {
    font-size: 17px;
    color: $mx-color-var-negative;
    background: transparent;
  }

  span {
    overflow-wrap: break-word;
  }
}
</style>