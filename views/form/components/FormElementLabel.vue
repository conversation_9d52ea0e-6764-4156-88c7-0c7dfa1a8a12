<script lang="ts">
import {defineComponent} from "@vue/composition-api";
import type {PropType} from "vue";
import { FormElementAnyInputModel} from "@model/form/defines/allType";
import {FormRuntimeOptions} from "@model/form/defines/shared";

export default defineComponent({
  name: 'FormElementLabel',
  components: {},
  props: {
    element: {
      type: Object as PropType<FormElementAnyInputModel>,
      required: true,
      default: null
    },
    runtimeOptions: {
      type: Object as PropType<FormRuntimeOptions>,
      default: () => ({})
    },
  }
})
</script>

<template>
<div class="form-element-label">
  <span class="form-label-text" v-if="!element.hideLabel">{{element.label}}</span>
  <span class="form-label-append" v-if="element.required">*</span>
</div>
</template>

<style scoped lang="scss">
  .form-element-label{
    margin-bottom: 4px;
    padding-left: 5px;
  }
  .form-label-text{
    font-size: 14px;
    font-weight: 600;
    line-height: 20px;
    word-break: break-word;
    white-space: pre-wrap;
  }
  .form-label-append{
    color: red;
    vertical-align: sub;
  }
</style>