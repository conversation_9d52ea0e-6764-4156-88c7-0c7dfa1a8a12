<script lang="ts">
import FormImagePreview from './FormImagePreview.vue'
export default {
  name: 'FormElementSupporting',
  components: {FormImagePreview},
  props: {
    content: {
      type: String,
      required: true,
      default: ''
    }
  },
  methods:{
    handleClickImage(ev){
      this.showImage = ev.detail.imgUrl
    }
  }
}
</script>

<template>
  <div>
  <div class="form-element-subtext mx-color-secondary mx-text-c2" v-safe-marked="content" @clickImage="handleClickImage"></div>
    <FormImagePreview v-if="showImage" imageUrl="showImage"></FormImagePreview>
  </div>
</template>

<style scoped lang="scss">
.form-element-subtext{
  white-space: break-spaces;
  word-break: break-word;
  margin: 0 0 8px 0;
  padding-left: 6px;
  font-weight: 400;

  ::v-deep {
    a:hover{
      text-decoration: underline;
    }
    img{
      max-width: 60px;
      max-height: 60px;
      cursor: pointer;
      vertical-align: bottom;
    }
    ul, ol {
      margin-block-start: 1em;
      margin-block-end: 1em;
      margin-inline-start: 0px;
      margin-inline-end: 0px;
      padding-inline-start: 20px;
      unicode-bidi: isolate;
      line-height: 1.2;
      margin: 0!important;
    }
    p {
      margin: 0!important;
    }
    ul {
      list-style: disc !important;
      display: block;
      li {
        list-style-type: disc;
      }
    }
    ol {
      list-style-type: decimal;
      li {
        list-style-type: decimal;
      }
    }
  }
}
</style>