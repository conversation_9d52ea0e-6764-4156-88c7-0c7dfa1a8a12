<script lang="ts">
import {defineComponent} from '@vue/composition-api'
import type {PropType} from 'vue'
import {FormElementAnyInputModel} from '@model/form/defines/allType'
import {FormElementType, FormRuntimeOptions, FormScenes} from '@model/form/defines/shared'
import {ElementHighlightProps} from '@views/form/common/types';
import {DefaultHighlightFieldName} from '@views/form/common/highlightProps'
import {BrowserUtils} from "@commonUtils";

export default defineComponent({
  name: 'FormElementView',
  components: {},
  props: {
    element: {
      type: Object as PropType<FormElementAnyInputModel>,
      required: true,
      default: null
    },
    runtimeOptions: {
      type: Object as PropType<FormRuntimeOptions>,
      default: () => ({})
    },
    scale: {
      type: Number,
      default: 1
    },
    selectable: {
      type: Boolean,
      default: false
    },
    selected: {
      type: Boolean,
      default: false
    },
    highlight: {
      type: Object as PropType<ElementHighlightProps>,
      default: () => ({})
    },
    pageWidth: {
      type: Number,
      default: 0
    }
  },
  data () {
    return {
      DefaultHighlightFieldName,
      isMouseHover: false,
      isMobile: BrowserUtils.isMobile,
    }
  },
  computed: {
    showDashedBorder () {
      return [FormElementType.SingleSelection, FormElementType.MultiSelection].includes(this.element.type) && this.element.options.length > 1
    },
    showBorder () {
      if(![FormElementType.SingleSelection, FormElementType.MultiSelection].includes(this.element.type)){
        return true
      }
      return (this.element.options.length === 1) ? false: true
    },
    isPreview(){
      return [FormScenes.Preview].includes(this.runtimeOptions.scenes)
    }
  },
  methods: {
  }
})
</script>

<template>
  <div
      :class="{
      'form-element': true,
      'is-field-selected': selected,
      'is-preview': isPreview,
      'is-show-highlight': isMouseHover || isMobile,
      'is-field-error': element.errors.length
    }"
      :data-id="element.id"
      @mouseover="isMouseHover = true"
      @mouseleave="isMouseHover = false"
  >
    <slot />
  </div>
</template>

<style scoped lang="scss">
.form-element {
  position: relative;
  padding: 12px 22px;
}
.is-preview{
  ::v-deep{
      .el-input__inner,.el-textarea__inner {
        box-shadow: none !important;
        border-color: #8A8A8A !important;
        color: #000 !important;
        background-color: white !important;
      }

  }
}
::v-deep {
  .el-form-item__content{
    line-height: inherit!important;
  }
  .el-form-item {
    margin-bottom: 0 !important;
  }
  .el-form-item__error {
    position: relative;
    padding: 0;
    margin: 4px 0;
    margin-left: 1px;
    i{
      margin-right: -3px;
    }
  }
}

.is-field-error {
  z-index: 1;
}

.is-field-selected {
  ::v-deep {
    .pdf-selection-item::before {
      border: var(--pdf-element-border-size) solid var(--pdf-element-color-selected);
    }
  }
}
.is-show-highlight {
  z-index: 2;
  ::v-deep {
    .online-user-name {
      visibility: visible!important;
    }
    .pdf-item-error {
      visibility: visible!important;
    }
  }
}
</style>
