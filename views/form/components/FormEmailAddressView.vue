<script lang="ts">
import type { PropType } from 'vue'
import { defineComponent } from '@vue/composition-api'
import FormUserFocusStatusMarker from './FormUserFocusStatusMarker.vue'
import { FormElementEventParams } from '../common/types'
import { DefaultHighlightFieldName } from '../common/highlightProps'
import { FormElementEmailAddressModel } from '@model/form/defines/FormEmailAddress'
import FormInputElementBase from '@views/form/common/FormInputElementBase'
import FormElementLabel from "@views/form/components/FormElementLabel.vue";
import FormElementError from "@views/form/components/FormElementError.vue";
import FormAutoFillLabelPlacehoder from "@views/form/components/FormAutoFillLabelPlacehoder.vue";

export default defineComponent({
  name: 'FormEmailAddressView',
  components: {FormAutoFillLabelPlacehoder, FormElementError, FormE<PERSON>Label, FormUserFocusStatusMarker },
  extends: FormInputElementBase,
  props: {
    element: {
      type: Object as PropType<FormElementEmailAddressModel>,
      required: true,
      default: null
    }

  },
  data () {
    return {
      modelValue: this.element.value,
      DefaultHighlightFieldName,
      userIsFocused: false
    }
  },

  watch: {
    'element.value': function (val) {
      if (this.userIsFocused) {
        // current user is focused this item, when value has changed we need ignore it.
        return
      }
      this.modelValue = val
    }
  },
  methods: {
    handleUserChange () {
      this.$emit('change', this.getEventInfo())
    },
    handleUserBlur () {
      this.userIsFocused = false
      this.$emit('blur', this.getEventInfo())
    },
    handleUserFocus () {
      this.userIsFocused = true
      this.$emit('focus', this.getEventInfo())
    },
    getEventInfo () {
      return {
        element: this.element,
        currentValue: this.modelValue,
        changedProperty: ''
      } as FormElementEventParams
    }
  }
})
</script>

<template>
  <div class="form-email-address-field">
    <FormElementLabel :element="element"></FormElementLabel>

      <div
        v-if="isPreviewCompleted"
        class="preview-content">
        {{ isNeedShowMask ? maskedValue : modelValue }}
      </div>
      <template v-else>
        <FormUserFocusStatusMarker :highlight="highlight[DefaultHighlightFieldName]">
          <FormAutoFillLabelPlacehoder
              :text="$t('Assignees_Email_Address')"
              :tooltip="$t('auto_fill_email_address')"
              v-if="isShowAutoFillLabel"></FormAutoFillLabelPlacehoder>
          <component
              :is="inputComponent"
              v-bind="getInputOptions({maxLength: 350})"
            v-model="modelValue"
            type="email"
            :placeholder="placeholder"
            autocomplete="off"
            @input="handleUserChange"
            @focus="handleUserFocus"
            @blur="handleUserBlur" />
        </FormUserFocusStatusMarker>
      </template>
    <FormElementError v-if="errorMessage" :error-message="errorMessage"></FormElementError>

  </div>
</template>
<style scoped lang="scss">
.form-email-address-field {
  ::v-deep {
    .el-form-item__error {
      position: relative;
      padding: 0;
      margin-left: 1px;
      margin-top: -5px;
    }
  }

  input {
    border: 1px solid #8a8a8a;
    border-radius: 6px;
  }

  .preview-content {
    height: 36px;
    overflow-y: auto;
    border: 1px solid #8a8a8a;
    border-radius: 5px;
    padding: 5px 8px;
    word-wrap: break-word;
    cursor: not-allowed;
    line-height: 24px;
  }
}
</style>
