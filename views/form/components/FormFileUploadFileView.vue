<script lang="ts">
  import {defineComponent} from '@vue/composition-api'
import type {PropType} from 'vue'
import utils from '@views/common/utils/utils'
import FileThumbnail from '@views/common/components/FileThumbnail.vue';
import errorMessageUtil from "@views/common/components/uploader/utils";
  import {FileUploadValue} from "@model/form/defines/FormFileUpload";

export default defineComponent({
  name: 'FormFileUploadFileView',
  components: {FileThumbnail},
  props: {
    maxFileSize: {
      type: Number,
      default: 0
    },
    loading: {
      type: Boolean,
      default: false
    },
    file: {
      type: Object as PropType<FileUploadValue>,
      required: true,
      default: null
    },
    isPreview: {
      type: Boolean,
      default: false
    },
    progressPercentage: {
      type: Number,
      default: 0
    },
    showProgress: {
      type: Boolean,
      default: false
    },
    editable:{
      type: Boolean,
      default: false
    },
    progressColor: {
      type: String,
      default: '#1C72E3'
    }
  },
  emits: ['change'],
  computed: {

    fileType () {
      return this.file.fileType?.toLowerCase()
    },
    fileSize () {
      if (this.file.size) {
        const {number, unit} = utils.getSizeObj(this.file.size, true)
        return number + unit
      } else {
        return ''
      }
    },
    showRetry() {
      const error = this.file.error
      if (error?.allowRetry) {
        return true
      }
      return false;
    },
    isLoading () {
      return this.file.loading
    }
  },
  methods: {
    getServerErrorMessage(error) {
      if(error.isRemoveError) {
        return this.$t('Unable_to_delete')
      }
      return errorMessageUtil.getErrorMessage(error, {
        uploadFailed: this.$t('Upload_failed'),
        virusDetected: this.$t('Malware_detected_in_file'),
        exceedMaxSize: this.$t('file_size_limit_reached'),
        fileTypeNotSupported: this.$t('File_type_unsupported')
      })
    },
    handleRetry() {
      if(this.file.error.isRemoveError){
        this.handleRemove()
      }else{
        this.$emit('retryUpload', this.file)
      }
    },
    handleRemove() {
      this.$emit('remove', this.file)
    },
    handlePreviewFile() {
      if (this.isPreview) {
        this.$emit('previewFile', this.file)
      }
    }
  }
})
</script>

<template>
  <div class="form-file-view" :class="{'error': file.error}">
    <div class="grid-left mx-ellipsis" :class="{'mx-clickable': isPreview}" @click="handlePreviewFile">
      <FileThumbnail
          class="file-image"
          :source="file.thumbnail"/>
      <div class="mx-ellipsis file-name-wrap">
        <div class="display-name mx-ellipsis">
          {{ file.name }}
        </div>
        <div class="file-size mx-color-secondary sub-title">
          {{ file.error ? getServerErrorMessage(file.error) : fileSize }}
        </div>
      </div>
    </div>
    <div class="grid-right" v-if="editable">
      <div v-if="isLoading" class="loading-spinner">
        <i class="micon-loading-spinner"></i>
      </div>
      <template v-else>
        <div v-if="showRetry"
             class="status retry error mx-clickable"
             @click.stop="handleRetry">
          <el-tooltip
              placement="top"
              :content="$t('try_again')"
              popper-class="overflow-control-tooltip">
            <i class="micon-retry font-icon-sm"/>
          </el-tooltip>
        </div>
        <div v-if="file.SPath || showRetry"
             class="action">
          <el-tooltip
              placement="top"
              :content="$t('remove')"
              popper-class="overflow-control-tooltip">
            <i
                class="micon-error"
                @click.stop="handleRemove"/>
          </el-tooltip>
        </div>
      </template>
    </div>
  </div>
</template>

<style scoped lang="scss">
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.grid-right {
  padding-right: 10px;
  display: flex;
  align-items: center;
  grid-gap: 10px;
}

.loading-spinner {
  display: flex;
  align-items: center;
  justify-content: center;
  animation: spin 2600ms infinite linear;
}
.mx-clickable{
  cursor: pointer!important;
}
.form-file-view {
  display: flex;
  justify-content: space-between;
  height: 60px;
  align-items: center;
  padding: 0;
  border-radius: 6px;

  .grid-right {
    min-width: 24px;
    min-height: 24px;
  }

  .always-show {
    visibility: visible;
  }

  .action {
    visibility: hidden;

    i {
      color: $mx-color-font-secondary;
      font-size: 16px;
      cursor: pointer;
    }
  }

  &:hover {
    background-color: $mx-color-var-fill-quaternary;

    .action {
      visibility: visible;
    }
  }

  &.error {
    background-color: #f7e9e9 !important;

    .sub-title {
      color: $mx-color-var-negative;
    }

    .action {
      visibility: visible;
    }

    i {
      color: $mx-color-var-negative;
    }
  }

  ::v-deep .ddr-file-disp {
    .source-file {
      gap: 0;

      .file-label {
        margin-left: 16px;

        .file-name {
          line-height: 20px;
        }

        .file-subtitle {
          line-height: 20px;
          font-size: 14px;
        }
      }
    }
  }
}

.sub-title {
  color: $mx-color-var-label-secondary;
}

.grid-left {
  display: flex;
  flex-direction: row;
  align-items: center;
  grid-gap: 10px;

  .file-image {
    flex-shrink: 0;
  }

  .file-name-wrap {
    flex-grow: 1;
  }
}
</style>
