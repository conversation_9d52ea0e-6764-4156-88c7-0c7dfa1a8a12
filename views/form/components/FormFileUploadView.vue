<script lang="ts">
import type {PropType} from 'vue'
import {defineComponent} from '@vue/composition-api'
import {FormElementEventParams} from '../common/types'
import {DefaultHighlightFieldName} from '../common/highlightProps'
import {FileUploadValue, FormElementFileUploadModel} from '@model/form/defines/FormFileUpload'
import FormInputElementBase from '@views/form/common/FormInputElementBase'
import FormFileUploadFileView from './FormFileUploadFileView.vue'
import {mapGetters} from 'vuex'
import {ObjectUtils} from '@commonUtils'

import {isNumber} from 'lodash'
import FilePicker from '@views/common/components/FilePicker/FilePicker.vue'
import FormElementLabel from "@views/form/components/FormElementLabel.vue";
import FormElementSupporting from "@views/form/components/FormElementSupporting.vue";
import FormUserFocusStatusMarker from "@views/form/components/FormUserFocusStatusMarker.vue";
import {PickedFile} from "@views/common/components/FilePicker";
import {FormScenes} from "@model/form/defines/shared";

export default defineComponent({
  name: 'FormFileUploadView',
  components: {
    FormUserFocusStatusMarker,
    FormElementSupporting,
    FormElementLabel, FilePicker, FormFileUploadFileView
  },
  extends: FormInputElementBase,
  props: {
    element: {
      type: Object as PropType<FormElementFileUploadModel>,
      required: true,
      default: null
    }
  },
  data() {
    return {
      modelValue: this.element.value,
      DefaultHighlightFieldName,
      userIsFocused: false,
      localErrorMessage: ''
    }
  },
  computed: {
    ...mapGetters('privileges', ['maxFileSize', 'allowedFileTypesAll']),
    ...mapGetters('user', ['currentUser']),
    fileList() {
      return this.element.value as FileUploadValue[]
    },
    editable() {
      return this.runtimeOptions.scenes === FormScenes.FillForm
    },
    serverAllowedFileTypes() {
      if (this.allowedFileTypesAll) {
        return this.allowedFileTypesAll.join(',')
      } else {
        return ''
      }
    },
    fileSizeLimitExceeded() {
      const uploadCount = this.element.value?.length
      return uploadCount >= this.element.maxFileCount
    },
    allowMaxFileSize() {
      let maxBody = ObjectUtils.getByPath(this.currentUser, 'userCap.client_max_body_size')
      let maxCloud = ObjectUtils.getByPath(this.currentUser, 'userCap.user_cloud_max')
      if (maxBody) {
        maxBody = ObjectUtils.bytesToMB(maxBody)
      }
      if (maxCloud) {
        maxCloud = ObjectUtils.bytesToMB(maxCloud)
      }
      let maxFileSize = this.element.maxFileSize
      if (maxFileSize) {
        maxFileSize = parseInt(maxFileSize)
      }
      let sdkMaxSize = parseInt(this.maxFileSize)
      if (sdkMaxSize) {
        sdkMaxSize = ObjectUtils.bytesToMB(sdkMaxSize)
      }

      const sizes = [maxBody, maxCloud, maxFileSize, sdkMaxSize].filter(n => isNumber(n) && n > 0)

      return Math.min(...sizes)
    },
    lastErrorMessage() {
      return this.localErrorMessage || this.errorMsg
    }
  },
  methods: {
    handleUserChange() {
      this.$emit('change', this.getEventInfo())
    },
    handleRemoveFile(file: FileUploadValue) {
      this.$emit('blur', {
        element: this.element,
        currentValue: this.modelValue,
        changedProperty: '',
        removeFile: file
      } as FormElementEventParams)
    },
    retryUpload(file: PickedFile) {
      this.handleUploadFile(file)
    },
    handleUploadFile(file: PickedFile) {
      this.userIsFocused = false
      this.$emit('blur', {
        element: this.element,
        currentValue: this.modelValue,
        uploadFile: file,
        changedProperty: ''
      } as FormElementEventParams)
    },
    handleSelectFile(files) {
      const file = files[0]
      this.localErrorMessage = ''
      this.handleUploadFile(file)

    },
    handleUserFocus() {
      this.userIsFocused = true
      this.$emit('focus', this.getEventInfo())
    },
    getEventInfo() {
      return {
        element: this.element,
        currentValue: this.modelValue,
        changedProperty: ''
      } as FormElementEventParams
    },
    handlePreviewFile(file) {
      this.$emit('action', {
        action: 'previewFile',
        file
      })
    },

    handleError(err) {
      if (err.fileCountExceed) {
        this.localErrorMessage = err.message
      } else if (err.fileSizeExceed) {
        this.localErrorMessage = this.$t('The_selected_file_exceeds_max_limit_size', {size: `${this.allowMaxFileSize}MB`})
      } else if (err.fileTypeError) {
        this.localErrorMessage = this.$t('File_type_not_supported')
      } else {
        this.localErrorMessage = err.message
      }
    }
  }
})
</script>

<template>
  <div class="form-file-upload">
    <FormElementLabel :element="element"></FormElementLabel>
    <template v-if="!runtimeOptions.isCompleted">
    <div class="mock-uploader-area mx-secondary" v-if="fileSizeLimitExceeded">
      {{ $t('The_current_field_has_reached_the_maximum_number_of_files') }}
    </div>
    <FormUserFocusStatusMarker
        v-else
        :highlight="highlight[DefaultHighlightFieldName]" :show-border="true">
      <FilePicker
          class="form-file-picker"
          :readonly="!isFillMode"
          :file-accept="element.fileAccept || serverAllowedFileTypes"
          :file-max-siz="allowMaxFileSize"
          @select="handleSelectFile"
          @focus="handleUserFocus"
          @error="handleError"/>
    </FormUserFocusStatusMarker>
    </template>
    <FormElementSupporting v-if="element.supporting" :content="element.supporting"></FormElementSupporting>
    <el-alert
        v-if="lastErrorMessage"
        class="file-upload-error"
        type="warning"
        :show-icon="true"
        :closable="false"
        :center="false"
        :title="lastErrorMessage" />
    <div class="form-upload-file-list">
    <FormFileUploadFileView
        :is-preview="isPreview"
        v-for="(item, index) in element.value"
        :key="index"
        :loading="item.loading"
        :editable="editable"
        @previewFile="handlePreviewFile"
        @retryUpload="retryUpload"
        @retryRemove="handleRemoveFile"
        :file="item"
        @remove="handleRemoveFile"
        @click="handleUserFocus"/>
    </div>
  </div>
</template>
<style scoped lang="scss">
.mock-uploader-area {
  border-radius: 6px;
  border: 2px dashed #c6c6c6;
  height: 92px;
  display: flex;
  align-items: center;
  justify-content: center;
  grid-gap: 5px;
  font-size: 14px;
  padding: 0 10px;
  text-align: center;
}
.file-upload-error{
  margin-top: 10px;
  ::v-deep {
    .el-alert__title {
      font-weight: 590 !important;
    }
  }
}
.form-upload-file-list{
  margin: 10px 0;
}

.form-file-upload {
  .file-upload-error {
    margin-top: 8px;
  }

  input {
    border: 1px solid #8a8a8a;
    border-radius: 6px;
  }

  ::v-deep {
    .el-form-item__error {
      position: relative;
      padding: 0;
      margin-left: 1px;
      margin-top: -2px;
    }

    .border-color-with-user {
      .empty-state,
      .form-file-view {
        border: none;
      }
    }
  }

  .preview-content {
    height: 36px;
    overflow-y: auto;
    border: 1px solid #8a8a8a;
    border-radius: 5px;
    padding: 5px 8px;
    word-wrap: break-word;
    cursor: not-allowed;
    line-height: 24px;
  }
}
</style>
