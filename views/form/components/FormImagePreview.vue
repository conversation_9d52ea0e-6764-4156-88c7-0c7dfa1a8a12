<template>
  <full-screen-dialog
    class="esign-builder fill-out-form"
    title=""
    @close="$emit('close')">
    <div class="form-img-preview">
      <img :src="imageUrl"/>
    </div>
  </full-screen-dialog>
</template>

<script lang="ts">
import {defineComponent} from '@vue/composition-api'
import FullScreenDialog from '@views/common/components/FullScreenDialog'

export default defineComponent({
  name: 'FillForm',
  components: {FullScreenDialog},
  props: {
    imageUrl:{
      type: String,
      default: ''
    }
  }
})
</script>

<style scoped lang="scss">
.form-img-preview{
  display: flex;
  width: 100%;
  height: 100%;
  justify-content: center;
  align-items: center;
  background: #f4f4f4 ;
}

</style>
