<template>
  <div
    class="form-image-field"
    :class="['form-field', imageAlignmentClass]">
    <div
      class="form-img">
      <img
        v-if="element.imageURL"
        :src="element.imageURL"
        :alt="element.imageAlt"
        :width="element.imageWidth+'px'">
      <ImgPlaceholder
        v-else
        :type="0"
        :border="false"
        width="200"
        height="124" />
    </div>
  </div>
</template>

<script lang="ts">
import  ImgPlaceholder from '@views/common/components/ImgPlaceholder.vue';
import {defineComponent} from '@vue/composition-api';
import type { PropType } from 'vue'
import { FormElementImageModel } from '@model/form/defines/FormImage'

export default defineComponent({
  name: 'FormImageView',
  components: {ImgPlaceholder},
  props: {
    element: {
      type: Object as PropType<FormElementImageModel>,
      required: true
    }
  },
  computed: {
    supporting () {
      const text = this.element.supporting || ''
      return text.replace(/\r?\n/g, '<br/>').replace(/\t/g, '&nbsp;&nbsp;&nbsp;&nbsp;')
    },
    imageAlignmentClass () {
      if(!this.element.imageUUID || !this.element.imageAlignment){
        return 'image-layout-center'
      }
      return `image-layout-${this.element.imageAlignment}`

    }
  }
})
</script>


<style scoped lang="scss">
.form-img {
  display: inline;
  img{
    border-radius: 6px;
  }
}



.form-image-field {
  display: flex;
  width: 100%;


  &.image-layout-left {
    justify-content: flex-start;
  }

  &.image-layout-center {
    justify-content: center;
  }

  &.image-layout-right {
    justify-content: flex-end;
  }
}
</style>

