<script lang="ts">
import type { PropType } from 'vue'
import { defineComponent } from '@vue/composition-api'
import InputHighlight from './InputHighlight.vue'
import { FormElementEventParams } from '../common/types'
import { DefaultHighlightFieldName } from '../common/highlightProps'
import {FormElementMultiSelectionModel} from '@model/form/defines/FormMultiSelection'
import FormInputElementBase from '@views/form/common/FormInputElementBase'
import FormElementLabel from "@views/form/components/FormElementLabel.vue";
import FormElementSupporting from "@views/form/components/FormElementSupporting.vue";
import FormElementError from "@views/form/components/FormElementError.vue";
import FormUserFocusStatusMarker from "@views/form/components/FormUserFocusStatusMarker.vue";
import clone from 'lodash/clone';

export default defineComponent({
  name: 'FormMultiSelectionView',
  components: {FormUserFocusStatusMarker, FormElementSupporting,FormElementError, FormElementLabel, InputHighlight },
  extends: FormInputElementBase,
  props: {
    element: {
      type: Object as PropType<FormElementMultiSelectionModel>,
      required: true,
      default: null
    }
  },
  data () {
    return {
      modelValue: this.element.value,
      DefaultHighlightFieldName
    }
  },

  watch: {
    'element.value': function (val) {
      this.modelValue = clone(val)
    }
  },
  methods: {
    handleUserChange (checked, value) {
      // if(checked){
      //   if(this.modelValue.indexOf(value) < 0){
      //     this.modelValue.push(value)
      //   }
      // }else{
      //   this.modelValue = this.modelValue.filter(item => item !== value)
      // }
      this.$emit('change', this.getEventInfo())
      this.$emit('blur', this.getEventInfo())
    },
    getEventInfo () {
      return {
        element: this.element,
        currentValue: this.modelValue,
        changedProperty: '',
        enforce: true,
        withFillStatus: true
      } as FormElementEventParams
    }
  }
})
</script>

<template>
  <div class="form-multi-selection-field" :class="{'has-error': !!errorMessage}">
    <FormElementLabel :element="element" />
    <FormElementSupporting v-if="element.supporting" :content="element.supporting"></FormElementSupporting>
    <FormUserFocusStatusMarker
        :highlight="highlight[DefaultHighlightFieldName]"
        :show-border="true">
    <el-checkbox-group
        v-model="modelValue"
        :disabled="isReadonly"
        :class="{vertical:true, 'is-error': errorMessage}">
        <div
          v-for="(item, index) in element.options"
          :key="index"
          class="">
          <el-checkbox
            class="option-item"
            :label="item.label"
            :value="item.value"
            @change="handleUserChange">
            <span>{{ item.label }}</span>
          </el-checkbox>
        </div>
      </el-checkbox-group>
    </FormUserFocusStatusMarker>
    <FormElementError v-if="errorMessage" :error-message="errorMessage"></FormElementError>

  </div>
</template>
<style scoped lang="scss">
.form-multi-selection-field {
  ::v-deep {
    .el-form-item__error {
      position: relative;
      padding: 0;
      margin-left: 1px;
      margin-top: -5px;
    }


    .el-checkbox {
      &.is-checked {
        .el-checkbox__input.is-disabled.is-checked {
          .el-checkbox__inner {
            background-color: var(--duo-primary-color) !important;

            i {
              color: #fff !important;
            }
          }
        }
      }

      .el-checkbox__inner {
        box-shadow: none !important;
        border-color: #8A8A8A !important;
        color: #000 !important;
      }

      .el-checkbox__label {
        color: $mx-color-var-text-primary !important;
      }
    }

  }
  .has-error {
    ::v-deep {
      .el-checkbox__inner {
        border-color: #B22424!important;
      }
    }
  }

  .vertical {
    margin-left: 6px;
    label {
      width: 100%;
      display: flex;
      align-items: flex-start;
    }

    .option-item {
      margin-left: 0px !important;
    }
  }
}
</style>
