<script lang="ts">
import type { PropType } from 'vue'
import { defineComponent } from '@vue/composition-api'
import FormUserFocusStatusMarker from './FormUserFocusStatusMarker.vue'
import { FormElementEventParams } from '../common/types'
import { DefaultHighlightFieldName } from '../common/highlightProps'
import { FormElementNumberModel } from '@model/form/defines/FormNumber'
import MxDDRNumberInput from '@views/ddr/MxDDRNumberInput'
import NumberInput from '@views/form/base/NumberInput.vue'
import FormInputElementBase from '../common/FormInputElementBase'
import FormElementLabel from "@views/form/components/FormElementLabel.vue";
import FormElementError from "@views/form/components/FormElementError.vue";
import FormElementSupporting from "@views/form/components/FormElementSupporting.vue";
import FormProtectedAlert from "@views/form/components/FormProtectedAlert.vue";
import {getLengthLimitMessage} from "@views/form/common/lengthLimit";

export default defineComponent({
  name: 'FormNumberView',
  components: {
    FormProtectedAlert,
    FormElementSupporting, FormElementError, FormElementLabel, FormUserFocusStatusMarker, MxDDRNumberInput, NumberInput },
  extends: FormInputElementBase,
  props: {
    element: {
      type: Object as PropType<FormElementNumberModel>,
      required: true,
      default: null
    }
  },
  data () {
    const modelValue = this.element.value

    return {
      modelValue,
      DefaultHighlightFieldName,
      userIsFocused: false
    }
  },
  computed: {
    lengthLimitMessage () {
      return getLengthLimitMessage(this.element.type, this.element.minLength, this.element.maxLength, this.$t)
    }
  },
  watch: {
    'element.value': function (val) {
      if (this.userIsFocused) {
        // current user is focused this item, when value has changed we need ignore it.
        return
      }
      this.modelValue = val
    }
  },
  methods: {
    handleUserChange () {
      this.$emit('change', this.getEventInfo())
    },
    handleUserBlur () {
      this.userIsFocused = false
      this.$emit('blur', this.getEventInfo())
    },
    handleUserFocus () {
      this.userIsFocused = true
      this.$emit('focus', this.getEventInfo())
    },
    getEventInfo () {
      return {
        element: this.element,
        currentValue: this.modelValue,
        changedProperty: ''
      } as FormElementEventParams
    }
  }
})
</script>

<template>
  <div class="form-number-field">
    <FormElementLabel :element="element"></FormElementLabel>
     <FormUserFocusStatusMarker :highlight="highlight[DefaultHighlightFieldName]">
        <MxDDRNumberInput
          v-if="runtimeOptions.enableDDR"
          v-model="modelValue"
          :placeholder="placeholder"
          :disabled="isReadonly"
          :readonly="isReadonly"
          :precision="element.precision"
          :options="{
            jsonEscape: true,
            hideDDRSelector: isPreviewTemplate
          }"
          @input="handleUserChange" />
        <NumberInput
          v-else
          v-model="modelValue"
          :disable-auto-event="true"
          :prevent-invalid-char="true"
          :disable-auto-update="true"
          :only-pad="true"
          :precision="element.precision"
          :placeholder="placeholder"
          :disabled="isReadonly"
          @input="handleUserChange"
          @focus="handleUserFocus"
          @blur="handleUserBlur" />
      </FormUserFocusStatusMarker>
    <FormElementError v-if="errorMessage" :error-message="errorMessage"></FormElementError>
    <FormElementSupporting v-if="element.supporting" :content="element.supporting"></FormElementSupporting>
    <FormElementSupporting v-if="lengthLimitMessage" :content="lengthLimitMessage"></FormElementSupporting>
    <FormProtectedAlert v-if="element.isProtected"></FormProtectedAlert>
  </div>
</template>

<style scoped lang="scss">
.form-number-field {
  ::v-deep {
    .el-input-number {
      width: 100%;
      line-height: 36px;

      input {
        padding: 7px !important;
        text-align: left !important;
      }
    }

    .el-form-item__error {
      position: relative;
      padding: 0;
      margin-left: 1px;
      margin-top: -5px;
    }

    .el-input-number__decrease,
    .el-input-number__increase {
      display: none;
    }
  }
}
</style>
