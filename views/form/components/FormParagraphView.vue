<script lang="ts">
import type { PropType } from 'vue'
import { defineComponent } from '@vue/composition-api'
import { FormElementParagraphModel } from '@model/form/defines/FormParagraph'
import FormElementLabel from "@views/form/components/FormElementLabel.vue";

export default defineComponent({
  name: 'FormParagraphView',
  components: {FormElementLabel},
  props: {
    element: {
      type: Object as PropType<FormElementParagraphModel>,
      required: true
    }
  },

  computed: {
    displayValue () {
      const val = this.element.text
      if (!val) {
        return this.$t('No_text')
      } else {
        return val
      }
    },
    hasContent () {
      const val = this.element.text
      return !!val
    }
  }
})
</script>
<template>
  <div class="form-paragraphs">
    <FormElementLabel :element="element"></FormElementLabel>

    <div class="form-paragraphs-content">
      <span
        v-safe-html="displayValue"
        :class="{ 'mx-color-secondary': !hasContent }" />
    </div>
  </div>
</template>

<style scoped lang="scss">
.form-paragraphs-content {
  margin-left: 4px;

  ::v-deep {
    ul {
      display: block;
      list-style-type: disc;
      margin-block-start: 1em;
      margin-block-end: 1em;
      margin-inline-start: 0px;
      margin-inline-end: 0px;
      padding-inline-start: 40px;
      unicode-bidi: isolate;

      li {
        list-style-type: disc;
      }
    }

    ol {
      list-style-type: decimal;

      li {
        list-style-type: decimal;
      }
    }
  }
}
</style>
