<script lang="ts">
import type { PropType } from 'vue'
import { defineComponent } from '@vue/composition-api'
import FormUserFocusStatusMarker from './FormUserFocusStatusMarker.vue'
import { FormElementEventParams } from '../common/types'
import CountrySelector from '@views/common/components/flags/countrySelector'
import { FormElementPhoneNumberModel, PhoneNumberValue } from '@model/form/defines/FormPhoneNumber'
import FormInputElementBase from '@views/form/common/FormInputElementBase'
import { BrowserUtils } from '@commonUtils'
import FormElementLabel from "@views/form/components/FormElementLabel.vue";
import FormElementSupporting from "@views/form/components/FormElementSupporting.vue";
import FormElementError from "@views/form/components/FormElementError.vue";
import FormProtectedAlert from "@views/form/components/FormProtectedAlert.vue";
import { AsYouType } from '@vendor/libphonenumber-js/bundle/libphonenumber-mobile'
import FormAutoFillLabelPlacehoder from "@views/form/components/FormAutoFillLabelPlacehoder.vue";

export default defineComponent({
  name: 'FormPhoneNumberView',
  components: {
    FormAutoFillLabelPlacehoder,
    FormProtectedAlert,
    FormElementError,
    FormElementSupporting,
    FormElementLabel, FormUserFocusStatusMarker, CountrySelector },
  extends: FormInputElementBase,
  props: {
    element: {
      type: Object as PropType<FormElementPhoneNumberModel>,
      required: true,
      default: null
    }

  },
  data () {
    const modelValue =  { ...this.element.value } as PhoneNumberValue
    let formatedPhoneNumber = modelValue.phoneNumber
    if(modelValue.phoneNumber) {
       formatedPhoneNumber = this.getFormatedPhoneNumber(modelValue.phoneNumber, modelValue.countryCode)
    }
    return {
      focusedField: '',
      focusedValue: '',
      modelValue,
      formatedPhoneNumber,
      rawNumber:'',
      isMobileView: BrowserUtils.isMobile
    }
  },
  computed: {
    showCountryDisabledNormalStyle () {
      return false
    },
  },

  watch: {
    'element.value': function (val) {
      Object.keys(val).forEach(key => {
        if (this.focusedField === key) {
          // current user is focused this item, when value has changed we need ignore it.
          return
        }
        this.modelValue[key] = val[key]
      })
    }
  },
  methods: {
    handlePhoneInput(newVal) {
      this.formatedPhoneNumber =   this.getFormatedPhoneNumber(newVal, this.modelValue.countryCode)
      this.handleUserChange(null, 'phoneNumber')
      return this.formatedPhoneNumber
    },
    getFormatedPhoneNumber(phoneNumber: string, countryCode: string){
      const formatter =  new AsYouType(countryCode)
      const formatedNumber = formatter.input(phoneNumber)
      this.rawNumber = formatter.digits || phoneNumber
      return formatedNumber
    },
    reformatNumber() {
      const {countryCode, phoneNumber} = this.modelValue
      this.formatedPhoneNumber = this.getFormatedPhoneNumber(phoneNumber, countryCode)
    },
    handleCountryChange (event) {
      const val: FormElementEventParams = {
        element: this.element,
        currentValue: this.modelValue,
        enforce: true,
        changedProperty: 'countryCode',
        affectedProperties: ['number']
      }
      this.reformatNumber()
      this.$emit('change', val)
      this.$emit('blur', val)

    },
    handleUserChange (event, changedProperty) {
      this.$emit('change', this.getEventInfo(event, changedProperty))
    },
    handleUserBlur (event, changedProperty: string, enforce: boolean) {
      this.focusedField = ''
      this.focusedValue = ''
      this.$emit('blur', this.getEventInfo(event, changedProperty, enforce))

    },
    handleUserFocus (event, changedProperty: string) {
      this.focusedField = changedProperty
      this.focusedValue = this.modelValue[changedProperty]
      this.$emit('focus', this.getEventInfo(event, changedProperty))
    },
    getEventInfo (event, changedProperty, enforce = false) {
      const currentValue = {
        ...this.modelValue,
        phoneNumber: this.rawNumber
      }
      return {
        element: this.element,
        currentValue,
        enforce,
        changedProperty
      } as FormElementEventParams
    }
  }
})
</script>
<template>
  <div class="form-phone-field">
    <FormElementLabel :element="element"></FormElementLabel>

    <div class="flex-row">
        <div style="width: 120px">
          <FormUserFocusStatusMarker :highlight="highlight.countryCode">
            <FormAutoFillLabelPlacehoder
                layout="select"
                :text="$t('Assignees_Country_Code')"
                :tooltip="$t('auto_fill_country_code')"
                v-if="isShowAutoFillLabel"></FormAutoFillLabelPlacehoder>
            <country-selector
              class="country-selector"
              :country-code.sync="modelValue.countryCode"
              :class="{
                'disabled-with-normal-style': showCountryDisabledNormalStyle,
                'normal-text': !element.readonly && isPreviewTemplate
              }"
              :number.sync="modelValue.number"
              :is-edit="element.lockCountry || !!modelValue.countryCode"
              :disabled="isReadonly || element.lockCountry"
              :show-badge="false"
              :is-protected="element.isProtected && !runtimeOptions.showProtected"
              :hide-badge-tool-tip="isPreviewTemplate"
              :in-form="true"
              size="medium"
              @userChange="handleCountryChange"
              @focus="handleUserFocus($event, 'countryCode')" />
          </FormUserFocusStatusMarker>
        </div>
        <div style="flex-grow: 1">
          <FormUserFocusStatusMarker :highlight="highlight.phoneNumber">
            <FormAutoFillLabelPlacehoder
                :text="$t('Assignees_Phone_Number')"
                :tooltip="$t('auto_fill_phone_number')"
                v-if="isShowAutoFillLabel"></FormAutoFillLabelPlacehoder>
            <el-input
                ref="phoneInput"
                v-model="formatedPhoneNumber"
                @input="handlePhoneInput"
                :disabled="isReadonly"
                :readonly="isReadonly"
                :placeholder="element.placeholder"
                @focus="handleUserFocus($event, 'phoneNumber')"
                @blur="handleUserBlur($event, 'phoneNumber')"
            />
          </FormUserFocusStatusMarker>
        </div>
      </div>
    <FormElementError v-if="errorMessage" :error-message="errorMessage"></FormElementError>
    <FormElementSupporting v-if="element.supporting" :content="element.supporting"></FormElementSupporting>
    <FormProtectedAlert v-if="element.isProtected"></FormProtectedAlert>
  </div>
</template>

<style scoped lang="scss">

.form-phone-field {
  ::v-deep {
    .el-form-item__error {
      position: relative;
      padding: 0;
      margin-left: 1px;
      margin-top: -5px;
    }
  }
}

.flex-row {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  grid-gap: 10px;
}
</style>
