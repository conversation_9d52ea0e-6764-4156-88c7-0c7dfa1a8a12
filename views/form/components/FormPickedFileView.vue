<script lang="ts">
import {defineComponent} from '@vue/composition-api'
import type {PropType} from 'vue'
import utils from '@views/common/utils/utils'
import FileThumbnail from '@views/aggregate/components/previewImage.vue'
import {IFileBriefViewModel} from '@model/baseObjects/defines/fileViewModel';

export default defineComponent({
  name: 'FormPickedFileView',
  components: {FileThumbnail},
  props: {
    maxFileSize: {
      type: Number,
      default: 0
    },
    loading: {
      type: Boolean,
      default: false
    },
    file: {
      type: Object as PropType<IFileBriefViewModel>,
      required: true,
      default: null
    },
    isPreview: {
      type: Boolean,
      default: false
    },
    progressPercentage: {
      type: Number,
      default: 0
    },
    showProgress: {
      type: Boolean,
      default: false
    },
    allowDelete:{
      type: Boolean,
      default: false
    },
    progressColor: {
      type: String,
      default: '#1C72E3'
    }
  },
  emits: ['change'],
  computed: {

    fileType () {
      return this.file.fileType?.toLowerCase()
    },
    fileSize () {
      if (this.file.size) {
        const {number, unit} = utils.getSizeObj(this.file.size, true)
        return number + unit
      } else {
        return ''
      }
    },
    fileObj () {
      return this.file
    }
  },
  methods: {
    handleRemove () {
      this.$emit('remove', this.file)
    },
    handlePreviewFile () {
      if (this.isPreview) {
        this.$emit('previewFile', this.file)
      }
    }
  }
})
</script>

<template>
  <div class="form-file-view">
    <div
        class="grid-left mx-ellipsis"
        :class="{ 'mx-clickable': isPreview }"
        @click="handlePreviewFile"
    >
      <FileThumbnail class="file-image" :file-type="fileType" :source="file.thumbnail" />

      <div class="mx-ellipsis file-name-wrap">
        <div class="display-name mx-ellipsis">
          {{ file.name }}
        </div>
        <div class="file-size mx-color-secondary">
          {{ fileSize }}
        </div>
      </div>
    </div>
    <div class="grid-right">
      <div v-if="showProgress && loading">
        <el-progress
            class="progress-bar"
            type="circle"
            :show-text="false"
            :stroke-width="5"
            :percentage="progressPercentage"
            :color="progressColor"
            :width="20"
        />
      </div>
      <el-tooltip
          v-else
          placement="top-center"
          popper-class="overflow-control-tooltip"
          :disabled="loading"
      >
        <div slot="content">
          <div class="mx-text-c1 text-left">
            {{ $t('delete') }}
          </div>
        </div>
        <el-button
            v-if="!isPreview && allowDelete"
            type="gray"
            size="small"
            :loading="loading"
            :square="true"
            @click="handleRemove"
        >
          <i :class="['micon-delete', 'mx-color-danger']" />
        </el-button>
      </el-tooltip>
    </div>
  </div>
</template>

<style scoped lang="scss">
.form-file-view {
  display: flex;
  justify-content: space-between;
  border: 2px dashed #c6c6c6;
  height: 92px;
  align-items: center;
  padding: 20px;
  border-radius: 6px;
}

.grid-left {
  display: flex;
  flex-direction: row;
  align-items: center;
  grid-gap: 10px;

  .file-image {
    flex-shrink: 0;
  }

  .file-name-wrap {
    flex-grow: 1;
  }
}
</style>
