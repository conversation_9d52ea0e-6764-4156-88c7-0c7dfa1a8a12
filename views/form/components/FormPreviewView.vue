<script lang="ts">
import {defineComponent} from '@vue/composition-api'
import type {PropType} from 'vue'
import {FormRuntimeOptions, FormScenes} from '@model/form/defines/shared'
import {FormViewModel} from '@model/form/defines/formViewModel'
import PDFFormCanvasView from '@views/form/pdfForm/PDFFormCanvasView.vue'
import PDFFormElementView from '@views/form/pdfForm/PDFFormElementView.vue'
import FormCanvasView from '@views/form/components/FormCanvasView.vue'
import PageBackground from '@views/form/pdfForm/PageBackground.vue'
import {FormAnyElementViewModel, FormElementAnyInputModel} from '@model/form/defines/allType'
import FormFactoryManager from '@model/form/factory'
import {installFormComponents} from '@views/form/install'
import LibListHeader from '@views/contentLibrary/component/LibListHeader.vue'
import FormTheme from '@views/form/components/FormTheme.vue';
import FormElementHotspot from '@views/form/pdfForm/FormElementHotspot.vue';
import {BrowserUtils} from '@commonUtils';
import FillFormActionPanel from '@views/form/components/FillFormActionPanel.vue';
import FillFormActionPanelForMobile from '@views/form/components/FillFormActionPanelForMobile.vue';

/**
 * Display the Form pages area, including the background, form elements, and zoom panel.
 */
export default defineComponent({
  name: 'FormPreviewView',
  components: {
    FormElementHotspot,
    FormCanvasView,
    FillFormActionPanel,
    FillFormActionPanelForMobile,
    FormTheme,
    LibListHeader,
    PageBackground,
    PDFFormCanvasView,
    PDFFormElementView},
  props: {
    runtimeOptions: {
      type: Object as PropType<FormRuntimeOptions>,
      default: () => ({})
    },
    formViewModel: {
      type: Object as PropType<FormViewModel>,
      default: () => ({})
    },
    selectedElement: {
      type: Object as PropType<FormAnyElementViewModel>,
      default: () => ({})
    },
    defaultPageNumber: {
      type: Number,
      default: 1
    },
    enableFullScreen: {
      type: Boolean,
      default: false
    },
    selectable: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      currentPage: 0,
      selectedModelId: this.selectedElement?.id,
    }
  },
  computed: {
    currentPageLabel () {
      return this.currentPage + 1
    },
    elementSelectable () {
      if(!this.selectable){
        return false
      }
      return [FormScenes.CreateForm, FormScenes.EditForm].includes(this.runtimeOptions.scenes)
    },
    effectivePages () {
      return (this.formViewModel.pages || []).filter((page) => page.isVisible)
    },
    totalPages () {
      return this.effectivePages.length
    },

    pageElements (): FormAnyElementViewModel[] {
      return this.effectivePages[this.currentPage]?.elements || []
    },
    currentPageModel() {
      return this.effectivePages[this.currentPage]
    },
    elementWrapRenderComponent () {
      if (this.formViewModel.isPDFForm) {
        return 'PDFFormElementView'
      }
      return 'FormElementView'
    },
    formPageComponent(){
      if (this.formViewModel.isPDFForm) {
        return 'PDFFormCanvasView'
      }
      return 'FormCanvasView'
    },
    actionPanelComponent () {
      return (BrowserUtils.isMobile || BrowserUtils.isTablet) ? 'FillFormActionPanelForMobile' : 'FillFormActionPanel'
    },
  },

  created () {
    installFormComponents()
  },
  methods: {
    handlePageChange (page: number) {
      console.debug('this.currentPage ',this.currentPage ,page)
      this.currentPage = page
    },
    getElementRenderComponent (model: FormElementAnyInputModel) {
      const factory = FormFactoryManager.getFormElementFactory(model.type)
      return factory.uiOption(this.runtimeOptions).view
    },
    handleSelectElement (element: FormElementAnyInputModel) {

      this.$emit('select', element)
    },
    gotoFirstErrorPage(){
      for(let i = 0 ; i <= this.effectivePages.length; i++){
        const page = this.effectivePages[i]
        if(page.elements.some(element=>element.errors?.length)){
          this.currentPage = i
          return ;
        }
      }
    },
    handleAction(event){
      console.debug('handle action', event)
      this.$emit('action', event)
    }
  }
})
</script>

<template>
  <div class="form-page-wrapper">
    <component :is="formPageComponent"
               v-slot="viewInfo"
               :key="formViewModel.isPDFForm?'':currentPageModel.id"
               class="form-preview"
               :page="currentPageLabel"
               :pageModel="currentPageModel"
               :enable-pager="true"
               :enable-full-screen="enableFullScreen"
               :form-view-model="formViewModel"
               @pageChange="handlePageChange">
      <FormTheme
          :scale="viewInfo.scale"
          :runtime-options="runtimeOptions">
        <el-form>
          <component
              :is="elementWrapRenderComponent"
              v-for="element in pageElements"
              :key="element.id + 'wrap'"
              :selected="selectedElement.id === element.id"
              :element="element"
              :pageWidth="currentPageModel.width"
              :runtime-options="runtimeOptions"
              :selectable="elementSelectable"
              :scale="viewInfo.scale"
              @click="handleSelectElement">
            <component
                :is="getElementRenderComponent(element)"
                :key="element.id"
                :readonly="true"
                :element="element"
                :runtime-options="runtimeOptions"
                @action="handleAction"
                :scale="viewInfo.scale" />
          </component>
        </el-form>
      </FormTheme>
    </component>
    <div class="form-actions" v-if="!runtimeOptions.isPDFForm">
      <component
          :is="actionPanelComponent"
          :runtimeOptions="runtimeOptions"
          v-model="currentPage"
          :class="{'pdf-form-action': formViewModel.isPDFForm}"
          :total-page="totalPages"
          @gotoPage="handlePageChange" />
    </div>
  </div>
</template>

<style scoped lang="scss">
.form-preview {
  height: 100%;
  width: 100%;
}
.form-page-wrapper{
  display: flex;
  justify-content: center;
  background: #f4f4f4;
  height: 100%;
  flex-direction: column;.form-actions {
  width: 100%;
  margin: auto;
  height: 92px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
}

</style>
