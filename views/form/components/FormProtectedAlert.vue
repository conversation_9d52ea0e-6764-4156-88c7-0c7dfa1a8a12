<script setup lang="ts">
export default {
  name: 'FormProtectedAlert',
}
</script>

<template>
  <div class="procted-tip mx-text-c3">
    <i class="micon-mep-warning" />
    {{$t('protected_field_info_tip')}}
  </div>
</template>

<style scoped lang="scss">
.procted-tip {
  display: flex;
  flex-direction: row;
  padding: 8px 16px 8px 8px;
  gap: 10px;
  min-height: 32px;
  background: rgba(189, 64, 0, 0.1);
  border: 1px solid #bd4000;
  box-shadow: 0px 0.3px 0.8px rgb(0 0 0 / 2%), 0px 0.9px 2.7px rgb(0 0 0 / 3%),
  0px 1px 5px rgb(0 0 0 / 5%);
  border-radius: 6px;
  align-items: center;
  margin-top: 8px;

  .micon-mep-warning {
    font-size: 14px;
    color: #bd4000;
    margin-top: 2px;
  }
}
</style>