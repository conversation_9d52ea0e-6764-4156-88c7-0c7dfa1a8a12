<script lang="ts">
import type { PropType } from 'vue'
import { defineComponent } from '@vue/composition-api'
import FormUserFocusStatusMarker from './FormUserFocusStatusMarker.vue'
import { FormElementEventParams } from '../common/types'
import { DefaultHighlightFieldName } from '../common/highlightProps'
import FormInputElementBase from '../common/FormInputElementBase'
import { FormElementSignatureModel } from '@model/form/defines/FormSignature'
import {
  makeTransactionResourceUrl,
  removeTransactionResource,
  uploadSignatureToTransaction
} from '@controller/contentLibrary/src/form'
import SignSignatureDialog from '@views/contentLibrary/plugins/form/common/SignSignatureDialog.vue'
import { popupFactory } from '@views/common/useComponent'
import { FormRuntimeOptions } from '@model/form/defines/shared'
import FormElementError from "@views/form/components/FormElementError.vue";
import FormElementLabel from "@views/form/components/FormElementLabel.vue";

export default defineComponent({
  name: 'FormSignatureView',
  components: {FormElementLabel, FormElementError, FormUserFocusStatusMarker },
  extends: FormInputElementBase,
  props: {
    element: {
      type: Object as PropType<FormElementSignatureModel>,
      required: true,
      default: null
    }
  },
  data () {
    return {
      modelValue: this.element.value,
      DefaultHighlightFieldName,
      loading: false
    }
  },

  watch: {
    'element.value': function (val) {
      this.modelValue = val
    }
  },
  methods: {
    handleUserFocus () {
      this.userIsFocused = true
      this.$emit('focus', this.getEventInfo())
    },
    getEventInfo () {
      return {
        element: this.element,
        currentValue: this.modelValue,
        changedProperty: ''
      } as FormElementEventParams
    },
    removeSignature () {
      this.handleUserFocus()
      this.$emit('blur', {
        element: this.element,
        currentValue: this.currentValue,
        removeFile: true,
        changedProperty: ''
      }, (result: Promise<void>) => {
        result.catch(error => {
          this.loading = false
          this.$mxMessage.error(this.$t('unable_to_do_action_try_again', {
            action: this.$t('remove')
          }))
        })
      })
    },
    handleSign () {
      const setSignature = popupFactory(SignSignatureDialog)
      this.handleUserFocus()
      const vm = this
      const [showSetSignature] = setSignature({
        success (signatureURL) {
          vm.$emit('blur', {
            element: vm.element,
            currentValue: vm.currentValue,
            uploadFile: {
              url: signatureURL
            },
            changedProperty: ''
          }, (result: Promise<void>) => {
            result.catch(error => {
              vm.$mxMessage.error(vm.$t('unable_to_do_action_try_again', {
                action: vm.$t('save')
              }))
            })
          })
        }
      })
      showSetSignature()
    }
  }
})
</script>

<template>
  <div class="form-signature-field">
    <FormElementLabel :element="element"></FormElementLabel>

    <FormUserFocusStatusMarker :highlight="highlight[DefaultHighlightFieldName]">
        <div class="form-img">
          <div
            v-if="element.imageURL"
            class="signature-placeholder field-border user-signature-view">
            <img :src="element.imageURL">
            <button
              v-loading="loading"
              class="btn-icon"
              v-if="!isPreview"
              @click="removeSignature">
              <svg
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg">
                <path
                  fill-rule="evenodd"
                  clip-rule="evenodd"
                  d="M13.4143 12.0001L14.8285 10.5859C15.2191 10.1954 15.2191 9.56222 14.8285 9.17169C14.438 8.78117 13.8049 8.78117 13.4143 9.17169L12.0001 10.5859L10.5859 9.17169C10.1954 8.78117 9.56222 8.78117 9.17169 9.17169C8.78117 9.56222 8.78117 10.1954 9.17169 10.5859L10.5859 12.0001L9.17169 13.4143C8.78117 13.8049 8.78117 14.438 9.17169 14.8285C9.56222 15.2191 10.1954 15.2191 10.5859 14.8285L12.0001 13.4143L13.4143 14.8285C13.8049 15.2191 14.438 15.2191 14.8285 14.8285C15.2191 14.438 15.2191 13.8049 14.8285 13.4143L13.4143 12.0001ZM4.92908 4.92908C8.83409 1.02408 15.1654 1.0232 19.0712 4.92908C22.9763 8.83409 22.9763 15.1662 19.0712 19.0712C15.1662 22.9763 8.83409 22.9763 4.92908 19.0712C1.0232 15.1654 1.02408 8.83409 4.92908 4.92908Z"
                  fill="#616161" />
              </svg>
            </button>
          </div>
          <div
            v-else
            class="field-border"
            :class="{ 'signature-placeholder': true }">
            <div>
              <el-button
                :branding-text="true"
                type="text"
                :disabled="isPreview"
                icon="micon-form-signature"
                @click="handleSign">
                {{ isPreview? $t('Signature') : $t('Enter_your_signature') }}
              </el-button>
            </div>
          </div>
        </div>
      </FormUserFocusStatusMarker>
    <FormElementError v-if="errorMessage" :error-message="errorMessage"></FormElementError>

  </div>
</template>

<style scoped lang="scss">
.signature-readonly-mask {
  position: absolute;
  width: 280px;
  height: 70px;
  top: 0;
  left: 14px;
  z-index: 20;
}

.signature-placeholder.user-signature-view {
  justify-content: space-between;

  button {
    border: none;
    background: transparent;
    height: 24px;
  }

  img {
    margin: 10px;
    max-height: 60px;
    max-width: 90%;
  }
}

.signature-placeholder {
  border-radius: 6px;
  border: 1px solid #c6c6c6;
  background: #fff;
  color: #616161;
  display: flex;
  width: 100%;
  height: 80px;
  justify-content: center;
  align-items: center;

  .el-button {
    margin-bottom: 0 !important;
  }

  ::v-deep {
    i {
      font-size: 24px;
    }
  }
}

.form-img {
  width: 100%;
}

.form-signature-field {
  padding: 0 !important;

  ::v-deep {
    .el-form-item__error {
      position: relative;
      padding: 0;
      margin-left: 1px;
      margin-top: -2px;
    }
  }
}

.form-signature-field.form-field {
  flex-direction: column;
  align-items: flex-start;
}
</style>
