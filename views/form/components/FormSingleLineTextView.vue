<script lang="ts">
import MxDDRInput from '@views/ddr/MxDDRInput'
import type {PropType} from 'vue'
import {defineComponent} from '@vue/composition-api'
import {FormElementSingleLineTextModel} from '@model/form/defines/FormSingleLineText'
import {FormElementEventParams} from '../common/types'
import {DefaultHighlightFieldName} from '../common/highlightProps'
import FormInputElementBase from '@views/form/common/FormInputElementBase'
import FormElementLabel from '@views/form/components/FormElementLabel.vue';
import FormElementSupporting from '@views/form/components/FormElementSupporting.vue';
import FormUserFocusStatusMarker from '@views/form/components/FormUserFocusStatusMarker.vue';
import FormElementError from '@views/form/components/FormElementError.vue';
import FormProtectedAlert from '@views/form/components/FormProtectedAlert.vue';
import {getLengthLimitMessage} from "@views/form/common/lengthLimit";

export default defineComponent({
  name: 'FormSingleLineTextView',
  components: {
    FormProtectedAlert,
    FormElementError, FormUserFocusStatusMarker, FormElementSupporting, FormElementLabel, MxDDRInput},
  extends: FormInputElementBase,
  props: {
    element: {
      type: Object as PropType<FormElementSingleLineTextModel>,
      required: true,
      default: null
    },
    scale: {
      type: Number,
      default: 1
    }
  },
  data() {
    return {
      modelValue: this.element.value,
      DefaultHighlightFieldName,
      userIsFocused: false
    }
  },
  computed: {
    lengthLimitMessage () {
      return getLengthLimitMessage(this.element.type, this.element.minLength, this.element.maxLength, this.$t)
    }
  },
  watch: {
    'element.value': function (val) {
      if (this.userIsFocused) {
        // current user is focused this item, when value has changed we need ignore it.
        return
      }
      this.modelValue = val
    }
  },
  methods: {
    handleUserChange() {
      this.$emit('change', this.getEventInfo())
    },
    handleUserBlur() {
      this.userIsFocused = false
      this.$emit('blur', this.getEventInfo())
    },
    handleUserFocus() {
      this.userIsFocused = true
      this.$emit('focus', this.getEventInfo())
    },
    getEventInfo() {
      return {
        element: this.element,
        currentValue: this.modelValue,
        changedProperty: ''
      } as FormElementEventParams
    }
  }
})
</script>

<template>
  <div class="form-singleline-field" :class="{'has-error': !!errorMessage}">
    <FormElementLabel :element="element"></FormElementLabel>
    <div
        v-if="isPreviewCompleted"
        class="preview-content">
      {{ isNeedShowMask ? maskedValue : modelValue }}
    </div>
    <template v-else>
      <FormUserFocusStatusMarker :highlight="highlight[DefaultHighlightFieldName]">
        <component
            :is="inputComponent"
            v-bind="getInputOptions({maxLength: 1000})"
            v-model="modelValue"
            :placeholder="placeholder"
            autocomplete="off"
            @input="handleUserChange"
            @focus="handleUserFocus"
            @blur="handleUserBlur"/>
      </FormUserFocusStatusMarker>
    </template>
    <FormElementError v-if="errorMessage" :error-message="errorMessage"></FormElementError>
    <FormElementSupporting v-if="element.supporting" :content="element.supporting"></FormElementSupporting>
    <FormElementSupporting v-if="lengthLimitMessage" :content="lengthLimitMessage"></FormElementSupporting>
    <FormProtectedAlert v-if="element.isProtected"></FormProtectedAlert>
  </div>
</template>
<style scoped lang="scss">
.form-singleline-field {
  input {
    border: 1px solid #8a8a8a;
    border-radius: 6px;
  }



  .preview-content {
    height: 36px;
    overflow-y: hidden;
    border: 1px solid #8a8a8a;
    border-radius: 5px;
    padding: 5px 8px;
    word-wrap: break-word;
    cursor: not-allowed;
    line-height: 24px;
  }
}
.has-error {
  ::v-deep {
    .mx-ddr-input,.el-input__inner {
      border: 1px solid #B22424!important;
    }
  }
}

</style>
