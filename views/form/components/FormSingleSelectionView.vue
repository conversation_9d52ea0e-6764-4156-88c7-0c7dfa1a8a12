<script lang="ts">
import type { PropType } from 'vue'
import { defineComponent } from '@vue/composition-api'
import { FormElementEventParams } from '../common/types'
import { DefaultHighlightFieldName } from '../common/highlightProps'
import { FormElementSingleSelectionModel } from '@model/form/defines/FormSingleSelection'
import FormInputElementBase from '@views/form/common/FormInputElementBase'
import FormElementSupporting from '@views/form/components/FormElementSupporting.vue'
import FormElementError from '@views/form/components/FormElementError.vue'
import FormElementLabel from '@views/form/components/FormElementLabel.vue'
import FormUserFocusStatusMarker from '@views/form/components/FormUserFocusStatusMarker.vue'

export default defineComponent({
  name: 'FormSingleSelectionView',

  components: { FormUserFocusStatusMarker, FormElementSupporting, FormElementError, FormElementLabel },
  extends: FormInputElementBase,
  props: {
    element: {
      type: Object as PropType<FormElementSingleSelectionModel>,
      required: true,
      default: null
    }

  },
  data () {
    return {
      modelValue: this.element.value,
      DefaultHighlightFieldName
    }
  },

  watch: {
    'element.value': function(val) {
      console.debug('element.value change', val)
      this.modelValue = val
    }
  },
  methods: {
    handleUserChange () {
      this.$emit('change', this.getEventInfo())
      this.$emit('blur', this.getEventInfo())
    },
    getEventInfo () {
      return {
        element: this.element,
        currentValue: this.modelValue,
        changedProperty: '',
        enforce: true,
        withFillStatus: true
      } as FormElementEventParams
    }
  }
})
</script>

<template>
  <div class="form-single-selection-field" :class="{ 'has-error': !!errorMessage }">
    <FormElementLabel :element="element" />
    <FormElementSupporting
      v-if="element.supporting"
      :content="element.supporting"
    ></FormElementSupporting>
    <FormUserFocusStatusMarker
      :highlight="highlight[DefaultHighlightFieldName]"
      :show-border="true"
    >
      <el-radio-group
        v-model="modelValue"
        :class="{ vertical: true, 'is-error': errorMessage }"
        :disabled="isReadonly"
        @change="handleUserChange"
      >
        <div
          v-for="(item, index) in element.options"
          :key="index"
          class="mx-flex-container align-item-center"
        >
          <el-radio class="radio-item" :name="element.id" :label="item.value">
            <span>{{ item.label }}</span>
          </el-radio>
        </div>
      </el-radio-group>
    </FormUserFocusStatusMarker>
    <FormElementError v-if="errorMessage" :error-message="errorMessage"></FormElementError>
  </div>
</template>
<style scoped lang="scss">
.form-single-selection-field {
  ::v-deep {
    .el-radio {
      &.is-checked {
        .el-radio__inner {
          border-color: var(--duo-primary-color);

          .el-radio__checked {
            background-color: var(--duo-primary-color);
          }
        }
      }
    }
  }


  .preview-content {
    height: 36px;
    overflow-y: auto;
    border: 1px solid #8a8a8a;
    border-radius: 5px;
    padding: 5px 8px;
    word-wrap: break-word;
    cursor: not-allowed;
    line-height: 24px;
  }
}

.has-error {
  ::v-deep {
    .el-radio__inner {
      border-color: #b22424 !important;
    }
  }
}
</style>
