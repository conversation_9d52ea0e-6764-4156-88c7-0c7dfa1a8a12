<script lang="ts">
import {defineComponent} from '@vue/composition-api'
import type {PropType} from 'vue';
import {FormRuntimeOptions, FormScenes} from '@model/form/defines/shared';

export default defineComponent({
  props: {
    runtimeOptions: {
      type: Object as PropType<FormRuntimeOptions>,
      default: () => ({})
    },
    scale: {
      type: Number,
      default: 1
    },
  },
  computed: {
    themeStyles () {
      if (this.runtimeOptions.isCompleted) {
        return this.previewCompletedThemes
      }
      return this.fillThemes
    },
    fillThemes () {
      const defaultFontSize = 10 * this.scale
      return {
        '--pdf-element-color-bg': 'rgb(7, 71, 154, 0.1)',
        '--pdf-element-color-bg-required': 'rgb(7, 71, 154, 0.24)',
        '--pdf-element-color-bg-focused': '#fff',
        '--pdf-element-color-border-focused': '#1A69D1',
        '--pdf-element-color-placeholder': '#616161',
        '--pdf-element-color-selected': 'rgba(57, 108, 174, 1)',
        '--pdf-element-color-selected-bg': 'rgba(195, 211, 231, 1)',
        '--pdf-element-color-error': '#B22424',
        '--pdf-element-border-radius': `${this.scale * 2}px`,
        '--pdf-element-border-size': `${this.scale}px`,
        '--pdf-form-input-font-size': `${defaultFontSize}px`,
        '--pdf-form-common-font-size': `${defaultFontSize}px`,
        '--pdf-scale': `${this.scale}`,
      }
    },
    previewCompletedThemes () {
      return {
        ...this.fillThemes,
        '--pdf-element-color-bg': 'transparent',
        '--pdf-element-color-bg-required': 'transparent'
      }
    }
  }
})
</script>
<template>
  <div class="form-theme" :style="themeStyles">
    <slot />
  </div>
</template>
