<script lang="ts">
import type {PropType} from 'vue'
import {FieldHighlightProps} from '../common/types'
import {defineComponent} from '@vue/composition-api'

export default defineComponent({
  name: 'FormUserFocusStatusMarker',
  props: {
    highlight: {
      type: Object as PropType<FieldHighlightProps>,
      default: null
    },
    /**
     * multiple selection not border, we need mock a border for this case
     */
    showBorder: {
      type: Boolean,
      default: false
    },
  },
  computed: {
    needShowLabel () {
      if (!this.highlightLabels?.length) {
        return false
      }
      return this.highlightLabels.length > 0
    },
    highlightLabels () {
      return this.highlight?.highlightLabels || []
    },
    highlightBorder () {
      return this.highlight?.highlightBorder || false
    },
    highlightColor () {
      return this.highlight?.highlightColor || ''
    }
  }
})
</script>
<template>
  <div class="online-marker"
       :class="{'has-user-focus':needShowLabel,
       'border-color-with-user': highlightBorder,
       'show-border': showBorder}"
        :style="{'--user-online-color': highlightColor}">
    <div class="online-user-name mx-ellipsis">
    <span class="mx-ellipsis" v-for="(name, index) in highlightLabels">
      {{ index > 0? ',': '' }}
      {{name}}
    </span>
    </div>
    <slot ></slot>
  </div>
</template>



<style scoped lang="scss">
.online-marker{
  position: relative;
  width:100%;
  .online-user-name{
    background-color:   var(--user-online-color);
    display: flex;
    border-top-right-radius: 2px;
    border-top-left-radius: 2px;
    position: absolute;
    margin-top: -16px;
    right: 4px;
    padding: 0 5px;
    height: 16px;
    font-size: 11px;
    color: #fff;
    max-width: 300px;
    visibility: hidden;
    .mx-ellipsis{
      max-width: 80px;
    }
  }
  &.show-border{
    border: 2px solid transparent;
    border-radius: 4px;
    .online-user-name{
      margin-top: -18px;
    }
  }
}

.has-user-focus *:focus ,.has-user-focus:hover, .bw-mobile .has-user-focus,.bw-tablet .has-user-focus{
  .online-user-name{
    visibility: visible;
  }
}
.border-color-with-user{
  ::v-deep {
    input ,.field-border, textarea{
      border-color: var(--user-online-color) !important;
      border-width: 2px;
    }
  }
  &.show-border {
    border-color: var(--user-online-color) !important;
  }
}
</style>
