<script lang="ts">
import type { PropType } from 'vue'
import { defineComponent } from '@vue/composition-api'
import { FormElementUserNameModel } from '@model/form/defines/FormUserName'
import FormUserFocusStatusMarker from './FormUserFocusStatusMarker.vue'
import { nameMaxLength } from '@views/common/appConst.js'
import { FormElementEventParams } from '../common/types'
import FormInputElementBase from '@views/form/common/FormInputElementBase'
import FormElementLabel from "@views/form/components/FormElementLabel.vue";
import FormAutoFillLabelPlacehoder from "@views/form/components/FormAutoFillLabelPlacehoder.vue";

export default defineComponent({
  name: 'FormUserNameView',
  components: {FormAutoFillLabelPlacehoder, FormElementLabel, FormUserFocusStatusMarker },
  extends: FormInputElementBase,
  props: {
    element: {
      type: Object as PropType<FormElementUserNameModel>,
      required: true,
      default: null
    }
  },
  data () {
    return {
      focusedField: '',
      modelValue: { ...this.element.value },
      nameMaxLength
    }
  },
  watch: {
    'element.value': function (val) {
      Object.keys(val).forEach(key => {
        if (this.focusedField === key) {
          // current user is focused this item, when value has changed we need ignore it.
          return
        }
        this.modelValue[key] = val[key]
      })
    }
  },
  computed: {

  },
  methods: {
    handleUserChange (event, changedProperty: string,enforce: boolean) {
      this.$emit('change', this.getEventInfo(event, changedProperty,enforce))
      if(enforce){
        this.handleUserBlur(event, changedProperty,enforce)
      }
    },
    handleUserBlur (event, changedProperty: string, enforce: boolean) {
      this.focusedField = ''
      this.$emit('blur', this.getEventInfo(event, changedProperty, enforce))
    },
    handleUserFocus (event, changedProperty: string) {
      this.focusedField = changedProperty
      this.$emit('focus', this.getEventInfo(event, changedProperty))
    },
    getEventInfo (event, changedProperty: string, enforce = false) {
      return {
        element: this.element,
        currentValue: this.modelValue,
        changedProperty,
        enforce
      } as FormElementEventParams
    }
  }
})
</script>

<template>
  <div class="form-user-name-field">
    <FormElementLabel :element="element"></FormElementLabel>
    <div
      width="100%"
      class="element-row">
      <div
        v-if="element.showPrefix"
        style="max-width: 80px;min-width: 60px"
        class="element-item">
        <div class="sub-label mx-text-c1">
          {{ $t('prefix') }}
        </div>
        <FormUserFocusStatusMarker :highlight="highlight.prefix">
          <el-select
            v-model="modelValue.prefix"
            class="prefix-dropdown-menu"
            :disabled="isDisabled"
            :readonly="isReadonly"
            :placeholder="isPreviewTemplate ? $t('select') : ''"
            @change="handleUserChange($event, 'prefix', true)"
            @focus="handleUserFocus($event, 'prefix')"
            >
            <el-option
              v-for="(item, index) in element.prefixOptions"
              :key="index"
              :label="item"
              :value="item" />
          </el-select>
        </FormUserFocusStatusMarker>
      </div>
      <div class="element-item">
        <div class="sub-label mx-text-c1">
          {{ $t('first_name') }}
        </div>
        <el-form-item :error="errors.firstName">
          <FormUserFocusStatusMarker :highlight="highlight.firstName">
            <FormAutoFillLabelPlacehoder
                :text="$t('Assignees_First_Name')"
                :tooltip="$t('auto_fill_first_name')"
                v-if="isShowAutoFillLabel"></FormAutoFillLabelPlacehoder>
            <component :is="inputComponent"
                       v-bind="getInputOptions({maxLength: nameMaxLength})"
                       v-model="modelValue.firstName"
                       type="text"
              @input="handleUserChange($event, 'firstName')"
              @focus="handleUserFocus($event, 'firstName')"
              @blur="handleUserBlur($event, 'firstName')" />
          </FormUserFocusStatusMarker>
        </el-form-item>
      </div>
      <div
        v-if="element.showMiddleName"
        class="element-item">
        <div class="sub-label mx-text-c1">
          {{ $t('middle_name') }}
        </div>
        <FormUserFocusStatusMarker :highlight="highlight.middleName">
          <ElInput
            v-model="modelValue.middleName"
            :trim="true"
            :disabled="isDisabled"
            :readonly="isReadonly"
            @change="handleUserChange($event, 'middleName')"
            @focus="handleUserFocus($event, 'middleName')"
            @blur="handleUserBlur($event, 'middleName')" />
        </FormUserFocusStatusMarker>
      </div>
      <div class="element-item">
        <div class="sub-label mx-text-c1">
          {{ $t('last_name') }}
        </div>
        <el-form-item :error="errors.lastName">
          <FormUserFocusStatusMarker :highlight="highlight.lastName">
            <FormAutoFillLabelPlacehoder
                :text="$t('Assignees_Last_Name')"
                :tooltip="$t('auto_fill_last_name')"
                v-if="isShowAutoFillLabel"></FormAutoFillLabelPlacehoder>
            <component
                :is="inputComponent"
              v-model="modelValue.lastName"
                v-bind="getInputOptions({maxLength: nameMaxLength})"
               type="text"
              @input="handleUserChange($event, 'lastName')"
              @focus="handleUserFocus($event, 'lastName')"
              @blur="handleUserBlur($event, 'lastName')" />
          </FormUserFocusStatusMarker>
        </el-form-item>
      </div>
      <div
        v-if="element.showSuffix"
        class="element-item"
        style="max-width: 86px">
        <div class="sub-label mx-text-c1">
          {{ $t('suffix') }}
        </div>
        <FormUserFocusStatusMarker :highlight="highlight.suffix">
          <ElInput
            v-model="modelValue.suffix"
            :trim="true"
            :disabled="isDisabled"
            :readonly="isReadonly"
            @change="handleUserChange($event, 'suffix')"
            @focus="handleUserFocus($event, 'suffix')"
            @blur="handleUserBlur($event, 'suffix')" />
        </FormUserFocusStatusMarker>
      </div>
    </div>
  </div>
</template>
<style scoped lang="scss">
.form-user-name-field {
  margin-bottom: 6px;

  .prefix-dropdown-menu {
    width: 100%;
  }

  .sub-label {
    height: 20px;
    color: #616161;
  }

  .mobile-table {
    td {
      padding-bottom: 10px;
    }
  }

  ::v-deep {
    .normal-text {
      color: #000 !important;

      input {
        color: #000 !important;
      }
    }

    .disabled-with-normal-style {
      .el-input__inner {
        box-shadow: none !important;
        border-color: #8a8a8a !important;
        color: #000 !important;
        background-color: white !important;
      }

      * {
        cursor: not-allowed !important;
      }
    }
  }
}

.element-row {
  display: flex;
  align-items: flex-start;
  .element-item {
    flex: 1;
  }

  .element-item + .element-item {
    margin-left: 8px;
  }
}
</style>
