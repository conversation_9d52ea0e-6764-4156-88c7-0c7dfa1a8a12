import FormElementView from './components/FormElementView.vue'
import PDFFormElementView from './pdfForm/PDFFormElementView.vue'
import FormHeadingView from './components/FormHeadingView.vue'
import FormImageView from './components/FormImageView.vue'
import FormSingleLineTextView from './components/FormSingleLineTextView.vue'
import PDFFormSingleLineTextView from './pdfForm/PDFFormSingleLineTextView.vue'
import PDFFormMultiLineTextView from './pdfForm/PDFFormMultiLineTextView.vue'
import FormUserNameView from './components/FormUserNameView.vue'
import FormMultiLineTextView from './components/FormMultiLineTextView.vue'
import FormSingleSelectionView from './components/FormSingleSelectionView.vue'
import FormMultiSelectionView from './components/FormMultiSelectionView.vue'
import FormDropdownListView from './components/FormDropdownListView.vue'
import FormAddressView from './components/FormAddressView.vue'
import FormEmailAddressView from './components/FormEmailAddressView.vue'
import FormPhoneNumberView from './components/FormPhoneNumberView.vue'
import FormDateView from './components/FormDateView.vue'
import FormSignatureView from './components/FormSignatureView.vue'
import FormCurrencyView from './components/FormCurrencyView.vue'
import FormNumberView from './components/FormNumberView.vue'
import FormLineSeparatorView from './components/FormLineSeparatorView.vue'
import FormParagraphView from './components/FormParagraphView.vue'
import FormFileUploadView from './components/FormFileUploadView.vue'
import PDFFormSingleSelectionView from '@views/form/pdfForm/PDFFormSingleSelectionView.vue'
import PDFFormMultiSelectionView from '@views/form/pdfForm/PDFFormMultiSelectionView.vue'
import PDFFormDateView from '@views/form/pdfForm/PDFFormDateView.vue'
import PDFFormSignatureView from '@views/form/pdfForm/PDFFormSignatureView.vue'
import PDFFormDropdownListView from '@views/form/pdfForm/PDFFormDropdownListView.vue'

import Vue from 'vue'

let componentInited = false

export function installFormComponents () {
  if (componentInited) {
    return
  }
  Vue.component(PDFFormElementView.name, PDFFormElementView)
  Vue.component(FormHeadingView.name, FormHeadingView)
  Vue.component(FormImageView.name, FormImageView)
  Vue.component(FormSingleLineTextView.name, FormSingleLineTextView)
  Vue.component(FormUserNameView.name, FormUserNameView)
  Vue.component(FormMultiLineTextView.name, FormMultiLineTextView)
  Vue.component(FormSingleSelectionView.name, FormSingleSelectionView)
  Vue.component(FormMultiSelectionView.name, FormMultiSelectionView)
  Vue.component(FormDropdownListView.name, FormDropdownListView)
  Vue.component(FormAddressView.name, FormAddressView)
  Vue.component(FormEmailAddressView.name, FormEmailAddressView)
  Vue.component(FormPhoneNumberView.name, FormPhoneNumberView)
  Vue.component(FormDateView.name, FormDateView)
  Vue.component(FormSignatureView.name, FormSignatureView)
  Vue.component(FormCurrencyView.name, FormCurrencyView)
  Vue.component(FormNumberView.name, FormNumberView)
  Vue.component(FormElementView.name, FormElementView)

  Vue.component(FormLineSeparatorView.name, FormLineSeparatorView)
  Vue.component(FormParagraphView.name, FormParagraphView)
  Vue.component(FormFileUploadView.name, FormFileUploadView)
  Vue.component(PDFFormSingleLineTextView.name, PDFFormSingleLineTextView)
  Vue.component(PDFFormMultiLineTextView.name, PDFFormMultiLineTextView)
  Vue.component(PDFFormSingleSelectionView.name, PDFFormSingleSelectionView)
  Vue.component(PDFFormMultiSelectionView.name, PDFFormMultiSelectionView)
  Vue.component(PDFFormDateView.name, PDFFormDateView)
  Vue.component(PDFFormSignatureView.name, PDFFormSignatureView)
  Vue.component(PDFFormDropdownListView.name, PDFFormDropdownListView)
  componentInited = true
}
