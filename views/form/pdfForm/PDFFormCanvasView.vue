<script lang="ts">
import {defineComponent} from '@vue/composition-api'
import type {PropType} from 'vue'
import {FormViewModel} from '@model/form/defines/formViewModel'
import PageBackground from '@views/form/pdfForm/PageBackground.vue'
import FormCanvasFloatBar from '@views/form/components/FormCanvasFloatBar.vue'
import {FormRuntimeOptions} from '@model/form/defines/shared';
import {isNumber} from 'lodash';
import moment from 'moment-timezone';
import {BrowserUtils} from '@commonUtils';

export default defineComponent({
  name: 'PDFFormCanvasView',
  components: { PageBackground, FormCanvasFloatBar},
  props: {
    /**
     * The default display scale.
     */
    defaultScale: {
      type: String,
      default: 'fit'
    },
    scaleable: {
      type: Boolean,
      default: false
    },
    runtimeOptions: {
      type: Object as PropType<FormRuntimeOptions>,
      default: () => ({})
    },
    formViewModel: {
      type: Object as PropType<FormViewModel>,
      default: () => ({})
    },
    enableFullScreen: {
      type: Boolean,
      default: false
    },
    enablePager: {
      type: Boolean,
      default: false
    },
    page: {
      type: Number,
      default: 0
    },
    keepShowFooterBar: {
      type: Boolean,
      default: false
    }
  },
  data () {
    const scale = BrowserUtils.isMobile ? 2 : 3
    return {
      /**
       * To achieve better clarity when zoomed in,
       * the background and elements are rendered at a default zoom level of 3 to 4 times.
       * Then, the zoom is scaled down using zoomScale to fit the screen size.
       */
      scale,
      displayScale: this.defaultScale,
      /**
       * The zoom ratio expected by the user.
       */
      zoomScale: 1,
      layoutReady: false,
      currentPage: this.page,
      containerHeight: 0,
      containerWidth: 0,
      initialized: false,
      canOperate: true,
    }
  },
  computed: {

    scaledRatio () {
      return this.zoomScale / this.scale
    },
    pageModel () {
      return this.formViewModel.pages[this.currentPageIndex]
    },
    currentPageIndex () {
      return this.currentPage - 1
    },
    canvasWrapStyle () {
      const height = (this.pageHeight * this.zoomScale)
      let marginTop = 'auto'
      if (height < this.containerHeight) {
        marginTop = (this.containerHeight - height - 20) / 2 + 'px'
      }
      return {
        width: (this.pageModel.width * this.zoomScale) + 'px',
        height: height + 'px',
        marginTop,
        '--pdf-form-item-border': this.scale + 'px',
        '--pdf-form-item-border-radius': this.scale + 'px'
      }
    },
    canvasStyles () {
      return {
        'transform-origin': 'top left',
        transition: 'none',
        transform: `scale(${this.scaledRatio})`
      }
    },
    backgroundImgStyle () {
      return {
        width: this.pageModel.width + 'px',
        height: this.pageModel.height + 'px'
      }
    },
    totalPage () {
      return this.formViewModel.pages.length
    },
    showPrevButton () {
      return this.currentPage !== 1
    },
    showNextButton () {
      return this.currentPage !== this.totalPage
    },
    footerStyle () {
      const width = this.pageModel.width * this.scale
      const height = this.pageModel.height * this.scale
      return {
        top: height + 'px',
        width: width + 'px',
        maxHeight: '30px',
        fontSize: 12 * this.scale + 'px',
      }
    },
    pageHeight () {
      let height = this.pageModel.height
      if (this.formViewModel.isCompleted) {
        height += 20
      }
      return height
    }
  },
  watch: {
    page (page: number) {
      this.handleChangePage(page)
    }
  },
  mounted () {
    this.calculateDisplayInfo()
    this.layoutReady = true
    if (!BrowserUtils.isMobile) {
      setTimeout(() => {
        this.bindResizeEvents();
      }, 1000)
    }
  },
  beforeDestroy () {
    this.unbindResizeEvents()
  },
  methods: {
    calculateDisplayInfo () {
      this.containerHeight = this.$refs.scrollContainer.offsetHeight
      this.containerWidth = this.$refs.scrollContainer.offsetWidth
      this.zoomScale = this.getRealScale(this.displayScale)
    },
    bindResizeEvents () {
      const resizeObserver = new ResizeObserver(() => {
        this.calculateDisplayInfo()

      });
      resizeObserver.observe(document.body);
      this.resizeObserver = resizeObserver;
    },
    unbindResizeEvents () {
      if (this.resizeObserver) {
        this.resizeObserver.unobserve(document.body);
        this.resizeObserver = null
      }
    },
    getRealScale (scale: number | 'fit') {
      let newScale = 1
      if (isNumber(scale)) {
        newScale = scale
      }
      if (scale === 'fit') {
        newScale = parseFloat(((this.containerHeight - 10) / this.pageHeight))
        if (this.pageModel.width * newScale >= this.containerWidth) {
          newScale = parseFloat(((this.containerWidth - 10) / this.pageModel.width))
        }
      }
      if (newScale < 0.1) {
        newScale = 0.1
      }

      return Math.floor(newScale * 100) / 100
    },
    handleChangePage (page: number) {
      this.currentPage = page
      this.initialized = false
      this.$emit('pageChange', page)
    },
    handleZoomChange (scale: number) {
      this.displayScale = scale
      this.zoomScale = scale
    },
    gotoPrevPage () {
      this.handleChangePage(this.currentPage - 1)
    },
    gotoNextPage () {
      this.handleChangePage(this.currentPage + 1)
    },
    triggerInitialized () {
      this.initialized = true
      this.$emit('initialized')
    },
    getSubmittedTime () {
      return moment(this.formViewModel?.updatedTime).format('L, hh:mm A')
    },
    getSubmitedName () {
      const submittedId = this.formViewModel.submittedAssigneeId
      const user = this.formViewModel.assignees.find(user => user.id === submittedId)
      return user?.displayName
    },
    handlePageLoadError(err){
      if(err.fileNotFond){
        this.canOperate = false
        this.scale = 1
      }
    }
  }
})
</script>

<template>
  <div class="form-canvas-view">
    <div
      ref="scrollContainer"
      class="form-canvas-scroll-container">
      <div
        v-if="layoutReady"
        class="form-canvas-view-wrap"
        :style="canvasWrapStyle">
        <div
          class="form-canvas-view-page"
          :style="canvasStyles">
          <PageBackground
            class="form-background"
            :page-model="pageModel"
            :runtime-options="runtimeOptions"
            :scale="scale"
            @error="handlePageLoadError"
            @initialized="triggerInitialized" />
          <div
            v-if="initialized"
            class="form-canvas-content">
            <slot :scale="scale" v-if="canOperate" />
          </div>
          <div
            v-if="formViewModel.isCompleted"
            class="form-canvas-footer"
            :style="footerStyle">
            <div>{{ $t('submit_by') }} {{ getSubmitedName() }}</div>
            <div>{{ getSubmittedTime() }}</div>
          </div>
        </div>
      </div>
    </div>

    <div
      v-if="enablePager && canOperate"
      class="form-canvas-pager">
      <div class="form-canvas-page-inner">
        <button
          v-if="showPrevButton"
          class="form-canvas-pager-btn btn-left"
          @click="gotoPrevPage">
          <i class="micon-accordion-left-centered" />
        </button>
        <button
          v-if="showNextButton"
          class="form-canvas-pager-btn btn-right"
          @click="gotoNextPage">
          <i class="micon-accordion-right-centered" />
        </button>
      </div>
    </div>
    <FormCanvasFloatBar
      ref="actionBar"
      v-if="canOperate"
      :page-initialized="initialized"
      :keep-show="true"
      :form-view-model="formViewModel"
      :page="currentPage"
      :page-width="pageModel.width"
      :scale="zoomScale"
      :enable-pager="enablePager"
      :enable-full-screen="enableFullScreen"
      :total-page="formViewModel.pages.length"
      @pageChange="handleChangePage"
      @zoomChange="handleZoomChange" />
  </div>
</template>

<style scoped lang="scss">
.form-canvas-scroll-container {
  overflow: auto;
  width: 100%;
  height: 100%;
  background-color: #f4f4f4;
}

.form-canvas-view-wrap {
  margin: auto;
}

.form-canvas-footer {
  display: flex;
  justify-content: space-between;
  position: absolute;
  font-weight: 400;
  color: $mx-color-var-label-secondary;
  margin-top: 12px;
}

.form-canvas-view {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
}

.form-canvas-view {
  position: relative;

  .form-canvas-view-page {
    transition: transform 0.4s ease-in-out;
  }

  .form-background {
    position: absolute;
    top: 0;
    left: 0;
  }

  .form-canvas {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
  }

  .form-canvas-pager {
    height: 100%;
    position: absolute;
    width: 100%;
    pointer-events: none;
  }

  .form-canvas-page-inner {
    width: 100%;
    margin-top: -24px;
  }

  .form-canvas-pager,
  .form-canvas-pager-btn {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .form-canvas-pager-btn {
    width: 32px;
    height: 48px;
    background-color: rgba(32, 32, 32, 0.92);
    color: #fff;
    border: none;
    position: absolute;
    pointer-events: auto;
  }

  .btn-left {
    left: 0px;
  }

  .btn-right {
    right: 0px;
  }
}
</style>
