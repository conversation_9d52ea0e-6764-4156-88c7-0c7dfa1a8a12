<script lang="ts">
import {defineComponent} from '@vue/composition-api'
import type {PropType} from 'vue'
import {FormElementAnyInputModel} from '@model/form/defines/allType'
import {FormElementType, FormRuntimeOptions, FormScenes} from '@model/form/defines/shared'
import {getSelectionElementCoord} from '@views/form/common/Coordinates'
import FormElementHotspot from '@views/form/pdfForm/FormElementHotspot.vue';
import {ElementHighlightProps} from '@views/form/common/types';
import {DefaultHighlightFieldName} from '@views/form/common/highlightProps'
import {BrowserUtils} from "@commonUtils";

function scaleFontSize (fontSize: string, scale: number, height?: number): string {
  const regex = /(\d+)(pt|px)/;
  const match = fontSize.match(regex);
  const defaultSize = height / 2
  if (match) {
    const size = parseInt(match[1]);
    let unit = match[2];
    let ratio = 1
    if (unit == 'pt') {
      ratio = 1.2
      unit = 'px'
    }
    let newSize = size * scale * ratio;
    if (height && (newSize > defaultSize || !newSize)) {
      newSize = defaultSize
    }
    return `${newSize}${unit}`;
  } else {
    return fontSize;
  }
}
const defaultFontSize = 10
const lineHeight = 1.26

function calcExpectedFontSize (element: FormElementAnyInputModel,scale, supportMultipleLine) {
  const pdfInfo = element.customData
  const modelHeight = pdfInfo.height
  if (!supportMultipleLine) {
    const minHeight = modelHeight /lineHeight
    return Math.ceil(Math.min(minHeight, defaultFontSize) * scale)
  } else {
    const targetRows = Math.ceil(modelHeight / (defaultFontSize * lineHeight))
    const targetFontSize = modelHeight / (targetRows * lineHeight)
    return Math.ceil(Math.min(targetFontSize, defaultFontSize) * scale)
  }
}

export default defineComponent({
  name: 'PDFFormElementView',
  components: {FormElementHotspot},
  props: {
    element: {
      type: Object as PropType<FormElementAnyInputModel>,
      required: true,
      default: null
    },
    runtimeOptions: {
      type: Object as PropType<FormRuntimeOptions>,
      default: () => ({})
    },
    scale: {
      type: Number,
      default: 1
    },
    selectable: {
      type: Boolean,
      default: false
    },
    selected: {
      type: Boolean,
      default: false
    },
    highlight: {
      type: Object as PropType<ElementHighlightProps>,
      default: () => ({})
    },
    pageWidth: {
      type: Number,
      default: 0
    }
  },
  data () {
    return {
      DefaultHighlightFieldName,
      isMouseHover: false,
      wrapStyles: this.getWrapStyles(),
      isMobile: BrowserUtils.isMobile,
    }
  },
  computed: {
    showDashedBorder () {
      return [FormElementType.SingleSelection, FormElementType.MultiSelection].includes(this.element.type) && this.element.options.length > 1
    },
    showBorder () {
      if(![FormElementType.SingleSelection, FormElementType.MultiSelection].includes(this.element.type)){
        return true
      }
      return (this.element.options.length === 1) ? false: true
    },
    showHotspot () {
      return this.selectable && !(this.runtimeOptions.isCompleted)
    },
    isOnPageRight (){
      const errorMaxWidth = 200
      if(this.pageWidth < (this.element?.customData?.x + errorMaxWidth)){
        return true
      }
      return false
    }
  },
  methods: {
    triggerClick () {
      this.$emit('click', this.element)
    },
    getWrapStyles () {
      if (!this.element.customData) {
        return {}
      }
      const {width, height, left, top} = getSelectionElementCoord(this.element, this.scale)
      const errorMaxWidth = (200 * this.scale)
      const fontSize = calcExpectedFontSize(this.element,this.scale,this.element.type === FormElementType.MultiLineText)
      const { letterSpacing, numberOfCells } = this.element.customData
      let outLetterSpacing = 'inherit'
      let fontFamily = 'inherit'
      if(numberOfCells){
        const cellWidth = width / parseInt(numberOfCells, 10);
        const charWidthEstimate = fontSize * 0.6; // 估算字符宽度（monospace 字体）
        outLetterSpacing = `${cellWidth - charWidthEstimate}px`
      }
      if(letterSpacing){
        outLetterSpacing = `${parseInt(letterSpacing) * fontSize * 0.85}px`
      }

      const styles = {
        width: `${width}px`,
        height: `${height}px`,
        left: `${left}px`,
        top: `${top}px`,
        '--pdf-form-item-height': `${height}px`,
        '--pdf-form-item-bg-height': `${height-8}px`,
        '--pdf-form-item-bg-width': `${width-8}px`,
        '--pdf-form-item-width': `${width}px`,
        '--pdf-form-error-max-width': `${errorMaxWidth}px`,
        '--pdf-form-input-font-size':  fontSize+ 'px',
        '--pdf-form-input-line-height':  (fontSize * lineHeight)+ 'px',
        '--pdf-form-input-letter-spacing': outLetterSpacing,
        '--scale-factor': this.scale,
        '--user-unit': letterSpacing || 0.42,

      }
      return styles
    },
  }
})
</script>

<template>
  <div
    :class="{
      'pdf-form-item': true,
      'is-field-selected': selected,
      'is-show-highlight': isMouseHover || isMobile,
      'is-field-error': element.errors.length,
      'is-on-page-right': isOnPageRight
    }"
    :data-id="element.id"
    :style="wrapStyles"
    @mouseover="isMouseHover = true"
    @mouseleave="isMouseHover = false"
  >
    <slot />
    <FormElementHotspot
      v-if="showHotspot"
      :selected="selected && showBorder"
      :dashed-border="showDashedBorder"
      @click="triggerClick()"
    />
  </div>
</template>

<style scoped lang="scss">
.pdf-form-item {
  position: absolute;
  --total-scale-factor: calc(var(--scale-factor) * var(--user-unit));
  //--pdf-form-input-letter-spacing: calc(14.400000000000002px * var(--total-scale-factor) - 1ch);
  //font-size: calc(9px * var(--total-scale-factor));
  > div {
    position: absolute;
  }
}

.is-field-error {
  z-index: 1;
}

.is-field-selected {
  ::v-deep {
    .pdf-selection-item::before {
      border: var(--pdf-element-border-size) solid var(--pdf-element-color-selected);
    }
  }
}
.is-on-page-right {
  ::v-deep {
    .tooltip-alert-baner {
      right: 0px;
    }
  }
}

.is-show-highlight {
  z-index: 2;
  ::v-deep {
    .online-user-name {
      visibility: visible !important;
    }
    .pdf-item-error {
      visibility: visible !important;
    }
  }
}
</style>
