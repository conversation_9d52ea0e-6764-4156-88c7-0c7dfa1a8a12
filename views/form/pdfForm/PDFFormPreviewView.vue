<script lang="ts">
import {defineComponent} from '@vue/composition-api'
import type {PropType} from 'vue'
import {FormRuntimeOptions, FormScenes} from '@model/form/defines/shared'
import {FormViewModel} from '@model/form/defines/formViewModel'
import PDFFormElementView from '@views/form/pdfForm/PDFFormElementView.vue'
import FormFieldView from '@views/form/components/FormFieldView.vue'
import PageBackground from '@views/form/pdfForm/PageBackground.vue'
import {FormAnyElementViewModel, FormElementAnyInputModel} from '@model/form/defines/allType'
import FormFactoryManager from '@model/form/factory'
import {installFormComponents} from '@views/form/install'
import LibListHeader from '@views/contentLibrary/component/LibListHeader.vue'
import FormTheme from '@views/form/components/FormTheme.vue';
import FormElementHotspot from '@views/form/pdfForm/FormElementHotspot.vue';
import PDFFormCanvasView from '@views/form/pdfForm/PDFFormCanvasView.vue';

/**
 * Display the Form pages area, including the background, form elements, and zoom panel.
 */
export default defineComponent({
  name: 'PDFFormPreviewView',
  components: {
    FormElementHotspot,
    FormTheme,
    LibListHeader,
    PageBackground,
    PDFFormCanvasView,
    PDFFormElementView,
    FormFieldView
  },
  props: {
    runtimeOptions: {
      type: Object as PropType<FormRuntimeOptions>,
      default: () => ({})
    },
    formViewModel: {
      type: Object as PropType<FormViewModel>,
      default: () => ({})
    },
    selectedElement: {
      type: Object as PropType<FormAnyElementViewModel>,
      default: () => ({})
    },
    defaultPageNumber: {
      type: Number,
      default: 1
    },
    enableFullScreen: {
      type: Boolean,
      default: false
    },
    selectable: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      currentPage: 0,
      selectedModelId: this.selectedElement?.id,
    }
  },
  computed: {
    currentPageLabel () {
      return this.currentPage + 1
    },
    elementSelectable () {
      if(!this.selectable){
        return false
      }
      return [FormScenes.CreateForm, FormScenes.EditForm].includes(this.runtimeOptions.scenes)
    },
    totalPages () {
      return (this.formViewModel.pages || []).length
    },
    pageElements (): FormAnyElementViewModel[] {
      return this.formViewModel.pages[this.currentPage]?.elements || []
    },
    currentPageModel() {
      return this.formViewModel.pages[this.currentPage]
    },
    elementWrapRenderComponent () {
      if (this.formViewModel.isPDFForm) {
        return 'PDFFormElementView'
      }
      return 'FormFieldView'
    }
  },

  created () {
    installFormComponents()
  },
  methods: {
    handlePageChange (page: number) {
      this.currentPage = page - 1
    },
    getElementRenderComponent (model: FormElementAnyInputModel) {
      const factory = FormFactoryManager.getFormElementFactory(model.type)
      return factory.uiOption(this.runtimeOptions).view
    },
    handleSelectElement (element: FormElementAnyInputModel) {

      this.$emit('select', element)
    },
    gotoFirstErrorPage(){
      for(let i = 0 ; i <= this.formViewModel.pages.length; i++){
        const page = this.formViewModel.pages[i]
        if(page.elements.some(element=>element.errors?.length)){
          this.currentPage = i
          return ;
        }
      }
    }
  }
})
</script>

<template>
  <PDFFormCanvasView
    v-slot="viewInfo"
    class="form-preview"
    :page="currentPageLabel"
    :enable-pager="true"
    :enable-full-screen="enableFullScreen"
    :form-view-model="formViewModel"
    @pageChange="handlePageChange">
    <FormTheme
      :scale="viewInfo.scale"
      :runtime-options="runtimeOptions">
      <el-form>
        <component
          :is="elementWrapRenderComponent"
          v-for="element in pageElements"
          :key="element.id + 'wrap'"
          :selected="selectedElement.id === element.id"
          :element="element"
          :pageWidth="currentPageModel.width"
          :runtime-options="runtimeOptions"
          :selectable="elementSelectable"
          :scale="viewInfo.scale"
          @click="handleSelectElement">
          <component
            :is="getElementRenderComponent(element)"
            :key="element.id"
            :readonly="true"
            :element="element"
            :runtime-options="runtimeOptions"
            :scale="viewInfo.scale" />
        </component>
      </el-form>
    </FormTheme>
  </PDFFormCanvasView>
</template>

<style scoped lang="scss">
.form-preview {
  height: 100%;
  width: 100%;
}
</style>
