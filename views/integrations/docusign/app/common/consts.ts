export const DocusignSignerColors = ['#FFD65B', '#ACDCE6', '#C0A5CF', '#97C9BF', '#F7B994', '#C2D5E6', '#CFDB7E', '#FF9980', '#E6C6E6', '#FFB3C6']

export const DSErrorDetailCode = {
  NO_DOCUMENT_FOUND: 'NO_DOCUMENT_FOUND',
  UNRESOLVED_DDR_FILE: 'UNRESOLVED_DDR_FILE',
  FILE_SIZE_EXCEEDED: 'FILE_SIZE_EXCEEDED',
  FILE_TYPE_NOT_SUPPORTED: 'FILE_TYPE_NOT_SUPPORTED',
  INVALID_USER_OFFSET: 'INVALID_USER_OFFSET',
  ANCHOR_TAB_STRING_NOT_FOUND: 'ANCHOR_TAB_STRING_NOT_FOUND',
  ANCHOR_TAG_PROCESSING_FAILURE: 'ANCHOR_TAG_PROCESSING_FAILURE',
  INVALID_IDENTITY_VERIFICATION_FOUND: 'INVALID_IDENTITY_VERIFICATION_FOUND',
  ID_CHECK_FAILED: 'ID_CHECK_FAILED'
}

export const CreateDSType = {
  CREATE_ENVELOPE: 'create_envelope',
  USE_TEMPLATE: 'use_template',
  CREATE_WITH_ANCHORS: 'create_with_anchors',
}

export const AnchorType = {
  SIGNATURE: 'signHereTabs',
  INITIALS: 'initialHereTabs',
  DATE_SIGNED: 'dateSignedTabs',
  FULL_NAME: 'fullNameTabs',
  TITLE: 'titleTabs',
  COMPANY: 'companyTabs',
  EMAIL: 'emailAddressTabs',
  TEXT: 'textTabs',
  DATE: 'dateTabs',
  NUMBER: 'numberTabs',
}

export const AnchorUserLimit = {
  maxTagNum: 10,
  maxAssigneeNum: 15
}