import { MxAjax, MxISDK } from 'isdk'
import {generateAuthorizationHeader} from './generateAuthorizationHeader'
import { DocuSignCredentials } from '../common/types'

export async function resendDocusignEvent (auth: DocuSignCredentials, envelopeId: string) {
  try {
    const {base_uri, account_id, auth_id} = auth
    const accessToken = await MxISDK.getCurrentUser().getAccessToken()
    const response = await MxAjax.put(`/integration/docusign/accounts/${account_id}/envelopes/${envelopeId}/resend`, {
      auth_id,
      base_uri
    },{
      authorizationHeader: generateAuthorizationHeader(auth, {access_token: accessToken}),
      contentType: 'application/json'
    })
    return response
  } catch (e) {
    throw e
  }
}