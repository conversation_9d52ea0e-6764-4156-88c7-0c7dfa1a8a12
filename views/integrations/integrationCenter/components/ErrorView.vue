<template>
  <div class="integration-erro-page">
    <div v-if="errorBody.noPermission" class="no-permission">
      <img :src="noPermissionsImg"
           alt="">
    </div>
    <template v-else>
      <div class="integration-erro-page_title">
        {{ errorBody.title }}
      </div>
      <div class="integration-erro-page_subtitle">
        {{ errorBody.subtitle }}
      </div>
      <div class="integration-erro_error-buttons">
        <el-button v-if="showCancel" type="gray" size="small" @click="onCancel">
          {{ $t('cancel') }}
        </el-button>
        <el-button v-if="showRetry" type="gray-branding" size="small" @click="onRetry">
          {{ $t('retry') }}
        </el-button>
      </div>
    </template>
  </div>
</template>

<script>
import noPermissionsImg from '@views/theme/src/images/integration/no_permissions.png'
export default {
  name: 'ErrorView',
  components: {},
  props: {
    showCancel:{
      type: <PERSON>olean,
      default: false,
    },
    errorBody: {
      type: Object,
      default: () => ({}),
    },
    showRetry: {
      type: Boolean,
      default: true,
    }
  },
  data () {
    return {
      noPermissionsImg,
    }
  },
  methods: {
    onRetry () {
      this.$emit('retry')
    },
    onCancel () {
      this.$emit('cancel')
    }
  }
}
</script>
<style lang="scss" scoped>
.integration-erro-page {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 28px 45px;
  &_title {
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: 24px;
    color: #000;
  }
  &_subtitle {
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px;
    color: #616161;
    text-align: center;
    margin-top: 7px;
    margin-bottom: 19px;
  }

  .no-permission{
    display: flex;
    align-items: center;
    height: 398px;
    img{
      width: 180px;
      height: 124px;
    }
  }

  .integration-erro_error-buttons {
    display: flex;
    align-items: center;
  }
}
</style>