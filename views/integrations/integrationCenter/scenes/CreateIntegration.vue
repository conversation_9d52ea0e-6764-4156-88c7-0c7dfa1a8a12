<template>
  <BaseModal :visible.sync="visibleProxy"
             :modal-option="{
               width: '540px',
               beforeClose: beforeClose
             }"
             class="mobile-fullscreen-dialog integration-dialog"
             :class="{ 'in-preview': isPreview ,'dialog-auto-height':hasVisitedForm && !isPreviewNow}"
             :showClose="isWorkflow || !isPreview"
             @close="onClose">
    <template slot="title">
      <div class="flex-center"
           style="width: 100%; justify-content: space-between;">
        <div style="display: flex; align-items: center; flex: 1; width: 90%;">
          <i v-if="showGoback"
             class="micon-left-arrow-new go-back-nav-icon"
             @click="handleGoback" />
          <IntegrationIcon v-show="!isPreviewNow"
                           :imgSrc="compImgSrc" />
          <div class="mx-ellipsis">
            {{ integrationTitle }}
          </div>
        </div>
      </div>
    </template>
    <template slot="content">
      <template v-if="loading">
        <span v-mx-loading-spinner="loading"
              class="loding-spinner" />
      </template>
      <template v-else>
        <IntegrationBasicForm v-show="currentRenderView === allViews.basic"
                              :isMainView="isMainView"
                              v-bind="basicInfoProps"
                              @handleEmit="handleEmit"
                              @next="handleBasicNext"
                              @close="onClose"
                              @addRole="addRole">
          <OAuthAccount v-if="!isEditMode && isMainView"
                        :connector="connector"
                        :showTip="oauthInvalid"
                        @change="onOAuthAccountChange" />
        </IntegrationBasicForm>

        <IntegrationAppActionList v-show="currentRenderView === allViews.actions"
                                  v-bind="actionProps"
                                  @next="handleActionsNext">
          <OAuthAccount v-if="!isEditMode && isMainView"
                        :connector="connector"
                        @change="onOAuthAccountChange" />
        </IntegrationAppActionList>

        <IntegrationFormView
          v-if="hasVisitedForm"
          v-show="currentRenderView === allViews.integrationForm"
          ref="formRef"
          class="integration-form-view"
          :integration-options="integrationOptions"
          :hideBackButton="true"
          @success="handleFormNext"
          :cachedValues.sync="cachedFormValues"
        />

        <IntegrationPreview v-if="currentRenderView === allViews.preview"
                            :connector="connector"
                            :selectedAction="selectedAction"
                            :invokedData="invokedData"
                            :hostBoard="hostBoard"
                            v-bind="previewProps"
                            @handleEmit="handleEmit"
                            @close="onClose" />
      </template>
    </template>
  </BaseModal>
</template>

<script>
import {
  reactive,
  ref,
  defineComponent,
  provide,
  watch,
  getCurrentInstance,
  computed,
  onBeforeMount,
  onBeforeUnmount,
} from '@vue/composition-api'
import { visibleMixin } from '@views/common/components/modals/mixins'
import { useI18n, useStore } from '@views/common'
import focusToErrorItem from '@views/workflows/utils/focusToErrorItem'
import IntegrationIcon from '../components/IntegrationIcon'
import OAuthAccount from '../components/OAuthAccount'
import IntegrationPreview from '../components/IntegrationPreview'
import IntegrationBasicForm from '../views/IntegrationBasicForm'
import IntegrationAppActionList from '../views/IntegrationAppActionList'
import IntegrationFormView from '@views/integrations/integrationCenter/views/IntegrationFormView.vue'
import { filterValidBinders } from '@views/requests/utils/filter'
import { useIntegrationCenterStore } from '@views/stores/integrationCenter'
import { useStorage } from '@views/common/composition/useStorage'
import { transformIntegrationAssignees } from '@views/integrations/integrationCenter/common/transformIntegrationForm'

export default defineComponent({
  name: 'CreateIntegration',
  components: {
    IntegrationIcon,
    OAuthAccount,
    IntegrationBasicForm,
    IntegrationAppActionList,
    IntegrationFormView,
    IntegrationPreview,
  },
  mixins: [visibleMixin, focusToErrorItem],
  props: {
    isPreview: {
      type: Boolean,
      default: false,
    },
    showBack: {
      type: Boolean,
      default: false,
    },
    insideBinder: {
      type: Boolean,
      default: false,
    },
    isReverseMilestoneContext:{
      type: Boolean,
      default: false
    },
    isFirstStepContext: {
      type: Boolean,
      default: false
    },
    isDirectMode: {
      type: Boolean,
      default: false,
    },
    isObjectCreation: {
      type: Boolean,
      default: false,
    },
    isWorkflow: {
      type: Boolean,
      default: false,
    },
    isFlowTemplate: {
      type: Boolean,
      default: false,
    },
    isEditMode: {
      type: Boolean,
      default: false,
    },
    isTempBoard: {
      type: Boolean,
      default: false,
    },
    selectUserDirect: {
      type: Boolean,
      default: true,
    },
    step: {
      type: Object,
      default: () => ({}),
    },
    boardUsers: {
      type: Array,
      default: () => [],
    },
    roles: {
      type: Array,
      default: () => [],
    },
    presetObject: {
      type: Object,
      default: () => ({}),
    },
    hostBoard: {
      type: Object,
      default: () => ({}),
    },
    subType: {
      type: String,
      default: 'Integration',
    },
    index: {
      type: Number,
      default: 0,
    },
    isSequential: {
      type: Boolean,
      default: true,
    },
    supportNoAssignee: {
      type: Boolean,
      default: false,
    },
    integrationApp: {
      type: Object,
      default: () => ({}),
    },
    flowStepOrderNumber: {
      type: Number,
      default: undefined,
    },
    isEditStepInCreateFlow: {
      type: Boolean,
      default: false,
    }
  },
  computed: {
    integrationOptions(){
      return {
        actionKey: this.selectedAction?.key,
        enableDDR: this.isWorkflow || this.isFlowTemplate,
        //todo: for edit case need get form values from action steps.0.actions.0
        formDefaultValues:{},
        appId: this.connector.app_id,
        authId: this.connector.auth_id,
        appName: this.integrationApp?.app_name,
        assignees: transformIntegrationAssignees(this.basicBoardInfo?.assignee),
        title: this.basicBoardInfo?.title,
        description: this.basicBoardInfo?.description
      }
    }
  },
  setup(props, { emit }) {
    const { t } = useI18n()
    const vm = getCurrentInstance()
    const allViews = {
      basic: 'BASIC',
      actions: 'ACTIONS',
      integrationForm: 'INTEGRATION_FORM',
      selectBinder: 'SELECT_BINDER',
      preview: 'PREVIEW',
    }
    const isMainView = ref(true)

    const localStorage = useStorage('mx-integrations-forms', { form: {} }, true)
    // reset cache form
    localStorage.form = {}
    provide('localStorage', reactive(localStorage))

    const integrationCenterStore = useIntegrationCenterStore()

    const cacheRoleList = ref([])

    const updatedRoleList = computed(() => {
      if(cacheRoleList.value && cacheRoleList.value.length){
        return props.roles.concat(cacheRoleList.value)
      }
      return props.roles
    })

    const addRole = (newRole) => {
      cacheRoleList.value.push(newRole) 
    }


    // const hasVisitedSelectBinder = ref(false)
    const viewHistory = ref([allViews.basic])
    const pushHistory = (history) => {
      if (!viewHistory.value.includes(history)) {
        viewHistory.value.push(history)
      }
    }
    const hasVisitedForm = computed(() =>{
      return viewHistory.value.includes(allViews.integrationForm)
    })

    const currentRenderView = computed(() => {
      return viewHistory.value[viewHistory.value.length -1]
    })

    const compImgSrc = computed(() => {
      return `/integration/framework/v1/apps/${props.integrationApp.app_id}/logo`
    })

    const appActions = computed(() => {
      return (
        integrationCenterStore.integrationApps.find(
          (app) => app.app_id === props.integrationApp.app_id
        )?.actions || []
      )
    })


    const showGoback = computed(() => {
      return props.showBack || viewHistory.value.length > 1 || !isMainView.value
    })


    const goback = () => {
      if(viewHistory.value.length == 1){
        emit('back');
        return;
      }
      if (!isMainView.value) {
        isMainView.value = true
      } else  {
        viewHistory.value.pop()
      }
    }

    const handleGoback = () => {
      if(hasVisitedForm.value && currentRenderView.value === allViews.integrationForm) {
        if (!vm.refs.formRef.goBack()) {
          //form current is first page
          goback()
        }
      }else{
        goback()
      }
    }

    const basicInfoProps = reactive({
      roles: updatedRoleList.value,
      presetObject: props.presetObject,
      isEditMode: props.isEditMode,
      selectUserDirect: props.selectUserDirect,
      boardUsers: props.boardUsers,
      isEditStepInFlowBinder: !!props.boardUsers.length,
      isEditStepInCreateFlow: props.isEditStepInCreateFlow,
      isFlowTemplate: props.isFlowTemplate,
      hostBoard: props.hostBoard,
      isWorkflow: props.isWorkflow,
      isTempBoard: props.isTempBoard,
      isSequential: props.isSequential,
      flowStepOrderNumber: props.flowStepOrderNumber,
      isReverseMilestoneContext: props.isReverseMilestoneContext,
      isFirstStepContext: props.isFirstStepContext,
      step: props.step,
      supportNoAssignee: props.supportNoAssignee,
      insideBinder: props.insideBinder,
      isObjectCreation: props.isObjectCreation
    })

    const selectedAction = ref(null)
    const invokedData = ref(null)

    const actionProps = reactive({
      actions: appActions,
    })

    const formProps = reactive({
      roles: updatedRoleList.value,
      presetObject: props.presetObject,
      isEditMode: props.isEditMode,
      enableDDR: props.isWorkflow || props.isFlowTemplate
    })

    const selectBinderProps = reactive({
      sharedBinders: null,
      assignees: null,
    })

    const previewProps = reactive({
      roles: updatedRoleList.value,
      insideBinder: props.insideBinder,
      isObjectCreation: props.isObjectCreation,
      selectUserDirect: props.selectUserDirect,
      isFlowTemplate: props.isFlowTemplate,
      isWorkflow: props.isWorkflow,
      boardUsers: props.boardUsers,
      isNotStarted: !props.isEditMode,
      isDirectMode: props.isDirectMode
    })

    watch(() => cacheRoleList.value, async () => {
      if(cacheRoleList.value && cacheRoleList.value.length){
        updatedRoleList.value = props.roles.concat(cacheRoleList.value)
        basicInfoProps.roles = updatedRoleList.value;
        formProps.roles = updatedRoleList.value;
        previewProps.roles = updatedRoleList.value;
      }
    }, {
      deep: true,
      immediate: true
    })


    // direct create transaction need to choose binder
    const getShareBinderInfo = async (assignees) => {
      const directServiceStore = useStore('directService')
      const sharedBinders = await directServiceStore.dispatch('getSharedBinders', assignees)
      if (sharedBinders) {
        selectBinderProps.sharedBinders = filterValidBinders(sharedBinders)
      }
    }

    const basicBoardInfo = ref(null)

    const oauthInvalid = ref(false)
    const handleBasicNext = (data, showPreview) => {
      if (showPreview) {
        // edit preivew case
        isMainView.value = false
      } else if (!connector.value) {
        oauthInvalid.value = true
      } else {
        basicBoardInfo.value = data
        if (appActions.value && appActions.value.length > 1) {
          pushHistory(allViews.actions)
        } else {
          if (!appActions?.value || !appActions?.value?.length === 0) {
            selectedAction.value = {}
          }
          
          if (appActions.value && appActions.value.length === 1) {
            selectedAction.value = appActions.value[0]
          }
          pushHistory(allViews.integrationForm)
        }
      }
    }

    const handleActionsNext = (action) => {
      selectedAction.value = action
      pushHistory(allViews.integrationForm)
    }

    const buildFormData = ref(null)
    const formRef = ref(null)

    const handleFormNext = async ({ formData, invokedData: _invokedData }) => {

        if(props.isDirectMode) {
          buildFormData.value = formData
        }

        invokedData.value = _invokedData
        pushHistory(allViews.preview)

    }

    provide('basicBoardInfo', basicBoardInfo)
    provide('buildFormData', buildFormData)
    provide('integrationApp', props.integrationApp)

    const integrationImg = computed(() => {
      const { app_id } = props.integrationApp
      return app_id ? `/integration/framework/v1/apps/${app_id}/logo` : ''
    })

    const isPreviewNow = computed(() => {
      return currentRenderView.value === allViews.preview || !isMainView.value
    })

    const integrationTitle = computed(() => {
      if (currentRenderView.value === allViews.integrationForm) {
        return selectedAction.value?.title
      } else if (isPreviewNow.value) {
        return t('View_preview')
      } else if (props.isEditMode) {
        let title = t('Edit_Integration_Action', { action: props.integrationApp.app_name })
        // For step, it use step.displayOrde, for the actual object, use StepIndex
        const stepIndex = props.step?.displayOrder || props.index
        if (stepIndex && props.isSequential) {
          title += ` (${t('Step_of_number', { number: stepIndex })})`
        }
        return title
      } else {
        return t('New_Integration_Action', { action: props.integrationApp.app_name })
      }
    })

    const onClose = (e) => {
      emit('close', e)
    }

    const beforeClose = (hide) => {
      emit('closeOnClick')
      hide()
    }

    const scrollToError = () => {
      vm.proxy.scrollToErrorField('.el-dialog__body')
    }

    const handleEmit = ({ action, data }) => {
      emit(action, data)
    }

    const loading = ref(false)
    const connector = ref(null)

    onBeforeMount(async () => {
      try {
        if (props.isEditMode) return

        loading.value = true
        const userStore = useStore('user')

        const { auth_id } = props.integrationApp
        //if support edit, the value must be set from 'customData'
        const creatorId = userStore.currentUser.value?.id
        connector.value = await integrationCenterStore.validateAccount(auth_id, creatorId)

        loading.value = false
      } catch (error) {
        loading.value = false
      }
    })

    const onOAuthAccountChange = (_connector) => {
      connector.value = _connector
      if (_connector) {
        oauthInvalid.value = false
        this.cachedFormValues = {}
      } else {
        //back to step one
        viewHistory.value.splice(1)
        hasVisitedForm.value = false
      }
    }

    onBeforeUnmount(() => {
      localStorage.form = {}
      integrationCenterStore.closeIntegrationOAuthWindow()
    })
    const cachedFormValues  = ref({})

    return {
      loading,
      compImgSrc,
      formRef,
      connector,
      isMainView,
      oauthInvalid,
      isPreviewNow,
      basicBoardInfo,
      allViews,
      viewHistory,
      currentRenderView,
      hasVisitedForm,
      basicInfoProps,
      actionProps,
      formProps,
      selectBinderProps,
      previewProps,
      selectedAction,
      invokedData,

      handleBasicNext,
      handleActionsNext,
      handleFormNext,

      showGoback,

      integrationImg,
      integrationTitle,

      onClose,
      handleGoback,
      beforeClose,
      scrollToError,
      handleEmit,
      onOAuthAccountChange,
      addRole,
      cachedFormValues
    }
  },
})
</script>

<style lang="scss" scoped>
.integration-dialog {
  ::v-deep .el-dialog {
    .el-dialog__header .left {
      width: 100%;
    }
    .el-dialog__footer {
      box-shadow: 0px -1px 0px #e0e0e0;
    }
    .el-dialog__body {
      padding-bottom: 0;
      padding: 0px;
      min-height: 460px;
    }
  }
  &.in-preview {
    ::v-deep .el-dialog {
      .el-dialog__body {
        padding: 24px;
        background-color: $mx-color-var-fill-quaternary;
      }
      .el-dialog__footer {
        display: none;
      }
    }
  }
}
.flex-center {
  display: flex;
  align-items: center;
}
.dialog-auto-height {
  ::v-deep {
    .el-dialog__body {
      overflow: auto;
      max-height: calc(100vh - 262px);
      display: flex;
      flex-direction: column;
    }
  }
}
.integration-form-view{
 ::v-deep{
   .integration-form-action{
     .el-button{
       width: 100%;
     }
   }
 }
}
.object-icon {
  margin-right: 10px;
  width: 24px;
}

.title-icon {
  width: 24px;
  height: 24px;
  margin-right: 8px;
}

.go-back-nav-icon {
  font-size: 16px;
  margin-right: 4px;
  cursor: pointer;
  color: $mx-color-var-text-secondary;

  &.disabled {
    cursor: default !important;
  }
}

@media (max-width: 768px) {
  .mobile-fullscreen-dialog {
    ::v-deep .el-dialog {
      .el-dialog__body {
        flex: 1;
      }
    }
  }
}
</style>