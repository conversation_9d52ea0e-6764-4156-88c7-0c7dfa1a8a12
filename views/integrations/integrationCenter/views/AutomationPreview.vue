<script lang="ts">
import AmIntegrationPreview from '@views/automationBuilder/components/integration/components/AmIntegrationPreview.vue'
import { defineComponent } from '@vue/composition-api'
import { PropType } from 'vue'
import { IntegrationFormOption } from '@views/integrations/integrationCenter/common/transformIntegrationForm'

export default defineComponent({
  name: 'AutomationPreview',
  components: { AmIntegrationPreview },
  props: {
    integrationOptions: {
      type: Object as PropType<IntegrationFormOption>,
      default: () => ({})
    },
    invokedData: {
      type: Object,
      default: () => ({})
    }
  }
})
</script>

<template>
  <div class="automation-form-wrap">
    <div class="preview-integration-form">
      <div class="preview-wrap">
        <div class="preview-wrap-inner">
          <div class="back-btn-action">
            <a class=" go-back-nav-icon mx-text-c1 mx-clickable" @click="$emit('back')">
            <i class="micon-arrow-left-sf" />
            <span>{{ $t('back') }}</span>
            </a>
          </div>
          <AmIntegrationPreview
            :actionData="integrationOptions.actionData"
            :invokedData="invokedData"
            class="integration-preview"
          />
        </div>
      </div>
    </div>
    <div class="integration-form-action for-outside">
      <slot></slot>
    </div>
  </div>
</template>

<style scoped lang="scss">
.preview-integration-form {
  flex-grow: 1;
  height: 100%;
  background-color: #f4f4f4;
  border-radius: 6px;
  padding: 23px 28px;
}
.back-btn-action{
  a{
    color: #616161;
  }
  font-size: 14px;
  font-weight: 590;
  height: 37px;
}
.preview-wrap {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #fff;
  border-radius: 6px;
}

.preview-wrap-inner {
  padding: 23px;
  height: 100%;
  flex-grow: 1;
}

.automation-form-wrap {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.for-outside {
  background: #fff;
  border-bottom-left-radius: 6px;
  border-bottom-right-radius: 6px;
}

.integration-form-action {
  height: 68px;
  padding: 10px 28px;
  flex-shrink: 0;
  display: flex;
  justify-content: flex-end;
  border-top: 1px solid #e0e0e0;
}
</style>
