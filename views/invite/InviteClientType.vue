<template>
  <div class="invite-client-type">
    <div>
      <div class="mx-text-h3 invite-client-type-title">
        {{ $t('How_would_you_like_to_invite_this_client') }}
      </div>
      <div
        v-for="item in allTypes"
        :key="item.mode"
        v-mx-ta="{ page: 'access_type', id: 'all' }"
        :class="['custom-radio', type === item.mode ? 'mx-branding-border' : 'default-status']"
        @click="switchType(item.mode)">
        <div>
          <label class="mx-text-c1">{{ item.title }}</label>
          <span class="mx-ellipsis">{{ item.subtitle }}</span>
        </div>
        <div class="right-cell">
          <el-radio
            v-model="type"
            :label="item.mode"/>
        </div>
      </div>
    </div>
    <el-button
      type="primary"
      id="sendInvitation"
      :disabled="isLoading"
      :loading="isLoading"
      @click="goNext">{{ buttonText }}
    </el-button>
  </div>
</template>

<script>
import {mapGetters, mapMutations, mapActions} from 'vuex';
import {useInviteClientUserStore} from '@views/stores/inviteClientUser';
import {mapActions as piniaMapActions, mapState as piniaMapState} from 'pinia'

const InviteType = {
  AddToWorkspace: 'AddToWorkspace',
  AddToClient: 'AddToClient',
  AddToClientOnly: 'AddToClientOnly'
}
export default {
  name: 'InviteClientType',
  props: {
    defaultUser: {
      type: Object,
      default: () => ({})
    },
    clientGroups: {
      type: Array,
      default: () => ([])
    }
  },
  data () {
    return {
      type: InviteType.AddToWorkspace,
      InviteType,
      isLoading: false
    }
  },
  computed: {
    ...mapGetters('group', ['enableEmailPrivacy', 'isHideClientDashboard']),
    allTypes () {
      const types = [{
        title: this.$t('Add_to_conversation'),
        subtitle: this.$t('Add_to_conversation_subtitle'),
        mode: InviteType.AddToWorkspace
      }, {
        title: this.$t('Add_to_Contacts'),
        subtitle: this.$t('Add_to_Contacts_subtitle'),
        mode: InviteType.AddToClient
      }]
      if (!this.enableEmailPrivacy) {
        types.push({
            title: this.$t('Add_to_Contacts_directly'),
            subtitle: this.$t('Add_to_Contacts_directly_subtitle'),
            mode: InviteType.AddToClientOnly
          }
        )
      }
      return types
    },
    buttonText () {
      switch (this.type) {
        case InviteType.AddToClient:
          return this.$t('invite')
        case InviteType.AddToClientOnly:
          return this.$t('add')
        default:
          return this.$t('next')
      }
    }
  },
  methods: {
    ...mapMutations('contacts', ['addNewAdded']),
    ...mapActions('contacts', ['checkMemberExist']),
    ...piniaMapActions(useInviteClientUserStore, ['createRelationAndUpdateClientInfo']),
    switchType (type) {
      this.type = type
    },
    goNext () {
      if (this.type === InviteType.AddToWorkspace) {
        this.$emit('goWorkspaceSelector')
      } else if (this.type === InviteType.AddToClient) {
        const suppressEmailSms = this.computeSuppressOrgInvitationEmailSMS()
        this.inviteUser(suppressEmailSms)
      } else {
        this.inviteUser(true)
      }
    },
    computeSuppressOrgInvitationEmailSMS () {
      return !!this.isHideClientDashboard;
    },
    async verifyUser () {

      // Handle corner case: before sending,the user is changed to deactived user or be invited as an internal user
      try {
        await this.checkMemberExist(this.defaultUser)
      } catch (err) {
        if (err.isMemberExist) {
          if (err.isInternalUser) {
            let errorInfo = this.defaultUser.email ? this.$t('email_address_already_in_use') : this.$t('phone_number_already_in_use')
            if (err.isDisabled) {
              errorInfo = this.$t('This_user_has_been_deactivated_please_contact_your_administrator')
            }
            this.$mxMessage.error(errorInfo)
            return false
          } else if (err.isDisabledClient) {
            this.$mxMessage.error(this.$t('This_user_has_been_deactivated_please_contact_your_administrator'))
            return false
          }
        }
      }
    },
    async inviteUser (suppressEmailSms) {
      this.isLoading = true
      const isValid = await this.verifyUser()
      if (isValid === false) {
        this.isLoading = false
        return
      }
      try {
        const param = {
          user: this.defaultUser,
          suppressEmailSms: suppressEmailSms,
          clientTeamIds: this.clientGroups
        }
        const userInfo = await this.createRelationAndUpdateClientInfo(param)
        this.$mxMessage.success(this.type === InviteType.AddToClient ? this.$t('invitation_successfully_sent') : this.$t('Client_successfully_added'))
        this.$emit('close')
        // MVB-21341 if current route is "Contacts", then manually add user to contacts list
        if (this.$route.name === 'contact') {
          this.addNewAdded({...userInfo, isNewAdded: true})
        }
        this.$router.push({name: 'contact', params: {id: userInfo.id}});

      } catch (error) {
        if (error.detailCode === 'ERROR_INVALID_USER_TYPE') {
          const errorInfo = this.defaultUser.email ? this.$t('email_address_already_in_use') : this.$t('phone_number_already_in_use')
          this.$mxMessage.error(errorInfo)
        } else {
          this.$mxMessage.error(this.$t('system_exceed_limit_error'))
        }
      } finally {
        this.isLoading = false
      }
    },
  }
}
</script>

<style scoped lang="scss">
.mx-branding-border {
  border-width: 2px !important;
}

.default-status {
  border-color: $mx-color-var-fill-tertiary;
}

.custom-radio {
  cursor: pointer;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  height: 63px;
  margin-bottom: 16px;
  padding: 8px 13px;
  border-radius: 5px;
  border-width: 1px;
  border-style: solid;

  ::v-deep .el-radio__label {
    display: none;
  }

  .right-cell {
    display: flex;
    align-items: center;
  }

  label {
    display: block;

    &.el-radio {
      margin-bottom: 0;
    }
  }

  span {
    color: $mx-color-var-label-secondary;
    display: block;
    font-size: 12px;
    font-weight: normal;
    line-height: 16px;
    max-width: 420px;
  }
}
.invite-client-type-title {
  padding-bottom: 24px;
}
.invite-client-type {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 100%;
  padding: 28px;
  overflow-y: auto
}
</style>