<template>
  <ElFormItem
    ref="formItem"
    :prop="prop"
    :data-ta="'form_title_item'"
    :label="label"
    :show-message="true"
    :error="localErrorMessage"
    :rules="rules">
    <FilePicker
      v-if="!pickedFile.name"
      class="form-file-picker"
      :file-accept="fileAccept"
      :disable-unsupported-types="true"
      :file-max-siz="allowMaxFileSize"
      :placeholder="placeholder"
      @select="handleSelectFile"
      @error="handleError" />
    <div
      v-else
      class="field-border">
      <FilePickedFileView
        :file="pickedFile"
        :loading="isInProgress"
        :allowDelete="allowDeleteFile"
        :show-progress="true"
        :progress-percentage="uploadPercentage"
        @remove="handleRemoveFile" />
    </div>
    <div
      v-if="showDecideLater"
      class="decide-later-info-section flex items-center">
      <span class="">{{ $t('Decide_later') }}</span>
      <el-tooltip
        :content="$t('Enable_preparation_in_esign_tips')"
        placement="top"
        popper-class="create-flow-tooltip">
        <i
          class="micon-mep-info-square more-info"
          role="gridcell" />
      </el-tooltip>
    </div>
    <template  v-slot:error="{ error }">
    <AlertError
        class="file-picker-error"
      :message="error" />
    </template>
  </ElFormItem>
</template>
<script lang="ts">
import {
  getDefaultFormItemProps,
  SelectedFile
} from '@views/newActions/common/defines/formItems/vmToFilePickerFormItem.ts'
import {transformToComponentProps} from '@views/newActions/common/utils'
import {mapGetters} from 'vuex'
import {ObjectUtils} from '@commonUtils'
import {defineComponent} from '@vue/composition-api'
import {mapActions as piniaMapActions, mapState as piniaMapState} from 'pinia'
import {isNumber} from 'lodash'
import FilePicker from '@views/common/components/FilePicker/FilePicker.vue'
import AlertError from '@views/common/components/AlertError.vue'
import FilePickedFileView from './FilePickedFileView.vue'
import {BoardFileViewModel, useTempBoardStore} from '@views/stores/tempBoardStore'
import {ActionMode, ActionScene} from '@views/newActions/common/types'

const defaultProps = transformToComponentProps(getDefaultFormItemProps())

export default defineComponent({
  name: 'FilePickerFormItem',
  components: { FilePicker, FilePickedFileView, AlertError },
  props: {
    ...defaultProps
  },
  data () {
    const vm = this
    return {
      modelValue: this.value as SelectedFile,
      isLoading: false,
      localErrorMessage: '',
      rules: [
        {
          validator: (rule: any, value: SelectedFile, callback: Function) => {
            const referenceFile = value?.attachment?.referenceFile
            let needValidateFile = true
            if (vm.linkChannel.hasEnabledPreparation) {
              needValidateFile = false
              if (vm.actionContext.mode === ActionMode.PREPARE) {
                needValidateFile = true
              }
            }
            if (needValidateFile) {
              if (!referenceFile || !referenceFile.name) {
                return callback(
                  new Error(vm.$t('Please_upload_a_PDF_form_to_continue') as string)
                )
              } else if (!referenceFile.spath || !value.formViewModel) {
                return callback(new Error(vm.$t('This_file_is_still_uploading') as string))
              }
            }
            callback()
          },
          trigger: 'manual'
        }
      ]
    }
  },
  watch: {
    currentFile (fileModel: BoardFileViewModel) {
      if(!fileModel){
        return;
      }
      const error = fileModel.convertError
      if(error){
        this.clearErrors()
        let errorMessage =''
        if(error.isNoFormFieldsError){
          errorMessage =  this.$t('Form_fields_cannot_be_found_in_this_PDF_file')
        } else if(error.isInvalidPasswordError){
          errorMessage = this.$t('This_PDF_is_password_protected')
        } else {
          errorMessage = this.$t('convert_file_failed')
        }
        this.handleRemoveFile()
        this.localErrorMessage = errorMessage
      }else {
        if (fileModel.referenceFile) {
          this.modelValue.attachment ={
            referenceFile: fileModel.referenceFile
          }
          this.triggerChange()
        }
      }
    },
    formViewModel (model) {
      if (model) {
        model.updatedTime = Date.now()
          this.modelValue.formViewModel = model
          this.clearErrors()
        this.triggerChange()
      }

    }
  },
  computed: {
    ...piniaMapState(useTempBoardStore, [
      'storeIsReady',
      'uploadPercentage',
      'isInProgress',
      'formViewModel',
      'currentFile',
      'convertedPercentage'
    ]),
    ...mapGetters('privileges', ['maxFileSize', 'allowedFileTypesAll']),
    ...mapGetters('user', ['currentUser']),
    pickedFile () {
      return this.modelValue?.attachment?.referenceFile || {}
    },
    showDecideLater () {
      return this.actionContext.mode !== ActionMode.PREPARE && [ActionScene.IN_FLOW_TEMPLATE, ActionScene.IN_MILESTONE_TEMPLATE, ActionScene.IN_CREATE_FLOW_WORKSPACE].includes(this.actionContext.scene)
    },
    serverAllowedFileTypes () {
      if (this.allowedFileTypesAll) {
        return this.allowedFileTypesAll.join(',')
      } else {
        return ''
      }
    },
    allowMaxFileSize () {
      let maxBody = ObjectUtils.getByPath(this.currentUser, 'userCap.client_max_body_size')
      let maxCloud = ObjectUtils.getByPath(this.currentUser, 'userCap.user_cloud_max')
      if (maxBody) {
        maxBody = ObjectUtils.bytesToMB(maxBody)
      }
      if (maxCloud) {
        maxCloud = ObjectUtils.bytesToMB(maxCloud)
      }
      let maxFileSize = this.maxFileSize
      if (maxFileSize) {
        maxFileSize = parseInt(maxFileSize)
      }
      let sdkMaxSize = parseInt(this.maxFileSize)
      if (sdkMaxSize) {
        sdkMaxSize = ObjectUtils.bytesToMB(sdkMaxSize)
      }

      const sizes = [maxBody, maxCloud, maxFileSize, sdkMaxSize].filter((n) => isNumber(n) && n > 0)

      return Math.min(...sizes)
    },
    errorMessage () {
      return this.localErrorMessage || this.errorMsg
    }
  },
  async created () {
    await this.initTempBoardStore()
    if(this.defaultSelectedFile){
      this.handleSelectFile([this.defaultSelectedFile])
    }
  },
  beforeDestroy () {
    this.destroyTempBoardStore()
  },
  methods: {
    ...piniaMapActions(useTempBoardStore, [
      'uploadFile',
      'initTempBoardStore',
      'destroyTempBoardStore',
      'removeFile'
    ]),
    triggerChange () {
      this.$emit('input', this.modelValue)
      this.$emit('change', {
        prop: this.prop,
        value: this.modelValue
      })
    },
    handleRemoveFile () {
      this.modelValue = {} as SelectedFile
      this.triggerChange()
      this.removeFile()
      this.clearErrors()
    },
    async handleSelectFile (files) {
      this.clearErrors()
      const file = files[0]
      this.modelValue = {
        attachment: {
          referenceFile: {
            name: file.name,
            fileType: file.type,
            size: file.size,
            clientUuid: file.uuid,
            thumbnail: ''
          }
        }
      }
      this.triggerChange()
      if (this.storeIsReady) {
        this.isLoading = true
        try {
          await this.uploadFile(file)
        }catch (error) {
          this.handleError(error)
        }
        this.isLoading = false
        this.triggerChange()
      }
    },
    clearErrors () {
      this.$refs.formItem.clearValidate()
      this.localErrorMessage = ''
    },

    handleError (err) {
      if (err.fileCountExceed) {
        this.localErrorMessage = err.message
      } else if (err.fileSizeExceed) {
        this.localErrorMessage = this.$t('The_selected_file_exceeds_max_limit_size', {
          size: `${this.allowMaxFileSize}MB`
        }) as string
      } else if (err.fileTypeNotAllowed) {
        this.localErrorMessage = this.$t('Unsupported_file_type_Please_upload_a_PDF_to_proceed') as string
      } else if (err.isPasswordException) {
        this.localErrorMessage = this.$t('This_PDF_is_password-protected') as string
      } else {
        this.localErrorMessage = this.$t('Unable_to_upload_files')
        this.modelValue = {}
      }
    }
  }
})
</script>
<style lang="scss">
.file-picker-error {
  margin-top: 12px;
}

.decide-later-info-section {
  color: #616161;
  margin-top: 10px;
  i {
    color: #616161;
    font-size: 16px;
  }
}
</style>
