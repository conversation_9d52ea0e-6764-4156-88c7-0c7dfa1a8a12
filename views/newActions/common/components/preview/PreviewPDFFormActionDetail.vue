<template>
  <div
    class="preview-container">
    <FormRequestAction
      :baseObject="previewBaseObject"
      :stepModel="mockStepModel"
      :isPreview="true"
      :ctrlKey="ctrlKey"
      :class="{
        'default-title': previewBaseObject.isDefaultTitle,
        'default-description': previewBaseObject.isDefaultDescription }"
      class="transaction-object-detail"
      :config="actionDetailConfig"/>
  </div>
</template>

<script lang="ts">
import FormRequestAction from '@views/thread/src/component/baseObjects/FormRequestAction.vue'

import { ActionScene, ActionContext, ActionMode } from '@views/newActions/common/types'
import { mockTemplateTransaction } from '@views/contentLibrary/plugins/request/mockTemplateTransction'
import { IActionObjectViewModel } from '@model/baseObjects/defines/actionObjectViewModel'
import util from '@views/common/utils/utils'

import { defineComponent, PropType } from '@vue/composition-api'

import {Defines} from 'isdk'

export default defineComponent({
  name: 'PreviewPDFFormActionDetail',
  components: {
    FormRequestAction,
  },
  props: {
    actionContext: {
      type: Object as PropType<ActionContext>,
      default: () => ({})
    },
    actionModel: {
      type: Object as PropType<IActionObjectViewModel>,
      default: () => ({
        title: ''
      })
    }
  },
  data () {
    return {
      openPreview: false,
      curPreviewBoardId: '',
      previewOptions: {},
      ctrlKey: Date.now().toString()
    }
  },
  computed: {
    actionDetailConfig () {
      const actionScene = this.actionContext.scene
      if (actionScene === ActionScene.IN_NORMAL_WORKSPACE 
        || actionScene === ActionScene.IN_DIRECT_CREATE_ACTION_TO_NEW_NORMAL_WORKSPACE
        || actionScene === ActionScene.IN_ACTION_TEMPLATE
      ) {
        return {}
      } else {
        return {
          isNotStarted: !(this.actionModel.isNotStarted === false),
          notInFlowWorkspace: this.notInFlowWorkspace
        }
      }
    },
    previewBaseObject () {
      const actionModel = this.actionModel

      const stepGroup = actionModel.stepGroups[0]
      const isSubStepParallel = stepGroup.isParallel

      const entryParams: any = {
        transactionType: Defines.TransactionType.TRANSACTION_TYPE_PDF_FORM,
        boardFiles: this.getAttachments(),
        title: actionModel.title || `${this.$t('PdfForm_title')}...`,
        description: actionModel.description,
        stepGroups: isSubStepParallel ? actionModel.stepGroups : [],
        stepGroup: isSubStepParallel ? stepGroup : '',
        isSequential: !isSubStepParallel
      }

      if (actionModel.dueDate) {
        if (actionModel.dueDate > 631123200000) {
          const {isNear, date} = util.computeDueDate(actionModel.dueDate, {
            yesterday: this.$t('due_yesterday'),
            tomorrow: this.$t('due_tomorrow'),
            today: this.$t('Due_today')
          })

          if (isNear) {
            entryParams.dueDateText = date
          } else {
            entryParams.dueDateText = this.$t('due_on_date', {
              date: date
            })
          }
        } else {
          entryParams.dueDateTimestamp = actionModel.dueDate
          entryParams.dueInTimeframe = actionModel.dueInTimeframe
        }
      }

      if (stepGroup.steps.length) {
        const formatAssignee = stepGroup.steps.map(step => {
          const signee = step.assignee
          const obj: any = {
            id: signee.id,
            name: signee.name,
            avatar: signee.avatar
          }

          if (signee.isRole) {
            obj.isInternalUser = true
            obj.isRole = true
            obj.isAssigneeRole = true
          } else if (signee.isTeam) {
            const teamDetail = signee.team
            obj.isInternalUser = true
            obj.isTeam = true
            obj.isDeleted = teamDetail?.isDeleted
            obj.isUserDeleted = teamDetail?.isDeleted
            obj.isClientTeam = signee.team.isClientTeam
          } else {
            const userDetail = signee.user
            obj.disabled = userDetail?.isDisabled
            obj.isUserDeleted = userDetail?.isUserDeleted
            obj.isInternalUser = userDetail?.isInternalUser
          }

          return obj
        })
        entryParams.assignees = formatAssignee
      }
      
      return {
        ...mockTemplateTransaction(this.$t, entryParams),
        isDefaultTitle: !actionModel.title,
        isDefaultDescription: !actionModel.description,
        sub_type: actionModel.type,
        formViewModel: actionModel.actionData.formViewModel
      }
    },
    mockStepModel () {
      // let preparer = transformToFormItemAssignee(this.actionModel.editor)
      // if (!preparer?.id) {
      //   preparer = {
      //     avatar: '',
      //     name: this.$t('Preparer') as string,
      //     id: '',
      //     isInternalUser: true
      //   }
      // } else {
      //   if (!preparer.isClient && !preparer.isClientTeam) {
      //     preparer.isInternalUser = true
      //   }
      // }

      return {
        isNotStarted: !(this.actionModel.isNotStarted === false),
        enablePreparation: this.actionModel.enablePreparation,
        editor: this.actionModel.editor,
        isPreparing: this.actionModel.isPreparing,
        isPrepareMode: this.actionContext.mode === ActionMode.PREPARE,
        isFlowTemplate: this.actionContext.scene === ActionScene.IN_FLOW_TEMPLATE || this.actionContext.scene === ActionScene.IN_MILESTONE_TEMPLATE,
        isPrepareCompleted: this.actionModel.isPrepareCompleted
      }
    },
    notInFlowWorkspace () {
      return this.actionContext.scene !== ActionScene.IN_FLOW_WORKSPACE
    }
  },
  methods: {
    getAttachments () {
      const normalAttachments = this.actionModel.attachments || []
      const ddrAttachments = this.actionModel.ddrAttachments || []

      const transformedNormalAttachments = normalAttachments.map(attachment => {
        const attachmentDetail = attachment.referenceFile
        return {
          name: attachmentDetail.name,
          webLinkUrl: attachmentDetail.url,
          thumbnailUrl: attachmentDetail.thumbnail,
          boardId: attachmentDetail.boardId,
          viewToken: attachment.viewToken,
          SPath: attachmentDetail.spath,
          creator: attachmentDetail.creator,
          resourceSize: attachmentDetail.contentLength,
          fileType: attachmentDetail.fileType,
        }
      })

      const transformedDDRAttachments = ddrAttachments.map(attachment => {
        return {
          isDDRFile: true,
          ...attachment
        }
      })

      return [...transformedNormalAttachments, ...transformedDDRAttachments]
    },

        }
      })
</script>

<style scoped lang="scss">
.preview-container {
  display: flex;
  justify-content: center;
  min-height: 612px;

  .transaction-object-detail {
    background: white;
    border-radius: 6px;
    border: 1px solid $mx-color-var-card-border;
    width: 400px;
  }

  ::v-deep {
    header {
      display: block;
      padding: 0;
      height: auto;
      border-bottom: 0;
    }
  }

  .default-title {
    ::v-deep {
      .mx-transaction-title header {
        color: $mx-color-var-fill-primary;
      }
    }
  }

  .default-description {
    ::v-deep {
      .mx-transaction-subtitle {
        color: $mx-color-var-fill-primary;
      }
    }
  }

  .default-assignee {
    ::v-deep {
      .assignee-member li:first-child .status div:first-child {
        color: $mx-color-var-fill-primary;
      }
    }
  }
}
</style>
