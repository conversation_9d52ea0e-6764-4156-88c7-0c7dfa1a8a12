<script>
import { useWorkspacePropertiesStore } from '@views/stores/workspaceProperties.ts'
import { mapActions as piniaMapActions } from 'pinia'
import { showEditPropertiesDialog } from '../shared'
import { PropertyType } from 'isdk/src/api/defines'
import templateFormat from '@views/common/utils/formatter/templateFormat'

export default {
  name: 'WorkspacePropertiesDetail',
  props: {
    boardId: {
      type: String,
      default: '',
      required: true
    },
    showHeader: {
      type: Boolean,
      default: true
    },
    disableSubscribe: {
      type: Boolean,
      default: false
    },
    isNoAction: {
      type: Boolean,
      default: false
    },
  },
  data () {
    return {
      PropertyType: PropertyType,
      properties: [],
      loading: false,
      subscription: null
    }
  },
  beforeDestroy () {
    if (this.subscription) {
      this.subscription.unsubscribe()
      this.subscription = null
    }
  },
  created () {
    this.loading = true
    let subscribeCallback
    if(!this.disableSubscribe){
      subscribeCallback =  (items) => {
        this.properties = items
      }
    }
    this.readBoardProperties(this.boardId, subscribeCallback).then(([properties, subscription]) => {
      this.properties = properties
      this.subscription = subscription
      this.loading = false
    })
  },
  methods: {
    ...piniaMapActions(useWorkspacePropertiesStore, ['readBoardProperties']),
    close () {
      this.$emit('close')
    },
    textItemValue (value){
      if(!value) return '-'
      return templateFormat.linkMode(value)
    },
    moreOptionDropdownSelect () {
      showEditPropertiesDialog({ boardId: this.boardId })
    }
  }
}
</script>

<template>
  <div
    v-loading="loading"
    class="flow-detail">
    <header
      v-if="!loading && showHeader"
      class="mx-flex-container mx-thread-header">
      <div class="left-area">
        <div class="title mx-text-c1 mx-ellipsis mx-capitalize">
          {{ $t('Workspace_tags') }}
        </div>
      </div>
      <div class="right-area">
        <el-dropdown
          trigger="click"
          v-if="!isNoAction"
          class="mx-hover-hide more-info-dropdown"
          @command="moreOptionDropdownSelect">
          <span
            v-mx-ta="{ page: 'properties', id: `more_options` }"
            :title="$t('more_options')"
            :data-original-title="$t('more_options')"
            class="mx-clickable el-dropdown-link">
            <i class="micon-more" />
          </span>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item
              data-ta="edit_property"
              command="editProperty">
              <span>{{ $t('Edit_tags') }}</span>
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
        <span
          v-mx-ta="{ page: 'properties', id: `close` }"
          class="mx-clickable right-close-button"
          role="button"
          :aria-label="$t('close')"
          tabindex="0"
          @keydown.enter="close"
          @click="close">
          <i class="micon-close" />
        </span>
      </div>
    </header>
    <div class="property-content">
      <div
        v-for="item in properties"
        :key="item.sequence"
        class="properties-item">
        <div
          v-safe-text="item.name"
          class="property-label mx-ellipsis" />
        <el-tooltip 
          v-if="item.type === PropertyType.PROPERTY_TYPE_TEXT"
          class="mx-ellipsis"
          popper-class="workspace-variable-item-tip"
          :content="item.value || '-'"
          effect="dark"
          placement="top">
          <div
            v-safe-html="textItemValue(item.value)"
            class="text-property-value mx-ellipsis-3line property-value" />
        </el-tooltip>
        
        <el-tooltip 
          v-else
          class="mx-ellipsis"
          popper-class="workspace-variable-item-tip"
          :content="item.value || '-'"
          effect="dark"
          placement="top">
          <div
            v-safe-text="item.value || '-'"
            class="property-value" />
        </el-tooltip>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.text-property-value {
  cursor: pointer;
}

.text-property-value a {
  color: #1A69D1;
  text-decoration: underline;
}

.mx-ellipsis-3line {
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}
.flow-detail {
  height: 100%;
}

.flow-detail {
  overflow: hidden;
  height: 100%;
  display: flex;
  flex-direction: column;

  > header,
  .header {
    flex: 0 0 auto;
    border-bottom: 1px solid $mx-color-var-fill-tertiary;
    display: flex;
    align-items: center;
    height: 52px;
    padding-left: 20px;
    padding-right: 16px;

    > :first-child:not(.back-icon) {
      flex: 1 1 auto !important;
    }

    .left-area {
      > span {
        margin-right: $mx-spacing-sm;
      }

      flex: 1 1 auto;
      display: flex;
      flex-direction: column;
      max-width: calc(100% - 74px);

      .time {
        color: $mx-color-var-label-secondary;
        margin-top: -2px;
      }
    }

    .right-area {
      flex: 1 1 auto;
      display: flex;
      justify-content: flex-end;

      .right-close-button {
        color: $mx-color-var-label-secondary;
        display: inline-block;
        width: 28px;
        height: 28px;
        text-align: center;
        border-radius: 6px;

        i {
          line-height: 28px;
          font-size: 16px;
        }
      }

      .right-close-button {
        background-color: $mx-color-var-fill-quaternary;
        margin-left: 12px;

        .close-button {
          font-size: 16px;
          color: $mx-color-var-label-secondary;
        }
      }

      .right-close-button-non-scope {
        padding: 12px;
        color: $mx-color-var-label-secondary;
        display: inline-block;
      }
    }
  }

  .property-content {
    padding: 20px;
    overflow: auto;

    .properties-item {
      width: 100%;
      display: block;
      margin-bottom: 20px;

      .property-label {
        color: $mx-color-var-label-secondary;
        font-size: 12px;
        font-weight: 400;
        line-height: 16px;
        text-align: left;
      }

      .property-value {
        white-space: pre-line;
        font-size: 14px;
        font-weight: 400;
        line-height: 20px;
        text-align: left;
      }
    }
  }
}
</style>
