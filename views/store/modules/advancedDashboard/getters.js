import { defaultWorkspaceConfig, defaultActionConfig } from '@views/common/appConst'
import { ObjectUtils } from "@controller/utils"
import i18n from '@views/i18n/mepIndex'
import moment from 'moment-timezone'

export default {
    getWorkspaces(state) {
        return state.workspaces
    },
    getActions(state) {
        return state.actions
    },
    getSavedWorkspaceConfigs(state) {
        return state.savedWorkspaceConfigs
    },
    getSavedActionConfigs(state) {
        return state.savedActionConfigs
    },
    getShowSpinner(state) {
        return state.showSpinner
    },
    getActionInProgress(state) {
        return state.actionInProgress
    },
    getTotalRecords(state) {
        return state.totalRecords
    },
    getSearchSize(state) {
        return state.searchSize
    },
    getCurrentPage(state) {
        return state.currentPage
    },
    getActiveTab(state) {
        return state.activeTab
    },
    getWorkspaceConfig(state) {
        return state.workspaceConfig
    },
    getActionConfig(state) {
        return state.actionConfig
    },
    getDashboardType(state) {
        return state.dashboardType
    },
    getTemplateList(state) {
        return state.templateList
    },
    getTotalTemplateListRecords(state) {
        return state.totalTemplateListRecords
    },
    getMilestoneList(state) {
        return state.milestoneList
    },
    getTotalMilestoneListRecords(state) {
        return state.totalMilestoneListRecords
    },
    getTemplateStepList(state) {
        return state.templateStepList
    },
    getTotalTemplateStepListRecords(state) {
        return state.totalTemplateStepListRecords
    },
    getWorkspaceOwnerList(state) {
        return state.workspaceOwnerList
    },
    getWorkspaceMemberList(state) {
        return state.workspaceMemberList
    },
    getTotalUsersListRecords(state) {
        return state.totalUsersListRecords
    },
    getHasMilestone(state) {
        return state.hasMilestone
    },
    getHasStep(state) {
        return state.hasStep
    },
    getCachedFilterConfig(state) {
        return state.cachedFilterConfig
    },
    getWorkspaceOwnerLoading(state) {
        return state.workspaceOwnerLoading
    },
    getWorkspaceMemberLoading(state) {
        return state.workspaceMemberLoading
    },
    getTemplatesLoading(state) {
        return state.templatesLoading
    },
    getMilestonesLoading(state) {
        return state.milestonesLoading
    },
    getCurrentStepsLoading(state) {
        return state.currentStepsLoading
    },
    areAdvancedFiltersApplied(state, getters, rootState, rootGetters) {
        return getters.currentTabConfig?.filter_config?.advanced_params?.line_items?.length && getters.currentTabConfig?.filter_config?.advanced_params?.line_items?.some(obj =>
            ["filter", "condition", "value"].some(key => obj[key] !== '' && obj[key] !== null && obj[key] !== undefined)
        ) || false
    },
    areQuickFiltersApplied(state, getters, rootState, rootGetters) {
        return getters.currentTabConfig?.filter_config?.quick_params.some(item => !!item.string_value)
    },
    getWorkspaceTagProperties(state, getters, rootState, rootGetters) {
        const boardProperties = rootGetters['group/groupBasicInfo']?.board_properties || []
        return boardProperties
            .filter(item => item && !item.is_deleted && item.name)
            .sort((a, b) => (Number(a?.order_number) || 0) - (Number(b?.order_number) || 0))
            .map(item => ({
                ...item,
                label: item.name,
                name: String(item.sequence),
                value: String(item.sequence),
                tag_type: item.type,
                type: 'workspace_tags',
                ui_type: 'workspace_tags',
                width: '200',
                visible: true,
                slot: true
            }))
    },
    isFilterColumnsDefault(state, getters, rootState, rootGetters) {
        const workspaceBoardProperties = getters.getWorkspaceTagProperties
        const constWorkspaceConfig = JSON.parse(JSON.stringify(defaultWorkspaceConfig))
        const completeColumnConfig = [
            ...constWorkspaceConfig.column_config,
            ...workspaceBoardProperties
        ]

        const configs = {
            advancedWorkspaceReport: completeColumnConfig,
            advancedActionReport: JSON.parse(JSON.stringify(defaultActionConfig.column_config))
        }

        const currentConfig = configs[state.dashboardType] || []
        return currentConfig
            ? ObjectUtils.isEqual(getters.currentTabConfig?.column_config, currentConfig)
            : true
    },
    currentTabConfig(state) {
        if (state.dashboardType === 'advancedWorkspaceReport') {
            const fullconfig = [...state.workspaceConfig]
            return fullconfig.find(item => item.view_seq === state.activeTab)
        } else if (state.dashboardType === 'advancedActionReport') {
            const fullconfig = [...state.actionConfig]
            return fullconfig.find(item => item.view_seq === state.activeTab)
        }
    },
    currentTabSavedConfig(state) {
        if (state.dashboardType === 'advancedWorkspaceReport') {
            const fullconfig = [...state.savedWorkspaceConfigs]
            return fullconfig.find(item => item.view_seq === state.activeTab)
        } else if (state.dashboardType === 'advancedActionReport') {
            const fullconfig = [...state.savedActionConfigs]
            return fullconfig.find(item => item.view_seq === state.activeTab)
        }
    },
    isSaveToThisViewDisabled(state, getters, rootState, rootGetters) {
        const currentConfig = getters.currentTabConfig
        let isDisabled = true

        const getSortedConfig = (config) => {
            let sorted = null
            if (config) {
                sorted = JSON.parse(JSON.stringify(config))

                sorted.filter_config?.advanced_params?.line_items?.sort((a, b) =>
                    a.filter.localeCompare(b.filter)
                )

                sorted.filter_config?.quick_params?.sort((a, b) =>
                    a.name.localeCompare(b.name)
                )
                sortFilters(sorted.filter_config?.quick_params, sorted.filter_config?.advanced_params)
            }
            return sorted
        }

        const savedConfigs = state.dashboardType === 'advancedWorkspaceReport'
            ? state.savedWorkspaceConfigs
            : state.savedActionConfigs

        const savedConfigObj = savedConfigs.find(item => item.view_seq === state.activeTab)
        const savedConfigSorted = getSortedConfig(savedConfigObj)
        const currentConfigSorted = getSortedConfig(currentConfig)

        // Compare the sorted configurations
        isDisabled = ObjectUtils.isEqual(currentConfigSorted, savedConfigSorted)

        if (getters.currentTabConfig.state == 'DRAFT') {
            isDisabled = false
        }

        return isDisabled
    },
    isSaveAsNewDisabled(state, getters, rootState, rootGetters) {
        if (state.dashboardType === 'advancedWorkspaceReport') {
            if (state.workspaceConfig.length > 5) {
                return true
            }
            else {
                const theDefaultViewConfig = state.workspaceConfig.find((item) => item.view_seq === 'mepx_dashboard_default_tab')
                const defaultWorkspaceConfigJSON = JSON.parse(JSON.stringify(defaultWorkspaceConfig))
                defaultWorkspaceConfigJSON.date_range = theDefaultViewConfig.date_range
                defaultWorkspaceConfigJSON.order_no = theDefaultViewConfig.order_no
                const workspaceBoardProperties = getters.getWorkspaceTagProperties

                if (workspaceBoardProperties && workspaceBoardProperties.length) {
                    defaultWorkspaceConfigJSON.column_config.push(...workspaceBoardProperties)
                }
                if (theDefaultViewConfig.filter_config.quick_params.length !== defaultWorkspaceConfigJSON.filter_config.quick_params.length) {
                    syncQuickParams(theDefaultViewConfig, defaultWorkspaceConfigJSON)
                }

                const lastYearStartMs = moment()
                    .subtract(12, 'months')
                    .add(1, 'day')
                    .startOf('day')
                    .valueOf()
                const nowDateTime = moment().endOf('day').valueOf()

                defaultWorkspaceConfigJSON.date_range = lastYearStartMs + '-' + nowDateTime
                sortFilters(theDefaultViewConfig.filter_config.quick_params, theDefaultViewConfig.filter_config.advanced_params)
                sortFilters(defaultWorkspaceConfigJSON.filter_config.quick_params, defaultWorkspaceConfigJSON.filter_config.advanced_params)
                const areEqual = ObjectUtils.isEqual(theDefaultViewConfig, defaultWorkspaceConfigJSON)
                if (areEqual) {
                    return true
                }
                else {
                    return false
                }
            }
        }
        else if (state.dashboardType === 'advancedActionReport') {
            if (state.actionConfig.length > 5) {
                return true
            }
            else {
                const defaultActionConfigObj = JSON.parse(JSON.stringify(defaultActionConfig))
                const theDefaultViewConfig = state.actionConfig.find((item) => item.view_seq === 'mepx_dashboard_default_tab')
                defaultActionConfigObj.date_range = theDefaultViewConfig.date_range
                defaultActionConfigObj.order_no = theDefaultViewConfig.order_no
                if (theDefaultViewConfig.filter_config.quick_params.length !== defaultActionConfigObj.filter_config.quick_params.length) {
                    syncQuickParams(theDefaultViewConfig, defaultActionConfigObj)
                }

                const lastYearStartMs = moment()
                    .subtract(12, 'months')
                    .add(1, 'day')
                    .startOf('day')
                    .valueOf()
                const nowDateTime = moment().endOf('day').valueOf()

                defaultActionConfigObj.date_range = lastYearStartMs + '-' + nowDateTime
                sortFilters(theDefaultViewConfig.filter_config.quick_params, theDefaultViewConfig.filter_config.advanced_params)
                sortFilters(defaultActionConfigObj.filter_config.quick_params, defaultActionConfigObj.filter_config.advanced_params)
                const areEqual = ObjectUtils.isEqual(theDefaultViewConfig, defaultActionConfigObj)
                if (areEqual) {
                    return true
                }
                else {
                    return false
                }
            }
        }
        else {
            return true
        }
    },
    getKanbanData(state, getters, rootState, rootGetters) {
        if (!state.kanbanData || typeof state.kanbanData !== 'object') return []
        const arr = Object.keys(state.kanbanData).filter((key) => !['total_records', 'total_records_by_kanban_column'].includes(key))

        let possibleValues = state.kanbanData.total_records_by_kanban_column
        const hasNULL = possibleValues && possibleValues.hasOwnProperty('NULL')
        const { dashboardType } = state
        const { kanban_column } = getters?.currentTabConfig || {}

        const statusFieldName = dashboardType === 'advancedWorkspaceReport' ? 'workspace_status' : 'action_status'

        if (kanban_column === statusFieldName) {
            possibleValues = dashboardType === 'advancedWorkspaceReport'
                ? { open: 0, due_today: 0, overdue: 0, completed: 0 }
                : { open: 0, due_today: 0, due_tomorrow: 0, due_in_7_days: 0, overdue: 0, completed: 0 }
        } else if (kanban_column === 'workspace_type') {
            possibleValues = { GROUP_BINDER: 0, FLOW_BINDER: 0, DIRECT_BINDER: 0 }
        }
        else if (!isNaN(kanban_column)) {
            // Workspace Tags - Get options from selected workspace tag
            const workspaceBoardProperties = getters.getWorkspaceTagProperties
            const filteredProps = workspaceBoardProperties.find(item => item.sequence === kanban_column)
            possibleValues = Object.fromEntries(filteredProps?.options.map(key => [Number(key) ? key + 'd@$hbo@rd' : key, 0]) || [])
            if (hasNULL) {
                possibleValues.NULL = 0
            }
        }
        const kanbanList = state.kanbanData[arr[0]] ? [...state.kanbanData[arr[0]]] : []
        const acc = state.kanbanData.total_records_by_kanban_column && Object.keys(possibleValues).map(key => {
            key = key.replace('d@$hbo@rd', '')
            let list = []
            if (arr.length && key != 'NULL') {
                list = kanbanList.filter(w => String(w[String(getters?.currentTabConfig.kanban_column)]?.toLowerCase()) === String(key.toLowerCase()))
                list.forEach(item => {
                    const index = kanbanList.indexOf(item)
                    if (index > -1) kanbanList.splice(index, 1)
                })
            } else {
                list = kanbanList
            }
            return {
                title: key != 'NULL' ? key : getters.currentTabConfig?.kanban_column,
                type: key == 'NULL' ? 'NO_TAG' : 'OTHERS',
                total_records: state.kanbanData.total_records_by_kanban_column[Object.keys(state.kanbanData.total_records_by_kanban_column || {}).find(k => k.toLowerCase() === key.toLowerCase())] || 0,
                list
            }
        })

        if (!acc) return []

        const resultArr = [
            ...acc.filter(item => item.type !== 'NO_TAG'),
            ...acc.filter(item => item.type === 'NO_TAG')
        ]

        if (getters?.currentTabConfig?.kanban_column_order?.length) {
            return resultArr.sort(
                (a, b) => getters.currentTabConfig.kanban_column_order.indexOf(a.title) -
                    getters.currentTabConfig.kanban_column_order.indexOf(b.title)
            )
        }
        return resultArr
    },
    advanceFilterList(state, getters, rootState, rootGetters) {
        const advanceFilterList = [
            {
                label: i18n.t('workspace_status'),
                value: 'workspace_status',
                type: 'dropdown',
                ui_type: 'filter'
            },
            {
                label: i18n.t('Conversation_Type'),
                value: 'workspace_type',
                type: 'dropdown',
                ui_type: 'filter'
            },
            {
                label: i18n.t('Template'),
                value: 'template_id',
                type: 'dropdown',
                ui_type: 'filter'
            },
            {
                label: i18n.t('client_workspace'),
                value: 'external_only',
                type: 'dropdown',
                ui_type: 'filter'
            },
            {
                label: i18n.t('conversation_owner'),
                value: 'workspace_owner_id',
                type: 'dropdown',
                ui_type: 'filter'
            },
            {
                label: i18n.t('workspace_member'),
                value: 'workspace_member_id',
                type: 'dropdown',
                ui_type: 'filter'
            },
            {
                label: i18n.t('start_date'),
                value: 'start_date',
                type: 'date',
                ui_type: 'filter'
            },
            {
                label: i18n.t('Overdue'),
                value: 'overdue',
                type: 'days',
                ui_type: 'filter'
            }
        ]

        if (state.dashboardType === 'advancedActionReport') {
            advanceFilterList.push({
                label: i18n.t('action_status'),
                value: 'action_status',
                type: 'dropdown',
                ui_type: 'filter'
            })

            advanceFilterList.push({
                label: i18n.t('assigned_to'),
                value: 'assigned_to',
                type: 'dropdown',
                ui_type: 'filter'
            })
        }

        if (state.hasMilestone) {
            advanceFilterList.push({
                label: i18n.t('current_milestone'),
                value: 'template_milestone_seq',
                type: 'dropdown',
                ui_type: 'filter'
            })
        }

        if (state.hasStep) {
            advanceFilterList.push({
                label: i18n.t('current_step'),
                value: 'template_step_seq',
                type: 'dropdown',
                ui_type: 'filter'
            },)
        }

        const actionTypes = [
            {
                label: i18n.t('Assigned_to_Me'),
                value: 'assigned_to_me',
                type: 'dropdown',
                ui_type: 'filter'
            },
            {
                label: i18n.t('action_type'),
                value: 'action_type',
                type: 'dropdown',
                ui_type: 'filter'
            }]

        const dividerArray = []

        const workspaceBoardProperties = getters.getWorkspaceTagProperties

        if (workspaceBoardProperties.length) {
            dividerArray.push({
                label: '',
                value: 'workspace_tag_divider',
                type: 'workspace_tag_divider',
                ui_type: 'workspace_tag_divider'
            })
        }

        let completeadvanceFilterList = []
        const order = ["workspace_status", "action_status", "workspace_type", "external_only", "assigned_to_me", "owned_by_me", "workspace_owner_id", "workspace_member_id", "assigned_to", "template_id", "template_milestone_seq", "template_step_seq", "last_activity", "action_type", "start_date", "overdue"]
        if (state.dashboardType === 'advancedWorkspaceReport') {
            advanceFilterList.push(
                ...[
                    {
                        label: i18n.t('owned_by_me'),
                        value: 'owned_by_me',
                        type: 'dropdown',
                        ui_type: 'filter'
                    },
                    {
                        label: i18n.t('Last_Activity'),
                        value: 'last_activity',
                        type: 'days',
                        ui_type: 'filter'
                    }
                ]
            )

            completeadvanceFilterList = [
                ...advanceFilterList.sort((a, b) => {
                    const indexA = order.indexOf(a.value)
                    const indexB = order.indexOf(b.value)
                    if (indexA === -1 && indexB === -1) {
                        return a.value.localeCompare(b.value)
                    }
                    if (indexA === -1) return 1
                    if (indexB === -1) return -1
                    return indexA - indexB
                }),
                ...dividerArray,
                ...workspaceBoardProperties
            ]
        }
        else {
            completeadvanceFilterList = [...advanceFilterList, ...actionTypes].sort((a, b) => {
                const indexA = order.indexOf(a.value)
                const indexB = order.indexOf(b.value)
                if (indexA === -1 && indexB === -1) {
                    return a.value.localeCompare(b.value)
                }
                if (indexA === -1) return 1
                if (indexB === -1) return -1
                return indexA - indexB
            })
            completeadvanceFilterList.push(...dividerArray, ...workspaceBoardProperties)
        }

        return completeadvanceFilterList
    },
    getSelectedTemplateRelatedFilterData(state, getters, rootState, rootGetters) {
        const data = {
            template_id: state?.selectedTemplateRelatedFilterData?.template_id,
            template_milestone_seq: state?.selectedTemplateRelatedFilterData?.template_milestone_seq,
            template_step_seq: state?.selectedTemplateRelatedFilterData?.template_step_seq,
            reload_step: state?.selectedTemplateRelatedFilterData?.reload_step
        }
        return data
    },
    getAppliedFilterCount(state, getters) {
        let count = 0;

        const { filter_config } = getters.currentTabConfig || {};
        const { applied_filter_type, quick_params = [], advanced_params = {} } = filter_config || {};

        if (applied_filter_type === 'quick') {
            count = quick_params.filter(item => {
                const val = item.string_value;
                return val !== '' && val !== null && val !== undefined && val !== 'false' && val !== false;
            }).length;
        } else {
            count = (advanced_params.line_items || []).filter(item => {
                const val = item.value;
                return val !== '' && val !== null && val !== undefined;
            }).length;
        }

        count++;
        if (getters.currentTabConfig?.view_type === 'MANAGER') {
            count++;
        }

        return `${i18n.t('Filters')}(${count})`;
    },
    myViewMembers(state) {
        return state.myViewMembers
    },
    myViewMembersHasNextPage(state) {
        return state.myViewMembersHasNextPage
    },
    myViewOwners(state) {
        return state.myViewOwners
    },
    myViewOwnersHasNextPage(state) {
        return state.myViewOwnersHasNextPage
    },
    managerViewMembers(state) {
        return state.managerViewMembers
    },
    managerViewOwners(state) {
        return state.managerViewOwners
    },
    adminViewMembers(state) {
        return state.adminViewMembers
    },
    adminViewMembersHasNextPage(state) {
        return state.adminViewMembersHasNextPage
    },
    adminViewOwners(state) {
        return state.adminViewOwners
    },
    getQuickFilters(state) {
        return state.quickFilter
    },
    getAdvancedFilter(state) {
        return state.advancedFilter
    },
    adminViewOwnersHasNextPage(state) {
        return state.adminViewOwnersHasNextPage
    },
    currentTemplateId(state) {
        const filterType = state.inProgressFilterConfig.filterType;

        return filterType === 'quick'
            ? state.quickFilter.template_id
            : state.advancedFilter?.line_items?.find(({ filter, value }) => filter === 'template_id' && value)?.value;
    },
    currentMilestoneId(state) {
        const filterType = state.inProgressFilterConfig.filterType;

        return filterType === 'quick'
            ? state.quickFilter.template_milestone_seq
            : state.advancedFilter?.line_items?.find(({ filter, value }) => filter === 'template_milestone_seq' && value)?.value;
    },
    currentStepId(state) {
        const filterType = state.inProgressFilterConfig.filterType;

        return filterType === 'quick'
            ? state.quickFilter.template_step_seq
            : state.advancedFilter?.line_items?.find(({ filter, value }) => filter === 'template_step_seq' && value)?.value;
    },
    nextTeamClientPage(state) {
        const pageSize = 20
        const currentPage = Math.ceil(state.managerViewMembers.length / pageSize)
        const hasMorePages = state.teamClientCount > state.managerViewMembers.length
        if (hasMorePages) {
            return currentPage + 1
        }
        return false
    }
}

function syncQuickParams(config, defaultConfigJSON) {
    let quickParams = config.filter_config.quick_params
    let defaultQuickParams = defaultConfigJSON.filter_config.quick_params

    if (quickParams.length !== defaultQuickParams.length) {
        const existingKeys = new Set(quickParams.map(param => param.name)) // Assuming each param has a unique 'key'

        // Add only missing items from defaultQuickParams
        defaultQuickParams.forEach(param => {
            if (!existingKeys.has(param.name) && !param.string_value) {
                quickParams.push(param)
            }
        })
    }
}

function sortFilters(quick_params, advanced_params) {
    quick_params?.forEach(param => {
        if (typeof param.string_value === 'string') {
            param.string_value = param.string_value.split(',').sort((a, b) => a.localeCompare(b)).join(',')
        }
    })
    advanced_params?.line_items.forEach(param => {
        if (typeof param.value === 'string') {
            param.value = param.value.split(',').sort((a, b) => a.localeCompare(b)).join(',')
        }
    })
}