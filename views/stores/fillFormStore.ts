import { defineStore } from 'pinia'
import Vue from 'vue'
import { FormController } from '@newController/form/formControllerNewImpl'
import { CBoardUser } from '@controller/defines/CBoardUser'
import { MxLogger } from '@commonUtils'
import { MxISDK } from 'isdk'
import { isMyTeam } from '@newController/utils'
import { getBaseColor } from '@commonUtils/color'
import { ISubscription } from '@newController/defines/common'
import { FormViewModel } from '@model/form/defines/formViewModel'
import {
  ElementFilledFieldValueModel,
  ElementFilledStatusViewModel,
  ElementFilledValueViewModel,
  FormUserStateType
} from '@model/form/defines/state'
import {
  FormElementId,
  FormElementTransformOption,
  FormElementType,
  FormValidateOptions,
  UserId
} from '@model/form/defines/shared'
import { FormElementEventParams } from '../form/common/types'
import { applyUserFilledValues } from '@model/form/transform/form'
import { FormElementPageModel } from '@model/form/defines/FormPage'
import { useBoardCommonActionsStore } from '@views/stores/boardCommonActions'
import { calculateElementVisibleByCondition } from '@model/form/common/condition'
import { CCustomError } from '@controller/common/CCustomError'
import cloneDeep from 'lodash/cloneDeep'
import { sendToCallbackUrl } from '@controller/binder'
import { isMultipleAssignee } from '@model/form/common/utils'
import { FileUploadValue } from '@model/form/defines/FormFileUpload'
import moment from 'moment-timezone'
import FormFactoryManager from '@model/form/factory'
import pick from 'lodash/pick'
import { FormAnyElementValue, FormElementAnyInputModel } from '@model/form/defines/allType'
import {
  removeTransactionResource,
  uploadSignatureToTransaction
} from '@controller/contentLibrary/src/form'
import { FormElementSignatureModel } from '@model/form/defines/FormSignature'
import { isDefine } from '@model/form/factory/shared'
import { StepAssigneeBaseInfo } from '@newController/defines/formControllerNew'
import { TransactionController } from '@newController/board/transactionControllerImpl'
import { canShowProtected } from '@model/form/utils'

const Logger = MxLogger.create('FillForm')

interface FillFormState {
  /**
   *
   */
  formViewModel: FormViewModel
  /**
   * The form element currently focused by the user.
   */
  focusedElement: Partial<ElementFilledFieldValueModel>
  /**
   * user has unsaved changes
   */
  hasUnsavedChanges: boolean
  /**
   * the form base data has changed after user enter the fill form page
   */
  formHasUpdated: boolean
  /**
   * Data is currently being submitted.
   */
  inSaving: boolean
  /**
   * form is in submitting
   */
  isSubmitting: boolean
  /**
   * The form elements are currently being saved.
   */
  submittingElements: Map<FormElementId, Partial<ElementFilledFieldValueModel>>
  /**
   * The list of form elements currently focused by the online users.
   */
  filledStatus?: Map<UserId, ElementFilledStatusViewModel>
  /**
   * The form element data entered by the user.
   * For form data where multiple users fill in the same field, only the most recently updated data should be retained
   */
  filledValues?: Map<FormElementId, ElementFilledValueViewModel>
  myFilledValues?: Map<FormElementId, ElementFilledValueViewModel>
  members?: Map<UserId, CBoardUser>
  currentUserId?: UserId
  urlCallbackInfo?: any
  subscription?: ISubscription
  currentAssigneeOfStep: StepAssigneeBaseInfo
  uploadingElements: Map<FormElementId, Partial<ElementFilledFieldValueModel>>
}

interface FormAssigneeUser {
  name: string
  assigneeId: string
  color: string
  avatar: string
}

export const useFillFormStore = defineStore('fillFormStore', {
  state: (): FillFormState => {
    return {
      formViewModel: null,
      focusedElement: null,
      hasUnsavedChanges: false,
      formHasUpdated: false,
      inSaving: false,
      submittingElements: new Map(),
      filledStatus: new Map(),
      filledValues: new Map(),
      myFilledValues: new Map(),
      currentAssigneeOfStep: null,
      isSubmitting: false,
      currentUserId: null,
      urlCallbackInfo: null,
      members: new Map(),
      subscription: null,
      uploadingElements: new Map()
    }
  },
  getters: {
    onlineAssignees(state) {
      const exist = []
      const onlineAssignees: ElementFilledStatusViewModel[] = Array.from(
        state.filledStatus.values()
      ).filter((model) => {
        if (!model.isFilling) {
          return false
        }
        if (exist.includes(model.assigneeId)) {
          return false
        }
        exist.push(model.assigneeId)
        if (model.assigneeId == this.currentUserId) {
          //fix first enter the current user not in online list
          model.isFilling = true
          model.updatedTime = Date.now()
        }
        return model.isFilling && Date.now() - model.updatedTime < 60 * 1000 * 5
      })
      return onlineAssignees.map((model) => {
        const user = this.members.get(model.assigneeId)
        return {
          ...model,
          name: user.displayName || user.name,
          color: getBaseColor(model.orderIndex),
          avatar: user.avatar,
          assigneeId: user.id
        }
      })
    },
    canSeeProtectedValue() {
      return canShowProtected(this.formViewModel)
    },
    isMultipleAssignee() {
      return isMultipleAssignee(this.formViewModel)
    },
    currentUser() {
      return this.members.get(this.currentUserId)
    },
    submittedAssignee() {
      return this.members.get(this.formViewModel?.submittedAssigneeId)
    },
    currentAssignee() {
      return (
        this.formViewModel.assignees.find((assignee) => {
          if (assignee.id === this.currentUserId) {
            return true
          }
          if (assignee.isTeam && isMyTeam(assignee.id)) {
            return true
          }
        }) || {}
      )
    },
    userTimezone() {
      return MxISDK.getCurrentUser().basicInfo.timezone || moment.tz.guess()
    },
    formTransformOption(): FormElementTransformOption {
      return {
        userTimezone: this.userTimezone,
        boardId: this.formViewModel?.boardId,
        transactionSequence: this.formViewModel?.transactionSequence
      }
    }
  },
  actions: {
    initFillFormStore(boardId: string, transactionSequence: number, onError) {
      this.currentUserId = MxISDK.getCurrentUser().id
      return new Promise(async (resolve, reject) => {
        this.subscription = await FormController.subscribeForm(
          boardId,
          transactionSequence,
          async ({
            isFirstTime,
            isFormDeleted,
            filledStatus,
            filledValues,
            uploadedFiles,
            members,
            myFilledValues,
            formViewModel,
            stepOfCurrentUser,
            urlCallbackInfo
          }) => {
            if (!this.formViewModel) {
              this.formViewModel = formViewModel
            }
            if (isFormDeleted) {
              onError(new CCustomError('form is deleted', { formIsDeleted: true }))
              return
            }
            if (isFirstTime) {
              this.currentAssigneeOfStep = stepOfCurrentUser
              if (formViewModel.hasChangeAfterLastView) {
                await FormController.updateViewTime(
                  boardId,
                  transactionSequence,
                  stepOfCurrentUser.stepSequence
                )
              }
              if (!formViewModel.isCompleted) {
                await FormController.autoFillForm({
                  boardId,
                  transactionSequence,
                  formViewModel,
                  stepSequence: stepOfCurrentUser.stepSequence,
                  myFilledValues
                })
              }
            }
            if (formViewModel.isCompleted) {
              this.formViewModel = formViewModel
              onError(new CCustomError('form is completed', { formIsCompleted: true }))
              return
            }
            if (formViewModel.hasChangeAfterLastView) {
              onError(new CCustomError('form has changed', { contentUpdated: true }))
              return
            }
            if (this.isSubmitting) {
              return
            }
            if (filledStatus) {
              this.filledStatus = filledStatus
            }
            if (filledValues) {
              this.filledValues = this.handleInProcessFiles(filledValues, uploadedFiles)
            }

            /**
             * subscribeForm just return the original template, we need populate user-filled data to the element model
             */
            applyUserFilledValues(
              this.formViewModel,
              filledValues,
              this.submittingElements,
              this.focusedElement,
              this.currentUserId,
              this.formTransformOption
            )

            calculateElementVisibleByCondition(this.formViewModel)
            // this.formViewModel = formViewModel
            if (myFilledValues) {
              this.myFilledValues = myFilledValues
            }
            this.members = members

            this.urlCallbackInfo = urlCallbackInfo
            if (isFirstTime) {
              resolve()
            }
          }
        )
      })
    },
    getTheUpdatedValue({
      element,
      currentValue,
      changedProperty,
      affectedProperties
    }: FormElementEventParams) {
      let elementValue: FormAnyElementValue = cloneDeep(element.value)

      if (changedProperty) {
        elementValue[changedProperty] = currentValue[changedProperty]
      } else {
        elementValue = cloneDeep(currentValue)
      }
      if (affectedProperties) {
        elementValue = {
          ...elementValue,
          ...pick(currentValue, affectedProperties)
        } as FormAnyElementValue
      }
      return elementValue
    },
    markChange(event: FormElementEventParams) {
      this.hasUnsavedChanges = true

      const elementValue = this.getTheUpdatedValue(event)
      Logger.debug('markChange:', event)
      /**
       * In the singleSelection component, focus and blur events occur simultaneously.
       * To avoid sending redundant requests, we combine the state and value storage in the blur event.
       * As a result, the component does not trigger the focus event.
       * We need to update the focusedElement in the change event.
       *
       */
      const { element, changedProperty } = event
      const focusedEl = this.focusedElement || {}
      if (
        focusedEl.elementId !== element.id ||
        (focusedEl.elementId === element.id && focusedEl.attrName !== changedProperty)
      ) {
        this.focusedElement = {
          elementId: element.id,
          attrName: changedProperty,
          value: cloneDeep(elementValue)
        }
        Logger.debug('markChange: change focusedElement')
      }
      console.debug('elementValue', elementValue)
      element.value = elementValue
      if (!this.formViewModel.isPDFForm) {
        calculateElementVisibleByCondition(this.formViewModel)
      }
      // clear error
      if (element.errors.length) {
        element.errors = element.errors.filter((err) => err.field !== event.changedProperty)
      }
    },
    setUserOnlineStatus(isOnline: boolean) {
      if (this.isSubmitting || this.formViewModel?.isCompleted) {
        return
      }
      let stateModel = this.filledStatus.get(this.currentUserId)
      if (stateModel) {
        stateModel.focusElement = ''
        stateModel.elementId = ''
        stateModel.isFilling = isOnline
      } else {
        stateModel = {
          isFilling: isOnline,
          stepSequence: 0,
          elementId: '',
          focusElement: '',
          assigneeId: this.currentUserId
        }
      }
      stateModel.stepSequence = this.currentAssigneeOfStep.stepSequence

      const { boardId, transactionSequence } = this.formViewModel
      FormController.updateUserFillStatus(boardId, transactionSequence, stateModel)
    },
    /**
     * this method only be call when user focus on a field of form element
     * The element ID and field that the user focuses on will be recorded.
     */
    updateFillStatus(event: FormElementEventParams) {
      if (this.isSubmitting || !this.formViewModel) {
        return
      }
      if (this.formViewModel.isCompleted) {
        return
      }
      const { element, changedProperty } = event
      const elementId = element.id
      const elementValue = this.getTheUpdatedValue(event)

      Logger.debug('updateFillStatus:', elementId, changedProperty, elementValue)

      const currFocusedElement: Partial<ElementFilledFieldValueModel> = this.focusedElement || {}

      if (
        currFocusedElement.elementId !== elementId ||
        (currFocusedElement.elementId === elementId &&
          currFocusedElement.attrName !== changedProperty)
      ) {
        let stateModel: Partial<ElementFilledStatusViewModel> = this.filledStatus.get(
          this.currentUserId
        )
        if (stateModel) {
          stateModel.focusElement = changedProperty
          stateModel.elementId = elementId
          stateModel.isFilling = true
        } else {
          stateModel = {
            isFilling: true,
            type: FormUserStateType.Status,
            stepSequence: this.currentAssigneeOfStep.stepSequence,
            elementId,
            focusElement: changedProperty
          }
        }
        Logger.debug('set focusedElement:', elementId, changedProperty, element.value)

        this.focusedElement = {
          elementId,
          attrName: changedProperty,
          value: cloneDeep(element.value)
        } as ElementFilledFieldValueModel

        const { boardId, transactionSequence } = this.formViewModel
        FormController.updateUserFillStatus(boardId, transactionSequence, stateModel)
      }
    },

    /**
     * this method only be call once when the blur event trigger after user focus a form element
     * The content entered by the user will be saved into step.contents.
     * @param event.elementId {string}
     * @param event.changedProperty  - the changed attribute name, maybe an array (user change one item need update multi-attributes)
     *       If attrName is null, it indicates that the current element model’s value is a simple value, such as a string or number.
     * @param event.currentValue  - module value in local, It is a complete model value.
     * @param event.affectedProperties - After the user modifies a property in the UI, multiple property values need to be updated simultaneously.
     * @param event.enforce {boolean} Force update without checking if the user has changed the value.
     * @param event.withFillStatus {boolean} Synchronously update the user’s input status.
     *
     */
    saveFilledValue(event: FormElementEventParams) {
      Logger.debug('saveFilledValue:', event)

      // const elementValue = this.getTheUpdatedValue(event)
      const { element, changedProperty, enforce, withFillStatus } = event
      let currentValue = event.currentValue
      return new Promise((resolve, reject) => {
        if (this.isSubmitting || !this.formViewModel) {
          return reject()
        }
        const { isCompleted, isPDFForm } = this.formViewModel

        if (isCompleted) {
          return reject()
        }

        const factory = FormFactoryManager.getFormElementFactory(element.type)

        let needUpdate = enforce
        if (!needUpdate) {
          const isChangeCurrentFocusedElement = this.focusedElement?.elementId === element.id
          const focusedValue = JSON.stringify(this.focusedElement?.value)
          needUpdate =
            !isChangeCurrentFocusedElement ||
            (isChangeCurrentFocusedElement && JSON.stringify(currentValue) !== focusedValue)
        }

        this.focusedElement = null
        const { boardId, transactionSequence } = this.formViewModel

        if (needUpdate) {
          factory.setValue(element, currentValue, { changedProperty, transactionSequence, boardId })

          if (element.errors.length) {
            element.errors = element.errors.filter((err) => err.field !== event.changedProperty)
          }
          const elementId = element.id

          if (!isPDFForm) {
            calculateElementVisibleByCondition(this.formViewModel)
          }
          this.inSaving = true

          this.submittingElements.set(elementId, {
            attrName: changedProperty,
            elementId,
            value: currentValue
          })

          let filledStatus = null
          if (withFillStatus) {
            // this.focusedElement = { elementId, attrName, value: cloneDeep(currentValue) }
            filledStatus = this.filledStatus.get(this.currentUserId)
            if (filledStatus) {
              filledStatus.focusElement = changedProperty
              filledStatus.elementId = elementId
              filledStatus.isFilling = true
            } else {
              filledStatus = {
                isFilling: true,
                type: FormUserStateType.Status,
                stepSequence: this.currentAssigneeOfStep.stepSequence,
                elementId,
                focusElement: changedProperty
              }
            }
          }

          const updatedFields: FormElementAnyInputModel[] = [element]
          if (this.formViewModel.isPDFForm && element.name) {
            //handle Mutual Exclusion Logic
            const isCheckbox = element.type === FormElementType.MultiSelection
            const isRadio = element.type === FormElementType.SingleSelection
            if (
              (isCheckbox && (element.value as string[])?.length) ||
              (isRadio && isDefine(element.value))
            ) {
              FormFactoryManager.process(this.formViewModel, (item, itemFactory) => {
                if (element.name == item.name && item.id !== element.id) {
                  itemFactory.resetValue(item)
                  updatedFields.push(item)
                }
              })
            }
          }
          return FormController.saveUserFilledField(
            this.currentAssigneeOfStep.stepSequence,
            updatedFields,
            this.myFilledValues,
            {
              boardId,
              transactionSequence,
              isPDFForm: this.formViewModel.isPDFForm
            },
            filledStatus
          )
            .then(() => {
              this.submittingElements.delete(elementId)
            })
            .finally(() => {
              this.inSaving = false
              this.hasUnsavedChanges = false
              resolve(false)
            })
            .catch(reject)
        } else {
          const filledModel = this.filledValues.get(element.id)
          if (filledModel && filledModel.value !== currentValue) {
            factory.syncValue(element, filledModel.value, {})
          }
          // check other user
          resolve(true)
        }
      })
    },
    handleInProcessFiles(
      filledValues: Map<FormElementId, ElementFilledValueViewModel>,
      uploadedFiles: Map<FormElementId, FileUploadValue[]>
    ) {
      if (uploadedFiles.size > 0) {
        uploadedFiles.forEach((files, elementId) => {
          filledValues.set(elementId, {
            elementId,
            value: files,
            stepSequence: this.currentAssigneeOfStep.stepSequence,
            assigneeId: this.currentUserId,
            updatedTime: Date.now(),
            contentSequence: 0,
            type: FormUserStateType.Value
          })
        })
      }
      if (this.uploadingElements.size > 0) {
        this.uploadingElements.forEach((model) => {
          const { elementId, value } = model
          const currValue = filledValues.get(elementId)
          if (currValue) {
            if (currValue.value.find((item) => item.uuid === value.uuid)) {
              // file is upload success ignore it
            } else {
              currValue.value.push(value)
            }
          } else {
            filledValues.set(elementId, model)
          }
        })
      }
      return filledValues
    },
    uploadResourceToElement({ element, uploadFile }: FormElementEventParams) {
      const { boardId, transactionSequence } = this.formViewModel
      return new Promise((resolve, reject) => {
        uploadSignatureToTransaction(boardId, transactionSequence, uploadFile.url)
          .then(({ url, client_uuid }) => {
            this.saveFilledValue({
              element,
              currentValue: client_uuid,
              changedProperty: '',
              enforce: true
            })
              .then(resolve)
              .catch((res) => {
                removeTransactionResource(boardId, transactionSequence, client_uuid)
                reject(res)
              })
          })
          .catch(reject)
      })
    },
    async removeFileFromElement({ element, removeFile }: FormElementEventParams) {
      const { boardId, transactionSequence } = this.formViewModel
      const elementValue = (element.value as FileUploadValue[]) || []

      const removedItem = elementValue.find((item) => item.refSeq === removeFile.refSeq)
      if (!removedItem) {
        return
      }
      try {
        Vue.set(removedItem, 'loading', true)
        await TransactionController.removeTransactionAttachment(
          boardId,
          transactionSequence,
          [removeFile.refSeq],
          { suppressFeed: true }
        )
      } catch (error) {
        removedItem.error = { isRemoveError: true, allowRetry: true }
        Vue.set(removedItem, 'loading', false)
      }
    },
    clearSignatureElement({ element }: { element: FormElementSignatureModel }) {
      return this.saveFilledValue({
        element,
        currentValue: '',
        changedProperty: '',
        enforce: true
      })
    },
    /**
     * handle upload a file to form element
     * @param elementId
     * @param card
     * @param enforce
     * @param withFillStatus
     */
    uploadFileToElement({ element, uploadFile, withFillStatus }: FormElementEventParams) {
      if (this.isSubmitting || this.formViewModel?.isCompleted) {
        return Promise.reject(false)
      }
      const currentValue = (element.value || []) as FileUploadValue[]
      return new Promise((resolve, reject) => {
        const elementId = element.id
        const filledModel = {
          elementId: elementId,
          value: uploadFile,
          stepSequence: this.currentAssigneeOfStep.stepSequence,
          assigneeId: this.currentUserId,
          type: FormUserStateType.Value
        }

        const { uuid, name, size, type } = uploadFile
        const inUploadFile = this.uploadingElements.get(uploadFile.uuid)
        if (inUploadFile) {
          const file = currentValue.find((item) => item.uuid === uploadFile.uuid)
          file.error = null
          // get picked file from uploadingElements queue
          uploadFile = inUploadFile.value
          Vue.set(file, 'loading', true)
        } else {
          currentValue.push({
            uuid,
            name,
            size,
            type,
            refSeq: 0,
            resSeq: 0,
            url: '',
            loading: true
          })
          this.uploadingElements.set(uploadFile.uuid, filledModel)
        }
        element.value = currentValue
        const { boardId, transactionSequence } = this.formViewModel
        return FormController.uploadFileToElement(boardId, transactionSequence, uploadFile, {
          customData: JSON.stringify({
            step: this.currentAssigneeOfStep.stepSequence,
            //uuid is for UI layer status update
            uuid: uploadFile.uuid,
            elementId
          }),
          suppressFeed: true
        })
          .then((res) => {
            this.uploadingElements.delete(uploadFile.uuid)
          })
          .catch((err) => {
            const uploadItem = currentValue.find((item) => item.uuid === uploadFile.uuid)
            err.allowRetry = true
            uploadItem.error = err
            Vue.set(uploadItem, 'loading', false)
          })
      })
    },
    async submitForm() {
      if (this.currentAssigneeOfStep.isTeamUser) {
        const teamId = this.currentAssigneeOfStep.teamId
        const boardCommonActionsStore = useBoardCommonActionsStore()

        // Call the transferActionToMySelf method
        await boardCommonActionsStore.transferActionToMySelf(this.formViewModel.boardId, {
          actionType: '', //Defines.WorkflowStepType.WORKFLOW_STEP_TYPE_FORM_REQUEST,
          baseObjectSequence: this.formViewModel.transactionSequence,
          stepSequence: this.currentAssigneeOfStep.stepSequence,
          fromAssignee: {
            id: teamId,
            isTeam: true,
            group: {
              id: teamId
            }
          }
        })
      }

      this.isSubmitting = true
      return FormController.submitForm(
        this.formViewModel,
        this.currentAssigneeOfStep.stepSequence
      ).then((result) => {
        if (this.urlCallbackInfo?.url) {
          sendToCallbackUrl(this.urlCallbackInfo.url, this.urlCallbackInfo.payload)
        }
        return result
      })
    },
    validateForm(viewModel: FormViewModel | FormElementPageModel) {
      /**
       * The data currently being submitted by the user will not be in the viewModel. We need to manually apply it to the viewModel before performing validation.
       */
      const option: FormValidateOptions = {}
      if (viewModel && viewModel.type === FormElementType.Page) {
        option.validatePage = this.formViewModel.pages.findIndex(
          (item) => item.id === (viewModel as FormElementPageModel).id
        )
      }
      return FormFactoryManager.validate(this.formViewModel, option)
    },
    async initPreviewForm(boardId: string, transactionSequence: number): Promise<void> {
      this.currentUserId = MxISDK.getCurrentUser().id
      const { members, formViewModel } = await FormController.readFormInfo(
        boardId,
        transactionSequence
      )
      this.members = members
      this.formViewModel = formViewModel
    },
    destroyFormStore() {
      if (this.subscription) {
        this.subscription.unsubscribe()
        this.subscription = null
      }
      this.$reset()
    }
  }
})
