import { defineStore } from 'pinia'
import {
  AppConnectionViewModel,
  SupportAppViewModel,
  IntegrationAppViewModel,
  IntegrationSubtype,
  OAuthFromType,
  IntegrationTransaction, BuildFunctionFormViewModel, BuildFunctionActionViewModel
} from '@model/workflow/defines/IntegrationViewModel'
import { IntegrationCenterController } from '@newController/index'
import actionImages from '@views/theme/src/images/base_action/index'
import i18n from '@views/i18n/mepIndex'
import { Defines, IntegrationDefines, ObjectUtils } from '@commonUtils'
import merge from 'lodash/merge'
import vuexStore from '@appStore'

const FlowType = Defines.WorkflowStepType

interface State {
  integrationApps: IntegrationAppViewModel[];
  connections: AppConnectionViewModel[];
  categories?: IntegrationDefines.CategoryModel[];
  showTemplateAppTipId: string;
}

const APP_CATEGORY_ID_VERIFICATION = 'APP_CATEGORY_ID_VERIFICATION'
const APP_CATEGORY_E_SIGNATURE = 'APP_CATEGORY_E_SIGNATURE'

const defaultMarketPlaceApps: IntegrationAppViewModel[] = [
  {
    app_id: 'mock_app_id-docusign',
    name: 'DocuSign',
    description: i18n.t('docusign_app_description'),
    type: Defines.WorkflowStepType.WORKFLOW_STEP_TYPE_DOCUSIGN,
    isDefault: true,

    // support action needs (refer to getSupportActions.js)
    label: 'DocuSign',
    icon: actionImages.DocuSign,
    color: '#616161',
    bgColor: '#F4F4F4',

    category: APP_CATEGORY_E_SIGNATURE
  },
  {
    app_id: 'mock_app_id-jumio',
    name: 'Jumio',
    type: Defines.WorkflowStepType.WORKFLOW_STEP_TYPE_INTEGRATION,
    subType: 'Jumio',
    description: i18n.t('jumio_app_description'),
    isDefault: true,

    // support action needs (refer to getSupportActions.js)
    label: 'Jumio',
    icon: actionImages.JumioIcon,
    color: 'rgb(61, 102, 12)',
    bgColor: 'rgba(61, 102, 12, 0.1)',
    labelCreate: `${i18n.t('New_jumio')}`,

    category: APP_CATEGORY_ID_VERIFICATION
  }
]

export const marketPlace: IntegrationAppViewModel[] = [
  {
    app_id: 'mock_app_id-marketPlace',
    icon: 'micon-Integration',
    type: FlowType.WORKFLOW_STEP_TYPE_INTEGRATION,
    label: `${i18n.t('more_integrations')}`,
    subType: IntegrationSubtype.MARKETPLACE
  }
]

const update = (array, uid, source, uniqueField = 'app_id') => {
  const index = array.findIndex((record) => record[uniqueField] === uid)
  if (index >= 0) {
    const record = array[index]
    merge(record, source)
    return record
  }
  return null
}

const customSort = (arr = [], fields: string[], sortOrder: string) => {
  const order = sortOrder === 'asc' ? -1 : 1;
  arr.sort(function (a, b) {
      for (const field of fields) {
      if (a[field] < b[field]) {
          return 1 * order;
      } else if (a[field] > b[field]) {
          return -1 * order;
      }
      }
      return 0;
  });
  return arr;
}

export const useIntegrationCenterStore = defineStore('integrationCenter', {
  state: (): State => {
    return {
      integrationApps: [],
      connections: [],
      categories: [],
      showTemplateAppTipId: ''
    }
  },
  getters: {
    renderCategories (state): IntegrationDefines.CategoryModel[] {
      const isEnableDocuSign = vuexStore.getters['privileges/canCreateDocusign']
      const isEnableJumio = vuexStore.getters['group/isEnableJumio']
      const result = []
      if(isEnableDocuSign) {
        const docuSignCategory = state.categories.find(item => item.key === APP_CATEGORY_E_SIGNATURE)
        if(!docuSignCategory) {
          result.push({
            key: APP_CATEGORY_E_SIGNATURE,
            title: {
              en: 'E-Signature'
            }
          })
        }
      }

      if(isEnableJumio) {
        const jumioCategory = state.categories.find(item => item.key === APP_CATEGORY_ID_VERIFICATION)
        if(!jumioCategory) {
          result.push({
            key: APP_CATEGORY_ID_VERIFICATION,
            title: {
              en: 'ID Verification'
            }
          })
        }
      }

      return result.concat(state.categories)
    },
    hasIntegrationApps (state): boolean {
      const isEnableOtherIntegrationApps = vuexStore.getters['group/isEnableOtherIntegrationApps']
      return isEnableOtherIntegrationApps && state.integrationApps?.length > 0
    },
    enableMoreIntegrationEndpoint (state): boolean {
      const isEnableDocuSign = vuexStore.getters['privileges/canCreateDocusign']
      const isEnableJumio = vuexStore.getters['group/isEnableJumio']
      const isEnableOtherIntegrationApps = vuexStore.getters['group/isEnableOtherIntegrationApps']

      if(isEnableDocuSign || isEnableJumio) {
        return true
      }

      if(isEnableOtherIntegrationApps && state.integrationApps?.length > 0) {
        return true
      }

      return false
    },
    enabledDefaultMarketPlaceApps (state): IntegrationAppViewModel[] {
      const isEnableJumio = vuexStore.getters['group/isEnableJumio']
      const isEnableDocuSign = vuexStore.getters['privileges/canCreateDocusign']
      return defaultMarketPlaceApps.filter((app) => {
        if (app.type === FlowType.WORKFLOW_STEP_TYPE_DOCUSIGN && isEnableDocuSign) {
          return true
        }
        if (app.type === FlowType.WORKFLOW_STEP_TYPE_INTEGRATION && isEnableJumio) {
          return true
        }
        return false
      }) || []
    },
    marketplaceApps (state): IntegrationAppViewModel[] {
      const user = vuexStore.getters['user/currentUser']
      const isEnableOtherIntegrationApps = vuexStore.getters['group/isEnableOtherIntegrationApps']
      let integrationApps = []
      if(isEnableOtherIntegrationApps) {
        integrationApps = state.integrationApps
      }

      const result = integrationApps.concat(this.enabledDefaultMarketPlaceApps).map(app => {
        const cat: IntegrationDefines.CategoryModel = this.renderCategories.find(c => c.key === app.category) || {}
        return {
          ...app,
          categoryLabel: (cat.title && cat.title[user?.language]) || (cat.title && cat.title['en'])
        }
      })
      
      return result
    },
    addedApps (state): IntegrationAppViewModel[] {
      const isEnableOtherIntegrationApps = vuexStore.getters['group/isEnableOtherIntegrationApps']
      let linkedApps = []
      if(isEnableOtherIntegrationApps) {
        linkedApps =
          state.integrationApps.filter((app) => {
            // get the last connection
            const connection: AppConnectionViewModel = this.getAppConnections(app.app_id)[0] || {}
  
            app.auth_id = connection?.auth_id || ''
            app.last_access_time = connection?.last_access_time || connection.created_time || ''
            // if (connection?.isMock) {
            //   app.isMock = connection.isMock
            // }
            return connection.auth_id ? true : false
          }) || []
      }

      return this.enabledDefaultMarketPlaceApps.concat(linkedApps)
    },
    supportActions (): SupportAppViewModel[] {
      const _supportApps: IntegrationAppViewModel[] = this.addedApps.map((app) => {
        const { icon, name: label, type, subType, app_id, auth_id, last_access_time, isMock } = app
        return { icon, label, type, subType, app_id, auth_id, last_access_time, isMock }
      })
      customSort(_supportApps, ['last_access_time'], 'asc')
      return _supportApps.length? _supportApps.concat(marketPlace): []
    },
    getAppConnections (state) {
      return (appId: string) => {
          const _connections = state.connections.filter(conn => conn.app_id === appId)
          customSort(_connections, ['last_access_time', 'created_time'], 'desc')
          return _connections
      }
    }
  },
  actions: {
    setTemplateAppTipId (appId: string) {
      //For flow template edit, user drags the app, clicks disconnect, and then connects, 
      //flowActionDragItem does not need to show the 'Drag and drop to add the integration action' prompt
      this.showTemplateAppTipId = appId
    },
    async checkNeedCustomAuth (appId){
      try {
        const isCustomAuth = await IntegrationCenterController.checkNeedCustomAuth(appId);
        return isCustomAuth
      } catch (error) {
        throw error
      }
    },
    async doIntegrationOAuth (appId: string, form?: OAuthFromType, isCustomAuth?: boolean) {
      try {
        const account = await IntegrationCenterController.doIntegrationOAuth(appId,isCustomAuth)
        if(account) {
          //connections
          const { app_id, auth_id, display_id } = account || {}
          this.connections.push({
            app_id,
            auth_id,
            display_id,
            creatorId: vuexStore.getters['user/currentUser']?.id,
            last_access_time: `${Date.now()}`,
          })
    
          if(form && ![OAuthFromType.ACCOUNT].includes(form)) {
            this.setTemplateAppTipId(app_id)
          }
          return account
        } else {
          throw {error: 'oauth failed'}
        }
      } catch (error) {
        throw error
      }
    },
    async disconnectAccount (appId: string, authId: string) {
      await IntegrationCenterController.disconnectAccount(authId)
      //integrationApps array, uid, source, uniqueField = 'app_id'
      const _app: IntegrationAppViewModel = ObjectUtils.cloneDeep(
        this.integrationApps.find((app) => app.app_id === appId) || {}
      )
      _app.auth_id = ''
      // _app.isAdded = false
      update(this.integrationApps, appId, _app)

      //connections
      const index = this.connections.findIndex(
        (connection) => connection.app_id === appId && connection.auth_id === authId
      )
      this.connections.splice(index, 1)
    },
    validateAccount (authId: string, creatorId: string,  boardId?: string, boardViewToken?: string) {
      return IntegrationCenterController.validateAccount(authId, creatorId, boardId,  boardViewToken)
    },
    getCategoryList (): Promise<IntegrationDefines.CategoryModel[]> {
      return IntegrationCenterController.getCategoryList()
    },
    async getIntegrationActionApps (language: string) {
      try {
        const { apps, categories } = await IntegrationCenterController.getIntegrationActionApps(language)
        this.integrationApps = apps
        this.categories = categories
        await this.getLinkedIntegrationAccounts()

        return true
      } catch (error) {
        console.warn(error)
        return false
      }
    },
    async getLinkedIntegrationAccounts (): Promise<AppConnectionViewModel[]> {
      const linkedAccounts = await IntegrationCenterController.getLinkedIntegrationAccounts()
      //filter out the action's connection
      const resultList = linkedAccounts.filter(
        (item) => this.integrationApps.findIndex((app) => app.app_id === item.app_id) > -1
      )
      this.connections.push(...(resultList || []))
      return linkedAccounts
    },
    invokeBuildFunction (payload: IntegrationDefines.BuildFunctionModel, accessToken?: string): Promise<BuildFunctionFormViewModel | BuildFunctionActionViewModel> {
      return IntegrationCenterController.invokeBuildFunction(payload, accessToken)
    },
    invokeActionFunction (payload: IntegrationDefines.IActionFunctionModel, accessToken?: string) {
      return IntegrationCenterController.invokeActionFunction(payload, accessToken)
    },
    createTransaction (transaction: IntegrationTransaction) {
      return IntegrationCenterController.createTransaction(transaction)
    },
    updateTransaction (transaction: Defines.Board) {
      return IntegrationCenterController.updateTransaction(transaction)
    },
    getRealBinderId (binderId: string) {
      return IntegrationCenterController.getRealBinderId(binderId)
    },
    copyTempTransactionToBinder (tempBoardId: string, tempTransactionSequence: number, destBinderId: string) {
      return IntegrationCenterController.copyTempTransactionToBinder(tempBoardId, tempTransactionSequence, destBinderId)
    },
    closeIntegrationOAuthWindow (){
      IntegrationCenterController.closeIntegrationOAuthWindow();
    },
    integrationDestroy () {
      this.$reset()
    }
  }
})
