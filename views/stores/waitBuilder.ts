/* eslint-disable space-before-function-paren */
import {
  EventActionInfo,
  AppBaseInfo,
  WaitAmData
} from '@model/automation/defines'

import {Defines} from 'isdk';
import {defineStore} from 'pinia'
import {ObjectUtils, IntegrationDefines, FunctionUtil} from '@commonUtils'
import {
  IWorkflowStepEntity,
  IWorkflowStepViewModel,
  IWorkflowViewModel,
  createWorkflowStepAwait
} from '@model/workflow';

import { createWorkflowStepEntity } from '@model/workflow/entity/utils'
import { createJwt } from '@views/common/utils/jwt'
import vuexStore from '@appStore'

import {IDDRVariable} from '@model/workflow/defines/ddrViewModel';
import { cloneDeep } from 'lodash'
import { IntegrationCenterController } from '@newController/index'
import { DDRController } from '@newController/board/ddrControllerImpl'
import { IDDRSourceFilter } from '@newController/defines/ddrController'
import {WorkflowStepType} from 'isdk/src/api/defines'
import _pick from 'lodash/pick'
import { MxIntegrationSDK } from 'integrationSDK';
import { WaitAppExtURLPayload } from 'integrationSDK/src/types';
import { createAutomationBuilderController, IAutomationBuilderController } from '@newController/defines/automationBuilderController';

const getDefaultSiteUrl = () => {
  if (window.location.hostname.indexOf('192.') != -1)
    return process.env.proxyUrl;
  return window.location.hostname === 'localhost' ? process.env.proxyUrl : location.hostname;
}

const createEmptyAwaitOption = (): AppBaseInfo => {
  const user = vuexStore.getters['user/currentUser']
  const me_name = `${user.name}`.trim()
  const emptyAwaitOption: AppBaseInfo = {
    id: FunctionUtil.uuid(),
    actions: [
      {
        id: FunctionUtil.uuid(),
        service: '', //AmServiceType.unselected,
        data: {} as WaitAmData,   // TODO: attr inner data is not responsibility
        auth_owner: '',
        auth_id: ''
      }
    ],
    site_url: getDefaultSiteUrl(),
    owner_name: me_name,
    owner_uid: user.id,
    created_time: 0,
    updated_time: 0,
  }
  return emptyAwaitOption
}

interface BasicStepEntity {
  title: string;
  description: string;
  dueDateTimestamp: number;
  dueInTimeframe: Defines.DueTimeFrameType;
  excludeWeekends: boolean;
  // additionalOptions: {
  //   skipSequentialOrder: boolean;
  // }
}


export const useWaitBuilderStore = defineStore('waitBuilder', {
  state: () => {
    return {
      basicStepEntity: {} as BasicStepEntity,

      awaitOption: {} as AppBaseInfo,
      //the full list of fields which can be DDR Source. Currently it only comes from integration type of automation
      ddrSourceFields: [] as IDDRVariable[], 
      availableSourceSteps: [],

      stepEntity:null as IWorkflowStepEntity,

      flowViewModel:null as IWorkflowViewModel,

      availableSFSteps: [] as IWorkflowStepViewModel[],

      automationBuilderController: null as IAutomationBuilderController,
    }
  },
  getters: {
    action (): EventActionInfo {
      return this.awaitOption?.actions?.[0];
    },
    stepViewModel(state): IWorkflowStepViewModel {
      return state?.stepEntity.model;
    },
    isInShadowFlow(state): boolean {
      return state?.flowViewModel?.isShadowFlow
    },
    isMilestoneTemplate(state): boolean {
      return state?.flowViewModel?.isMilestoneTemplate
    }
  },
  actions: {
    initWaitStore(flowViewModel: IWorkflowViewModel, amStepViewModel: IWorkflowStepViewModel, isEditMode = false, prevStepClientUuid: string){
      this.awaitOption = createEmptyAwaitOption();
      this.flowViewModel = flowViewModel

      this.setAvailableSFSteps(flowViewModel, amStepViewModel, prevStepClientUuid, isEditMode)

      const duplicatedViewModel = amStepViewModel ? cloneDeep(amStepViewModel) : {};

      if (isEditMode) {
        this.stepEntity = createWorkflowStepEntity(duplicatedViewModel);
        this.awaitOption = _pick(duplicatedViewModel?.awaitOption, Object.keys(this.awaitOption));
        const { title, description, dueDate, dueInTimeframe, excludeWeekends, skipSequentialOrder } = duplicatedViewModel
        this.basicStepEntity = {
          title,
          description,
          dueDateTimestamp: dueDate,
          dueInTimeframe,
          excludeWeekends,
          // additionalOptions: {
          //   skipSequentialOrder
          // }
        }
      }
      else {
        this.stepEntity = createWorkflowStepAwait(duplicatedViewModel)
      }
      this.automationBuilderController = createAutomationBuilderController()
    },
    setAvailableSFSteps (flowModel: IWorkflowViewModel, amStepViewModel: IWorkflowStepViewModel, prevStepClientUuid: string, isEditMode: boolean): IWorkflowStepViewModel[] {
      const cid = {
        boardId: flowModel.boardId,
        clientUuid: flowModel.clientUuid
      }

      const filter: IDDRSourceFilter = {
        curStepType:  Defines.WorkflowStepType.WORKFLOW_STEP_TYPE_AWAIT,
        includeShadowFlowStep: true
      }

      if(isEditMode) {
        filter.curStepClientUuid = amStepViewModel.clientUuid
      } else {
        filter.prevStepClientUuid = prevStepClientUuid
      }

      const allSteps = DDRController.getAvailableSourceSteps(cid, filter, flowModel)

      const filtedStepType = [WorkflowStepType.WORKFLOW_STEP_TYPE_SHADOW_FLOW]
      this.availableSFSteps = allSteps.filter(s=>{
        return filtedStepType.includes(s.type)
      })
    },
    updateWaitAction (amAction: EventActionInfo, config: any = {}) {
      const action = this.awaitOption.actions[0]
      const tempObj = ObjectUtils.mergeObject(action, amAction, config)
      this.awaitOption.actions.splice(0, 1, tempObj)
    },
    updateBasicStepEntity (entity: BasicStepEntity) {
      this.basicStepEntity = entity
    },
    async invokeBuildFunction (payload: IntegrationDefines.BuildFunctionModel, accessToken?: string) {
      return IntegrationCenterController.invokeBuildFunction(payload, accessToken)
    },
    
    async testExternalURLRequest (payload: any): Promise<boolean> {
      try {
        //check if webhookPayload.enableJWT is true, if true, sign all custom_request_body with jwt.jwt_secret
        if (payload.method === 'POST' 
          && payload.enableJWT 
          && payload.jwt 
          && payload.jwt.jwt_key 
          && payload.jwt.jwt_secret) {
          const jwtToken = createJwt(payload.custom_request_body, payload.jwt.jwt_secret)
          Object.keys(payload.custom_request_body).forEach(key => {
            if (key !== payload.jwt.jwt_key) {
              delete payload.custom_request_body[key]
            }
          })
          payload.custom_request_body[payload.jwt.jwt_key] = jwtToken
        }
        delete payload.auth
        payload.headers.push("x-mx-token: hardcode.token.for.test.purpose")
        payload.headers = JSON.stringify(payload.headers)
        return this.automationBuilderController.testWebhookByTrigger(payload)
      } catch (e) {
        throw e
      }
    },
    async getEncryptedWebhookPassword (plainPassword: string): Promise<string> {
      return this.automationBuilderController.getEncryptedWebhookPassword(plainPassword)
    },
    
    setDDRSourceFields (fileds: IDDRVariable[]) {
      this.ddrSourceFields = fileds;
    },
    setAvailableSourceSteps(steps) {
      this.availableSourceSteps = steps
    },

    addWaitStep (payload: IWorkflowStepViewModel) {
      this.awaitOption.created_time = Date.now()
      this.updateStepEntity(payload)
    },
    editWaitStep (payload: IWorkflowStepViewModel) {
      this.awaitOption.updated_time = Date.now()
      this.updateStepEntity(payload)
    },
    updateStepEntity(payload: IWorkflowStepViewModel) {
      //automation, ddrFields
      this.stepEntity.update(payload)
    },
    getArrayCustomRequestBody(custom_request_body: { [key: string]: string }): { key: string, value: string }[] {
      return Object.entries(custom_request_body).map(([key, value]) => ({
        key,
        value
      }))
    },
    getObjectCustomRequestBody(customRequestBody: { key: string, value: string }[]) {
      return customRequestBody.reduce((acc, { key, value }) => {
        acc[key] = value
        return acc
      }, {})
    },
    destroyStore () {
      this.$reset();
      this.$dispose();
    },
  }
})