<template>
  <div class="flow-baseobject-docusign flow-baseobject">
    <div
      v-if="alertMessage"
      class="alert-info-container">
      <i class="micon-mep-warning"/>
      <div class="mx-text-c3 mx-margin-left-xs">{{ alertMessage }}</div>
    </div>

    <slot name="preparation-slot"></slot>
    <FlowStepNameWithIcon 
      type="WORKFLOW_STEP_TYPE_DOCUSIGN" 
      :name="stepModelBaseBaseObject.name"
      :date="stepModelBaseBaseObject.expirationDate" 
      :isEnded="stepModelBaseBaseObject.isCompleted || stepModelBaseBaseObject.isCanceled"/>
    
    <div
      v-if="stepModelBaseBaseObject.description"
      class="mx-text-c2 desc-content pre-line-text">
      <MxDDRTextDisplay :text="stepModelBaseBaseObject.description" />
    </div>
    
    <template v-if="!showError && !isProcessing && !isProcessSucceed">
      <!-- for creating  -->
      <template v-if="isUnprocessedAnchorTagDS">
        <AnchorTagsDSAttachments
          v-if="attachments.length"
          :attachments="attachments"
          :docuSignNoAction="docuSignNoAction"
          :stepBoardViewToken="stepBoardViewToken" />
      </template>
      <template v-else>
        <FlowAttachments
          v-if="!stepModelBaseBaseObject.isCompleted || (stepModelBaseBaseObject.isCompleted && hasValidAttachments) || stepModelBaseBaseObject.isMarkedAsCompleted"
          :step="{type: 'WORKFLOW_STEP_TYPE_DOCUSIGN'}"
          :attachments="attachments"
          :docuSignNoAction="docuSignNoAction"
          :showAttachmentsCount="attachments.length >= 1 ? true: false"
          :isDocuSignCompleted="stepModelBaseBaseObject.isCompleted&&!stepModelBaseBaseObject.isMarkedAsCompleted"
          :ignoreAttachmentCountCheck="true"
          :isInAggregate="isInAggregate"
          @viewAttachment="viewPage" />
        <div v-else-if="stepModelBaseBaseObject.isCompleted && !hasValidAttachments && !stepModelBaseBaseObject.isMarkedAsCompleted" class="info-banner base-banner">
          <i class="micon-info" />
          <span>{{ $t('Signed_DocuSign_is_still_being_processed') }}</span>
        </div>
      </template>
      <SignatureActionButton
        v-if="showSignBtn"
        :isNoAction="disableSignButton"
        :isWorkflow="mockedBinderInfos.isWorkflow"
        @viewPage="doSign" />
    </template>
    <!-- errorBanner -->
    <div 
      v-else-if="isProcessSucceed" 
      class="success-banner base-banner">
      <i class="micon-positive" />
      <span class="mx-text-c3">{{ processSuccessMsg }}</span>
    </div>
    <div 
      v-else-if="isProcessing"
      class="processing-banner base-banner">
      <i class="el-icon-loading" />
      <span class="mx-text-c3">{{ processingMsg }}...</span>
    </div>
    <template v-else-if="errorInfo.tips">
      <div class="error-banner base-banner">
        <i class="el-icon-warning" />
        <div>
          <span>{{ errorInfo.tips }}</span>
          <span
            v-if="errorInfo.needConnect" 
            class="mx-branding-text-action mx-text-c3 mx-clickable"
            @click="reconnect">{{ $t('Reconnect') }}</span>
          <span
            v-else-if="errorInfo.needRetry" 
            class="mx-branding-text-action mx-text-c3 mx-clickable"
            :class="{ 'mx-clickable-disabled': isProcessing }"
            @click="doProcess('RETRY')">{{ $t('retry') }}</span>  
          <span
            v-else-if="errorInfo.needResend" 
            class="mx-branding-text-action mx-text-c3 mx-clickable"
            :class="{ 'mx-clickable-disabled': isResending }"
            @click="doProcess('RESEND')">{{ $t('resend') }}</span>  
        </div>
      </div>

      <!-- Need to display transaction.attachments for processing anchor-tag DS -->
      <template v-if="isUnprocessedAnchorTagDS">
        <AnchorTagsDSAttachments
          v-if="attachments.length"
          :attachments="attachments"
          :docuSignNoAction="docuSignNoAction"
          :stepBoardViewToken="stepBoardViewToken" />
      </template>
    </template>
    
    <FlowProgress
      :showTurnOrder="stepModelBaseBaseObject.signByOrder"
      :stepModel="stepModelBaseBaseObject"
      :binderInfos="mockedBinderInfos"
      :currentFlowInfos="flowInfos"
      :config="config"
      :assignees="getTransformedAssignees()"
      :isObjectCreator="isObjectCreator"
      :retainSkipStatus="!!isAnchorTagsDS" />
  </div>
</template>

<script>
import {MxConsts} from "@commonUtils";
import propsMixin from '../mixins/props'
import MxDDRTextDisplay from '@views/ddr/MxDDRTextDisplay.vue'
import {AssigneeStatus} from '@views/workflows/utils/consts';
import FlowProgress from '@views/workflows/components/FlowProgress'
import FlowAttachments from '@views/workflows/components/FlowAttachments'
import SignatureActionButton from '@views/thread/src/component/SignatureActionButton'
import FlowStepNameWithIcon from '@views/workflows/components/FlowStepNameWithIcon'
import AnchorTagsDSAttachments from '@views/integrations/docusign/app/components/uploader/AnchorTagsDSAttachments.vue'
import { useAnonymousUser } from '@controller/user'
import { viewEnvelopDocument, viewCompletedDocument } from '@views/integrations/docusign/app/common/popup'
import {  useConnectDocuSign } from '@views/integrations/docusign/app/common/hooks'
import { useStore,useI18n } from '@views/common'
import { showSignDocusign } from '@views/integrations/docusign/app/showSignDocusign'
import { getEnvelopDocumentThumbnail, getTemplateDocumentThumbnail } from '@views/integrations/docusign/core/interactors/getDocumentThumbnail'
import { getUserAccessToken } from '@views/integrations/docusign/core/interactors/getUserAccessToken'
import { isAuthIdValid } from '@views/integrations/docusign/core/interactors/isAuthIdValid'
import { startDocusignEvent } from '@views/integrations/docusign/core/interactors/startDocusignEvent'
import { resendDocusignEvent } from '@views/integrations/docusign/core/interactors/resendDocusignEvent'
import { getDocusignAuthCredentials } from '@views/integrations/docusign/core/interactors/getDocusignAuthCredentials'
import { defineComponent, computed, watch, ref,reactive,provide, getCurrentInstance } from '@vue/composition-api'
import { createEnvelopeByTemplateId} from '@views/integrations/docusign/core/interactors/createDraftEnvelope'
import { removeTransaction, pinTransaction } from '@views/common/utils/transactionActionUtils'
import {parseTransactionAttachments} from '@views/contentLibrary/plugins/request/mockTemplateTransction.js'
import { AttachmentType } from '@views/newActions/common/defines/formItems/vmToAttachmentListFormItem'
import { DSErrorDetailCode } from '@views/integrations/docusign/app/common/consts'
import { getDocusignIdentityVerificationOptions } from '@views/integrations/docusign/core/interactors/getDocusignIdentityVerificationOptions.ts'

export default defineComponent({
  name: 'DocuSignObject',
  components: {
    MxDDRTextDisplay,
    FlowProgress,
    SignatureActionButton,
    FlowAttachments,
    FlowStepNameWithIcon,
    AnchorTagsDSAttachments,
  },
  mixins: [propsMixin],
  setup (props,{ emit }) {
    const showError = ref(false)
    const privilegeStore = useStore('privileges')
    const { currentUser }= useStore('user')
    const { t } = useI18n()
    const isEnvelopIdError = ref(false)
    const isProcessing = ref(false)
    const isProcessSucceed = ref(false)
    const processingMsg = ref('')
    const processSuccessMsg = ref('')
    const hasInvalidIDV = ref(false)
    let usedIDVs = []    // short for used identity verifications

    const { token: boardViewToken, isAnonymousUser } = useAnonymousUser()

    const userAccessToken = ref('')
    if (!isAnonymousUser) {
      getUserAccessToken().then(token => {
        userAccessToken.value = token
      })
    }

    const isLogin = ref(false)
    const isDocImageArrived = ref(false)

    const isEnvelopeStatusInvalid = () => {
      const custom_data = props.baseObject.custom_data?.docusign || props.baseObject.custom_data
      if (!custom_data) return

      const { draft, error, anchor_tags } = custom_data
      let hasError = draft || error
      if (anchor_tags?.length && hasError) {
        if (props.baseObject.isMarkedAsCompleted && !error) {
          // The anchor-tag DS was marked as completed
          hasError = false
        }
      }
      showError.value = Boolean(hasError)

      return hasError
    }

    const checkAuthIdValidity = async () => {
      const custom_data = props.baseObject.custom_data?.docusign || props.baseObject.custom_data
      if (!custom_data) return
      
      const { account_id, auth_id, base_uri, creator_user_id } = custom_data
      try {
        await isAuthIdValid({
          auth_id,
          account_id,
          base_uri
        }, {
          creatorId: creator_user_id,
          isAnonymous: isAnonymousUser,
          boardViewToken,
          boardId: props.binderObj.id
        })

        isLogin.value = true
      } catch (e) {
        isLogin.value = false
      } finally {
        isDocImageArrived.value = true
      }
    }

    const isRealObject = () => {
      if (props.inPreview) {
        return false
      }

      // For flow step case
      if (props.config?.stepSequence) {
        return props.config.stepIsInited
      }

      return true
    }

    const checkSignStatus = () => {
      if (!props.baseObject) return

      if (isRealObject()) {
        isEnvelopeStatusInvalid()
      }
      checkAuthIdValidity()
    }
    
    const checkHasInvalidIDVerifications = async () => {
      const custom_data = props.baseObject.custom_data?.docusign || props.baseObject.custom_data
      if (usedIDVs.length && custom_data) {
        const { account_id, auth_id, base_uri } = custom_data
        const docuSignCredentials = {account_id, auth_id, base_uri}
        const accountAvailableIDVs = await getDocusignIdentityVerificationOptions(docuSignCredentials)
        if (accountAvailableIDVs?.length) {
          hasInvalidIDV.value = usedIDVs.some(id => accountAvailableIDVs.findIndex(idv => idv.id === id) < 0)
        } else {
          hasInvalidIDV.value = true
        }
        if (hasInvalidIDV.value) {
          showError.value = true
        }
      }
    }

    watch(() => isLogin.value, (val) => {
      if (val && props.stepModel?.isNotStarted) {
        checkHasInvalidIDVerifications()
      }
    })

    watch(() => props.baseObject.custom_data, () => {
      if (!isProcessing.value) {
        checkSignStatus()
      }
    })  

    checkSignStatus()

    const getBoardUserById = id => {
      let targetBoardUser
      const boardUsers = props.binderObj.boardUsers
      if (boardUsers?.length > 0) {
        targetBoardUser = boardUsers.filter(bu => !bu.is_deleted && bu.id === id)[0]
        if (!targetBoardUser) {
          targetBoardUser = boardUsers.filter(bu => bu.id === id)[0]
        }
      }
      return targetBoardUser
    }

    const isAnchorTagsDS = computed(() => {
      const custom_data = props.baseObject.custom_data?.docusign || props.baseObject.custom_data
      return custom_data?.anchor_tags?.length
    })

    const isUnprocessedAnchorTagDS = computed(()=> {
      const custom_data = props.baseObject.custom_data?.docusign || props.baseObject.custom_data
      const {envelope_id, anchor_tags} = custom_data || {}
      if (anchor_tags?.length && !envelope_id) {
        return true
      }
      return false
    })

    const stepModelBaseBaseObject = computed(() => {
      const {sequence,title,subTitle, steps,expirationDate, isCompleted,isCanceled,custom_data,dueDateText,isMarkedAsCompleted} = props.baseObject
      let signByOrder = false
      if(steps?.length){
       signByOrder = steps[0].orderNumber === steps[steps.length - 1].orderNumber? false: true
      }
      return {
        type: MxConsts.WorkflowStepType.WORKFLOW_STEP_TYPE_DOCUSIGN,
        sequence: sequence,
        name: title,
        description: subTitle,
        steps: steps,
        expirationDate: props.config.dueDateText || dueDateText ||  expirationDate,
        isCompleted: isCompleted,
        isCanceled: isCanceled,
        signByOrder: signByOrder,
        isMarkedAsCompleted: isMarkedAsCompleted
      }
    })
    const mockedBinderInfos = computed( ()=> {
      return {
        isWorkflow: props.binderObj.isWorkflow,
        supportTransferConversation: false
      }
    })

    const isObjectCreator = computed(()=> {
      return props.baseObject?.creator?.isMySelf
    })

    const attachments = computed(()=> {
      let {attachments:docusignAttach} = props.baseObject
      const custom_data = props.baseObject.custom_data?.docusign || props.baseObject.custom_data
      if (!custom_data) return []

      const {documents,account_id,auth_id,envelope_id: envelopeId,base_uri,document_resource_map,creator_user_id,template_id:templateId} = custom_data
      let attachmentList = []
      const isResourceUploaded = document_resource_map && Object.keys(document_resource_map).length > 0
      if(isResourceUploaded){
        for(let i in document_resource_map){
          let attach = docusignAttach.find(a=>{
              return a.original === document_resource_map[i]
            })
          attach && attachmentList.push(attach)
        }
      } else {
        if (isUnprocessedAnchorTagDS.value && docusignAttach?.length > 0) {
          // Not started Anchor-Tags DocuSign
          let ddrAttachments = [], realFileAttachments = []
          docusignAttach.forEach(attach => {
            if (attach.isDDRFile) {
              // Note: Preview/Edit DS journey
              ddrAttachments.push(attach)
            } else if (attach.type === AttachmentType.DDR) {
              // Note: In creation journey, the ddrOriginal is the original file
              ddrAttachments.push(attach.ddrOriginal)
            } else {
              realFileAttachments.push(attach)
            }
          })
          if (realFileAttachments.length > 0) {
            realFileAttachments = parseTransactionAttachments(realFileAttachments)
          }
          attachmentList = [...ddrAttachments, ...realFileAttachments]
        } else {
          const docuSignCredentials = {
            account_id,auth_id,base_uri
          }  
          documents?.forEach(document=>{
            let baseThumbnailUrl = ''
            if(envelopeId){
              baseThumbnailUrl = getEnvelopDocumentThumbnail(docuSignCredentials, envelopeId, document.document_id, 1, { width: 100, height: 100 })
            }else{
              baseThumbnailUrl = getTemplateDocumentThumbnail(docuSignCredentials, templateId, document.document_id, 1, { width: 100, height: 100 })
            }
  
            if (!isAnonymousUser) {
              baseThumbnailUrl = `${baseThumbnailUrl}&access_token=${userAccessToken.value}&creator_user_id=${creator_user_id}`
            } else {
              baseThumbnailUrl = `${baseThumbnailUrl}&board_view_token=${boardViewToken}&board_id=${props.binderObj.id}&creator_user_id=${creator_user_id}`
            }
  
            attachmentList.push({
              id: document.document_id,
              name: document.name,
              thumbnailUrl: baseThumbnailUrl,
              // fileType: this.signature.fileType,
              size: '-'
            })
          })
        }
      }
      return attachmentList
    })
    const disableSignButton = computed(()=> {
      let {isMock} = props.baseObject
      let {isFlowStep, isNotStarted} = props.config
      return isMock || (isFlowStep && isNotStarted) || isProcessing.value
    })

    const isMySelf = computed(() => {
      const custom_data = props.baseObject.custom_data?.docusign || props.baseObject.custom_data
      return custom_data?.creator_user_id === currentUser.value.id
    })

    const isCreatorAccountConnected = computed(() => {
      if (props.baseObject.creator?.isMySelf || isDocImageArrived.value) {
        return isLogin.value
      }
      return true
    })
    
    const errorInfo = computed(()=>{
      const custom_data = props.baseObject.custom_data?.docusign || props.baseObject.custom_data
      if (!custom_data) return {}

      const { creator_docusign_email: email, creator_user_id: creatorId, draft, error } = custom_data
      const creatorInfo = getBoardUserById(creatorId)

      const errorInfo = {
        tips: '',
        needConnect: false,
        needRetry: false,
        needResend: false
      }

      const isEnvelopSentFailed = draft || error

      if (isEnvelopSentFailed) {
        const isEnvelopeUnderProcessing = Boolean(draft) && !error
        const isSentFailedByConnectionError = Boolean(draft) && error?.code === 'RESPONSE_ERROR_CENTRAL_OAUTH2'

        const {isCompleted, isCanceled} = props.baseObject
        if (isAnchorTagsDS.value && (isCompleted || isCanceled)) {
          errorInfo.tips = ''
        } else if (isEnvelopeUnderProcessing && isCreatorAccountConnected.value) {
          if (hasInvalidIDV.value) {
            if (currentUser.value.isInternalUser) {
              errorInfo.tips = t('DS_has_invalid_id_verification_enabled_error_info_for_internal')
            } else {
              errorInfo.tips = t('DS_has_invalid_id_verification_enabled_error_info_for_client')
            }
          } else {
            errorInfo.tips = t('This_envelope_is_still_under_processing')
          }
        } else if (isSentFailedByConnectionError || !isCreatorAccountConnected.value) {
          if (isMySelf.value) {
            errorInfo.tips = t('Error_connecting_with_docusign_pls_retry_for_creator', {
              email
            })
          } else if (currentUser.value.isInternalUser) {
            errorInfo.tips = t('Error_connecting_with_docusign_pls_retry_for_internal_user', {
              creatorName: creatorInfo?.name,
              creatorEmail: email
            })
          } else {
            errorInfo.tips = t('Error_connecting_with_docusign_pls_retry_for_client_user', {
              creatorName: creatorInfo?.name
            })
          }
        } else {
          errorInfo.tips = t('We_were_unable_to_process_your_request')
        }
        errorInfo.needRetry = (!isEnvelopeUnderProcessing || !isCreatorAccountConnected.value) && (isMySelf.value || currentUser.value.isInternalUser) && !props.binderObj.isNoAction
      } else {
        const isIDVCheckFailed = error?.detail_code === DSErrorDetailCode.ID_CHECK_FAILED
        if (isIDVCheckFailed) {
          errorInfo.tips = t('identity_verification_check_failed')
          if (currentUser.value.isInternalUser) {
            errorInfo.needResend = true
          }
        } else if (isEnvelopIdError.value) {
          errorInfo.tips = t('docusign_disconnect_for_client')
        } else if(isMySelf.value) {
          errorInfo.tips = t('docusign_disconnect_for_creator',{
            email
          })
          errorInfo.needConnect =  !props.binderObj.isNoAction
        } else if (currentUser.value.isInternalUser) {
          errorInfo.tips = t('docusign_disconnect_for_internal',{
            email,
            name: creatorInfo?.name
          })
        } else {
          errorInfo.tips = t('docusign_disconnect_for_client')
        }
      }

      return errorInfo
    })

    const showSignBtn = computed(()=>{
      const canSignDocusign = props.binderObj.isWorkflow || privilegeStore.canSignFile.value
      if(props.config.isAudit || props.binderObj.isNoAction || props.baseObject.isEnded || !canSignDocusign){
        return false
      }
      //  return props.baseObject.isMyTurn && props.baseObject.isActive
      if (props.inPreview) {
        return props.baseObject.isMyTurn 
      }

      return props.baseObject.isMyTurn && !props.baseObject.currentStep?.isDocuSignCCStep
    })

    const getTransformedAssignees = ()=>{
      let assigneeList = []
      const {currentTurnOrder, custom_data} = props.baseObject
      let sortedSteps = props.baseObject?.steps
      if(isAnchorTagsDS.value){
        sortedSteps = sortStepForAnchorDocusign(props.baseObject?.steps)
      }
      let allAssigneeList = [], idvList = new Set([])
      sortedSteps?.forEach(step=>{
        const {isSkipped,orderNumber, actions} = step
        const payload = actions?.[0]?.payload && JSON.parse(actions[0]?.payload)
        if (payload?.identity_verification?.workflowId) {
          idvList.add(payload.identity_verification.workflowId)
        }

        const {id,name,avatar,sequence,isInternalUser,isUserDeleted,isDeactivated,displayDeletedUser, isNoRole, isDefaultAssignee, isRole} = step.assignee
        const signee = {
          id,
          name,
          avatar,
          sequence,
          isInternalUser,
          isUserDeleted,
          displayDeletedUser,
          isNoRole,
          isRole,
          isDefaultAssignee,
          status: getAssigneeStatus(step,currentTurnOrder),
          isDone: step.isCompleted || step.isCanceled,
          isSkipped,
          isDocuSignCCUser: step.isDocuSignCCStep,
          orderNumber
        }
        allAssigneeList.push(signee)
        // if(step.isDocuSignCCStep){
        //   ccUserList.push(signee)
        // }else{
        //   signerList.push(signee)
        // }
      })
      usedIDVs = Array.from(idvList)

      assigneeList = allAssigneeList
      return assigneeList
    }

    const sortStepForAnchorDocusign = (steps)=>{
      const routingOrders = steps.map(signer => signer.orderNumber);
      const isParallel = routingOrders.every(order => order === routingOrders[0]);
      if(isParallel){
        const ccSteps = steps.filter(s=>{return s.isDocuSignCCStep})
        const signerSteps = steps.filter(s=>{return !s.isDocuSignCCStep})
        return ccSteps.concat(signerSteps)
      }else{
        return steps
      }
    }

    const getAssigneeStatus = (step,currentTurnOrder) => {
      if (step.isSkipped) {
        return AssigneeStatus.SKIPPED
      } else if (props.config.isNotStarted) {
        return step.isDocuSignCCStep ? AssigneeStatus.WAITING_TO_RECEIVE_COPY : AssigneeStatus.WAITING_TO_SIGN
      } else if (step.isCompleted) {
        return step.isDocuSignCCStep ? AssigneeStatus.RECEIVED : AssigneeStatus.SIGNED
      } else if (step.isCanceled) {
        return AssigneeStatus.DECLINED
      } else if (step.isCancel) {
        return AssigneeStatus.CANCELED
      } else if (step.orderNumber === currentTurnOrder || step.isYourTurn) {
        return step.isDocuSignCCStep ? AssigneeStatus.WAITING_TO_RECEIVE_COPY : AssigneeStatus.SIGNING
      } else {
        return step.isDocuSignCCStep ? AssigneeStatus.WAITING_TO_RECEIVE_COPY : AssigneeStatus.WAITING_TO_SIGN
      }
    }

    const viewPage = (file) => {
      if(props.baseObject.isCompleted && !props.baseObject.isMarkedAsCompleted){
        emit('viewPage', file)
        return
      }
      if (!isDocImageArrived.value) {
        return
      }

      if (!(props.baseObject.isCompleted || props.baseObject.isCanceled)) {
        if (!isLogin.value) {
          showError.value = true
          return
        }
      }

      if (file.originalUrl) {
        viewCompletedDocument(file.originalUrl)
      } else {
        const custom_data = props.baseObject.custom_data.docusign || props.baseObject.custom_data
        const { account_id, auth_id, base_uri, envelope_id: envelopeId, creator_user_id: creatorUserId,template_id:templateId } = custom_data
        const docuSignCredentials = {
          account_id,
          auth_id,
          base_uri
        }
        const authOptions = {}
        if (isAnonymousUser) {
          authOptions.boardId = props.binderObj.id
          authOptions.boardViewToken = boardViewToken
        } else {
          authOptions.accessToken = userAccessToken.value
        }
        viewEnvelopDocument(docuSignCredentials, envelopeId, file.id, authOptions, creatorUserId,templateId)
      }
    }

    const doSign = async () => {
      if (!isDocImageArrived.value) {
        return
      }
      const custom_data = props.baseObject.custom_data.docusign || props.baseObject.custom_data
      const { account_id, auth_id, base_uri, envelope_id: envelopeId,template_id:templateId ,creator_user_id} = custom_data
      let newEnvelopId = ''
      try {
        if(!custom_data.hasOwnProperty('draft') && !envelopeId){
          const {sequence,steps,subTitle,title} = props.baseObject
          let signers = []
          steps.forEach(s=>{
            const {actions,assignee} = s
            const payload = actions[0]?.payload && JSON.parse(actions[0]?.payload)
            const signeeInfo = {
              recipient_id: payload?.recipient_id,
              name: assignee.name,
              email: assignee.email,
              client_user_id: assignee.id,
            }
            signers.push(signeeInfo)

          })
          const templateDocusignInfos = {
            template_id: templateId,
            board_id: props.binderObj.id,
            transaction_sequence: sequence,
            signers,
            title,
            description: subTitle
          }
          const res = await createEnvelopeByTemplateId({base_uri, account_id, auth_id}, templateDocusignInfos,{
          creatorId: creator_user_id,
          isAnonymous: isAnonymousUser,
          boardViewToken,
          boardId: props.binderObj.id
        })
          newEnvelopId = res?.data?.envelope_id
          }
      } catch (error) {
         isEnvelopIdError.value = true
         showError.value = true
         return
      }


      if (isLogin.value || isAnonymousUser) {
        const { binderObj, baseObject } = props
        showSignDocusign({
          boardId: binderObj.id,
          transactionSequence: baseObject.sequence,
          envelopId: newEnvelopId
        })
      }else{
        showError.value = true
      }
    }

    const delayShowSuccedMsg = (msg) => {
      isProcessSucceed.value = true
      processSuccessMsg.value = msg
      setTimeout(() => {
        isProcessSucceed.value = false
      }, 3000)
    }

    const reconnect = async () => {
      const { connectDocuSign } = useConnectDocuSign()
      await connectDocuSign()
      showError.value = false
      await checkAuthIdValidity()
    }

    const doProcess = async (actionType) => {
      if (isProcessing.value) return

      isProcessing.value = true
      const isDoResend = actionType === 'RESEND'
      processingMsg.value = isDoResend ? t('resending') : t('retrying')

      const custom_data = props.baseObject.custom_data.docusign || props.baseObject.custom_data
      const { account_id, auth_id, base_uri, envelope_id, error, draft } = custom_data
      const isUnexpectedError = Boolean(draft) && error && error.code !== 'RESPONSE_ERROR_CENTRAL_OAUTH2'

      try {
        let isAccountConnected = true
        if (props.baseObject.creator?.isMySelf && !isLogin.value) {
          isAccountConnected = false
          const { connectDocuSign } = useConnectDocuSign()
          const result = await connectDocuSign()
          if (result) {
            isAccountConnected = true
          }
        }

        if (isAccountConnected) {
          if (isDoResend) {
            // resend
            const docuSignCredentials = {account_id, auth_id, base_uri}
            await resendDocusignEvent(docuSignCredentials, envelope_id)
          } else {
            // retry
            await startDocusignEvent({
              id: props.binderObj.id,
              transactions: [{
                sequence: props.baseObject.sequence
              }]
            })
            showError.value = false
            await checkAuthIdValidity()
          }

          let successMsg = ''
          if (isUnexpectedError) {
            successMsg = isDoResend ? t('Resend_successfully') : t('Your_request_has_been_successfully_proceed')
          } else {
            successMsg = t('Account_reconnected')
          }
          delayShowSuccedMsg(successMsg)
        }
      } finally {
        isProcessing.value = false
      }
    }

    const checkCreatorAccountDisconnection = () => {
      const { authCredentials } = getDocusignAuthCredentials()
      const custom_data = props.baseObject.custom_data.docusign || props.baseObject.custom_data
      const { account_id, auth_id } = custom_data 
      if (authCredentials.auth_id !== auth_id || authCredentials.account_id !== account_id) {
        isLogin.value = false
        showError.value = true
      } else {
        isLogin.value = true
      }
    }

    
    if (props.baseObject?.creator?.isMySelf) {
      const updateUserTagSeq = ref('')
      const docuSignCredentials = reactive({
        auth_id: '',
        base_uri: '',
        account_id: '',
        email: '',
        name: '',
      })
      const { userAuthTagSequence, authCredentials } = getDocusignAuthCredentials()
      updateUserTagSeq.value = userAuthTagSequence
      docuSignCredentials.auth_id = authCredentials.auth_id
      docuSignCredentials.base_uri = authCredentials.base_uri
      docuSignCredentials.account_id = authCredentials.account_id
      docuSignCredentials.email = authCredentials.email
      docuSignCredentials.name = authCredentials.name
      provide('docuSignCredentials', docuSignCredentials)
      provide('updateUserTagSeq', updateUserTagSeq)
      if (!(props.baseObject?.isCompleted || props.baseObject?.isCanceled)) {
        checkCreatorAccountDisconnection()
      }
    }
    
    const vm = getCurrentInstance()
    const handleDropdownAction = (cmd) => {
      if (cmd === 'deleteTransaction') { // grouop workspace case
        removeTransaction(props.baseObject, vm.proxy)
      } else if (cmd === 'pinTransaction') { // grouop workspace case
        pinTransaction(props.baseObject, vm.proxy)
      } 
    }
    const docuSignNoAction = computed(()=> {
      let {isMock} = props.baseObject
      let {isFlowStep, isNotStarted} = props.config
      return isMock || (isFlowStep && isNotStarted) || isProcessing.value
    })

    const alertMessage = computed(()=>{
      // Note: For DS which created with Anchor-Tags only
      let alertMsg = ''
      const custom_data = props.baseObject.custom_data?.docusign || props.baseObject.custom_data
      if (isAnchorTagsDS.value && custom_data?.error) {
        const {detail_code, code} = custom_data.error
        if (code === 'RESPONSE_ERROR_CENTRAL_OAUTH2') {
          alertMsg = t('DS_error_connection_to_ds_failed')
        } else {
          switch (detail_code) {
            case DSErrorDetailCode.NO_DOCUMENT_FOUND:
            case DSErrorDetailCode.UNRESOLVED_DDR_FILE:
              alertMsg = t('DS_error_required_missing_files')
              break
            case DSErrorDetailCode.FILE_SIZE_EXCEEDED:
              alertMsg = t('The_file_size_exceeds_the_limit')
              break
            case DSErrorDetailCode.FILE_TYPE_NOT_SUPPORTED:
              alertMsg = t('DS_error_unsupported_files')
              break
            case DSErrorDetailCode.INVALID_USER_OFFSET:
              alertMsg = t('DS_error_invalid_offset')
              break
            case DSErrorDetailCode.ANCHOR_TAB_STRING_NOT_FOUND:
              alertMsg = t('DS_error_anchor_string_not_found')
              break
            case DSErrorDetailCode.ANCHOR_TAG_PROCESSING_FAILURE:
              alertMsg = t('DS_error_anchor_tag_processing_failure')
              break
            case DSErrorDetailCode.INVALID_IDENTITY_VERIFICATION_FOUND:
              if (currentUser.value.isInternalUser) {
                alertMsg = t('DS_has_invalid_id_verification_enabled_error_info_for_internal')
              } else {
                alertMsg = t('DS_has_invalid_id_verification_enabled_error_info_for_client')
              }
              break
            default:
              alertMsg = t('something_went_wrong_retry')
              break
          }
        }
      }
      return alertMsg
    })

    const stepBoardViewToken = computed(()=>{
      return props.stepModel?.inputBoardViewToken || ''
    })

    const hasValidAttachments = computed(() => {
      return attachments.value.some(attachment => attachment.SPath)
    })

    return {
      showError,
      isLogin,
      stepModelBaseBaseObject,
      attachments,
      mockedBinderInfos,
      isObjectCreator,
      errorInfo,
      alertMessage,
      isProcessing,
      disableSignButton,
      showSignBtn,
      getTransformedAssignees,
      getAssigneeStatus,
      viewPage,
      doSign,
      doProcess,
      reconnect,
      isProcessSucceed,
      processingMsg,
      processSuccessMsg,
      isAnchorTagsDS,
      isUnprocessedAnchorTagDS,
      stepBoardViewToken,
      handleDropdownAction,
      docuSignNoAction,
      isInAggregate: props.isInAggregate,
      hasValidAttachments
    }
  },

})
</script>

<style lang="scss" scoped>
.flow-baseobject-docusign {
  position: relative;
  padding: 0px !important;
  ::v-deep .flow-detail-duedate {
    padding: 5px 20px 3px 20px;
    margin-top: 4px;
  }

  .desc-content {
    white-space: pre-wrap;
    padding: 5px 20px 3px 20px;
    margin-top: 4px;
    &.pre-line-text {
      white-space: pre-line;
    }
  }

  .mx-object-attachment {
    margin: 4px 16px 0px;
    ::v-deep {
      .response-header {
        padding: 9px 0 7px;
        line-height: 20px;
        margin-bottom: 0;
        height: initial;
        .mx-text-c1 {
          padding-left: 4px;
        }
      }
      .flow-step-attachs li.attachment-item {
        padding-left: 8px;
      }
    } 
  }

  .base-banner{
    display: flex;
    align-items: center;
    padding: 8px 16px 8px 8px;
    margin: 8px 16px;
    // box-shadow: 0px 0.3px 0.8px rgba(0, 0, 0, 0.02), 0px 0.9px 2.7px rgba(0, 0, 0, 0.03), 0px 1px 5px rgba(0, 0, 0, 0.05);
    border-radius: 6px;
    i {
      font-size: 16px;
      margin-right: 12px;
    }
  }

  .error-banner {
    background: rgba(189, 64, 0, 0.1);
    border: 1px solid $mx-color-var-caution;
    color: black;

    i {
      color: $mx-color-var-caution;
    }
  }
  .info-banner {
    background: #F4F4F4;
    border: 1px solid #C6C6C6;
    color: black;
    span {   
      font-size: 12px; 
      font-weight: 600;
    }
    i {
      color: #A8A8A8;
    }
  }
  .processing-banner {
    background: $mx-color-var-fill-quaternary;
    border: 1px solid $mx-color-var-label-quaternary;
    color: $mx-color-var-text-secondary;
    
    i {
      font-weight: bold;
    }
  }

  .success-banner {
    background: rgba(47, 121, 89, 0.1);
    border: 1px solid $mx-color-var-positive;
    color: black;

    i {
      color: $mx-color-var-positive;
    }
  }

  ::v-deep .is-default-signee .assignee-name-wrapper{
    color: $mx-color-var-label-tertiary
  }

  .mx-clickable-disabled {
    cursor: default;
    color: $mx-color-var-text-tertiary;
  }
}

</style>
