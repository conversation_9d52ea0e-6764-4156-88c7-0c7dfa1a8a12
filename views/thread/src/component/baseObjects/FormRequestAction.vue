<template>
  <div class="form-object">
    <ActionBasicInfo
      :custom-style="actionInfoCustomStyle"
      :img-src="actionImg"
      :title="baseObject.title"
      :sub-title="subTitle"
      :supplement="expirationDate"
    />

    <!-- description -->
    <ActionDescription :content="baseObject.subTitle" />
    <FormPreviewLink v-if="showFormPreviewLink" :isPDFForm="isPDFForm" @toView="enterToFormPreview" />

    <!--action buttons-->
    <ActionPrepareButtonPanel
      v-if="showPreparationButton"
      :disable-button="disablePreparationButton"
      @prepare="handlePrepare"
    />
    <ActionButtonWrapper
      v-else-if="showOperateBtns"
      :loading="loading"
      :is-success="isSuccess"
      :is-declined="isDeclined"
      :alert-title="''"
      :view-only="!!config.isAudit"
      :actions="renderActionButtons"
      :is-mock="baseObject.isMock"
      :get-action-text="getActionText"
      :operation-mask="operationMask"
      @processAction="processAction"
    />

    <!--form response after step completed-->
    <FormResponse
      v-if="!baseObject.isMarkedAsCompleted"
      class="mx-form-response"
      :base-object="baseObject"
      :binder-obj="binderObj"
      :isPDFForm="isPDFForm"
      @viewAttachment="previewFormUploadedFile"
      @preview="enterToFormPreview"
    />
    <!-- attachments -->
    <template v-if="showSubmittedFiles">
      <ActionSceneHeader 
        :title="$t('Submitted_files_from_form')"
        :is-form-submitted-files="isFormSubmittedFilesSection"
        :title-uppercase="false">
        <a v-if="canDownloadFile" @click="downloadSubmittedFiles">
          <i class="micon-mep-download mx-clickable" />
        </a>
      </ActionSceneHeader>
      <ActionAttachments
        class="action-attachments"
        :showCustomFolder="showCustomFolder"
        :customFolder="customFolder"
        :attachments="baseObject.submittedFiles"
        :attachment-clickable="attachmentClickable"
        :hideDownloadIcon="shouldHideDownloadIcon"
        :binder-obj="binderObj"
        :step="currentStep"
        @enterFolder="enterFolder"
        @viewAttachment="(attachment) => $emit('viewPage', attachment)"
      />
    </template>
    
    <!-- process -->
    <ActionPreparerPanel
      v-if="currentStep.enablePreparation"
      :step="currentStep"
      :is-preview="isPreview"
      :config="config"
      :is-no-action="isPreview || boardBriefViewModel.isNoAction"
      @reassign="reassign"
    />

    <ActionSceneHeader :title="$t('Progress')" :status-text="progressStatus" />
    <ActionProgress
      :is-preparing="isPreparing"
      :base-object="baseObject"
      :need-indication="needIndication"
      :need-show-order-num="false"
      :config="config"
      :show-transfer-option="showTransferOption"
      @reassign="reassign"
    >
      <div
        v-if="requiredDecription"
        slot="alert"
        class="mx-text-c4 required-assignee alert-decription"
      >
        {{ requiredDecription }}
      </div>
    </ActionProgress>
  </div>
</template>

<script>
import BaseAction from '@views/thread/src/component/baseObjects/BaseAction'
import FormPreviewLink from '@views/thread/src/component/baseObjects/common/FormPreviewLink'
import { mapGetters, mapActions, mapMutations } from 'vuex'
import { useAnonymousUser } from '@controller/user'
import MxFormPreview from '@views/contentLibrary/plugins/form/creator/MxFormPreview'
import FillForm from '@views/contentLibrary/plugins/form/creator/FillForm.vue'
import get from 'lodash/get'
import FormResponse from '@views/thread/src/component/baseObjects/common/FormResponse'

import actionImages from '@views/theme/src/images/base_action/index'
import ActionPrepareButtonPanel from './common/ActionPrepareButtonPanel.vue'
import ActionPreparerPanel from '@views/thread/src/component/ActionPreparerPanel.vue'
import downloadMixin from '@views/common/mixins/downloadActions'
import {openFormPreview, useFormFiller} from '@views/form'
import {FormElementType, FormScenes} from '@model/form/defines/shared'
import {Defines} from "isdk";
import ActionSceneHeader from './common/ActionSceneHeader.vue'
import {ActionScene} from "@views/newActions/common/types";
import { popupFactory } from '@views/common/useComponent'

export default {
  name: 'FormRequestAction',
  components: {
    FormPreviewLink,
    ActionPrepareButtonPanel,
    ActionPreparerPanel,
    FormResponse,
    ActionSceneHeader
  },
  extends: BaseAction,
  mixins: [downloadMixin],
  props: {
    needIndication: {
      type: Boolean,
      default: false
    },
    showTransferOption: {
      type: Boolean,
      default: false
    },
    baseObjectInfo: {
      type: Object,
      default: null
    },
    fromTemplate: {
      type: Boolean,
      default: false
    },
    attachmentClickable: {
      type: Boolean,
      default: true
    },
    fromScene: {
      type: String,
      default: ''
    },
    hideAttachmentDownload: {
      type: Boolean,
      default: true
    },
    scene:ActionScene
  },
  data() {
    return {
      objectInfo: this.getResourceInfo(),
      anonymousUserInfo: useAnonymousUser(),
      action: {}
    }
  },

  computed: {
    ...mapGetters('user', ['currentUser']),
    ...mapGetters('websdk', ['hasTransactionButtonClickCallback']),
    ...mapGetters('privileges', ['canDownloadFile']),
    isPDFForm (){
      return this.baseObject.transactionType === Defines.TransactionType.TRANSACTION_TYPE_PDF_FORM
    },
    actionImg (){
      return this.isPDFForm? actionImages.PdfForm : actionImages.FormRequest
    },
    subTitle (){
      return this.isPDFForm ? this.$t('PdfForm'): this.$t('Form')
    },
    IAmAssignee () {
      const myId = this.currentUser.id || this.anonymousUserInfo.user.id
      const user = this.baseObject.steps?.find(
        (step) => step.assignee.id === myId || step.assignee.isMyTeam
      )
      if (user) {
        return true
      }
      return false
    },
    showSubmittedFiles() {
      const baseObject = this.baseObject
      const totalFiles = baseObject?.submittedFiles?.length
      if (!baseObject.isCompleted || baseObject.isMarkedAsCompleted) {
        return false
      }
      return totalFiles > 0
    },
    showCustomFolder () {
      return this.baseObject?.submittedFiles?.length && this.baseObject.hasCustomFolder
    },
    customFolder () {
      return {
        name: this.baseObject.customFolderName || ''
      }
    },
    showFormPreviewLink() {
      // if(this.scene && ![ActionScene.IN_FLOW_WORKSPACE, ActionScene.IN_NORMAL_WORKSPACE].includes(this.scene)){
      //   if(this.baseObject?.formViewModel?.isPDFForm && this.baseObject?.formViewModel?.isEmptyForm){
      //     return false
      //   }
      //   return true
      // }
      // if (this.fromScene == 'TemplateLibrary' || this.fromScene == 'flowBuilder') {
      //   if(this.baseObject?.formViewModel?.isPDFForm && this.baseObject?.formViewModel?.isEmptyForm){
      //     return false
      //   }
      //   return true
      // }
      if (this.isPrepareStage) {
        if(!this.baseObject.isCompleted) {
          return false
        }
      }
      // 1. "isMock" is always true if the status of step is "NotStarted".
      if (this.baseObject.isMock) {
        return true
      }
      // 2. in process, not myself turn show link, is myself turn not show link
      if (!(this.baseObject.isCompleted || this.IAmAssignee)) {
        return true
      }
      if (this.config.isCanceled) {
        return true
      }
      return false
    },
    formPreviewContext() {
      if (!this.config.isReadOnlyPreview && this.baseObject.isMock && !this.config.isFlowStep) {
        return 'TEMPLATE_PREVIEW'
      }
      return 'READ_ONLY_PREVIEW'
    },
    renderActionButtons() {
      return this.baseObject.currentStep?.actions || []
    },
    isFormSubmittedFilesSection() {
      return this.baseObject && this.baseObject.transactionType === "TRANSACTION_TYPE_FORM_REQUEST";
    },
    shouldHideDownloadIcon() {
      return this.hideAttachmentDownload;
    }
  },
  watch: {
    ['baseObject.isCompleted']() {
      this.objectInfo = this.getResourceInfo()
    }
  },
  methods: {
    ...mapMutations('thread', ['setTakingAction']),
    ...mapActions('thread', ['updateActionLogForStep', 'updateTransactionViewTime']),
    ...mapActions('websdk', ['triggerTransactionButtonClickEvent']),
    getActionText() {
      return this.isPDFForm? this.$t('Fill_Out_PDF_Form') : this.$t('fill_out_form')
    },
    downloadSubmittedFiles() {
      this.doDownloadFiles(this.binderObj.id, this.baseObject.submittedFiles, this.baseObject.title)
    },

    previewFormUploadedFile(attachment) {
      console.debug('previewFormUploadedFile', attachment, this.baseObject)

      // const [show] = usePreview({}, {
      //   entry: {
      //     boardId: this.binderObj.id,
      //     fullData: {
      //       fileSequence: attachment.sequence,
      //       isTransactionAttachment: true,
      //       objectSequence: this.baseObject.sequence,
      //       type: 'TRANSACTION'
      //     }
      //   },
      //   binder: this.binderObj
      // })
      // show()
      this.$emit('viewPage', attachment)
    },
    handlePreviewAction(cmdInfo) {
      console.debug('handlePreviewAction', cmdInfo)
      if (cmdInfo.action == 'previewFile') {
        const attachment = this.baseObject.submittedFiles.find(
          (item) => item.referenceSequence === cmdInfo.file.refSeq
        )
        if (attachment) {
          this.previewFormUploadedFile(attachment)
        } else {
          console.debug('not find the file', cmdInfo.file)
        }
      }
    },
    getResourceInfo() {
      let objectInfo = this.baseObjectInfo
      const rawData = this.config?.rawData
      const isNotStarted = this.config?.isNotStarted
      if (rawData) {
        if (isNotStarted) {
          objectInfo = {
            boardId: rawData.board_id,
            transactionSequence: get(rawData, 'input.board.transactions[0].sequence'),
            viewToken: rawData.board_view_token
          }
        } else {
          objectInfo = {
            boardId: this.binderObj.id,
            transactionSequence: get(rawData, 'output.board.transactions[0].sequence')
          }
        }
      }
      // TODO: suppose we should use "scenes" to determine is flow/flow-template case, but for now we cannot get it for all scenarios, like flowtemplate preview, etc.
      if (objectInfo && (this.binderObj.isWorkflow || this.stepModel?.clientUuid)) {
        objectInfo.enableDDR = true
      }
      return objectInfo
    },
    enterToFormPreview() {
      if(this.isPDFForm){
        const formViewModel =  this.baseObject.formViewModel
        const runtimeOptions = {
          isCompleted: formViewModel.isCompleted,
          scenes: FormScenes.Preview,
          enableDDR: this.baseObjectInfo?.enableDDR || this.binderObj.isWorkflow
        }
        openFormPreview({
          formViewModel,
          runtimeOptions
        })
      }else {
        this.showFormPreviewPage()
      }
    },
    showFormPreviewPage () {
      const [showFormPreview] = popupFactory(MxFormPreview)({
        action: this.handlePreviewAction
      }, {
        binder: this.binderObj,
        fromTemplate: this.fromTemplate,
        transaction: this.baseObject,
        baseObjectInfo: this.objectInfo,
        formPreviewContext: this.formPreviewContext,
        isMultipleAssignees: this.baseObject.steps?.length !== 1
      })
      showFormPreview()
    },
    async processAction(action) {
      // for keyboard
      if (this.baseObject.isMock) {
        return
      }
      // await this.checkTeamAssignee()
      this.setTakingAction(true)
      const viewTimeParam = {
        ctrlKey: this.ctrlKey,
        transactionSeq: this.baseObject.sequence,
        stepSeq: this.baseObject.currentStep.sequence
      }
      await this.updateTransactionViewTime(viewTimeParam)
      // this.processingAction = action
      // this.submitStep = this.baseObject.currentStep
      if (this.hasTransactionButtonClickCallback) {
        this.triggerTransactionButtonClickEvent({
          payload: action.payload,
          stepId: this.baseObject.currentStep.sequence,
          chatId: this.$route.params.id,
          transactionId: this.baseObject.sequence,
          buttonId: action.id
        })
      } else {
        if (this.isPDFForm) {
          useFormFiller(
            {
              success: () => {
                this.closeFormOnSuccess()
              }
            },
            {
              transactionSequence: this.baseObject.sequence,
              binderId: this.binderObj.id,
              runtimeOptions: {
                isPDFForm: true
              }
            }
          )
        } else {
          this.showFillFormPage()
        }
        this.action = action
      }
    },
    showFillFormPage () {
      const [openFillForm] = popupFactory(FillForm)({}, {
        binderId: this.binderObj.id,
        transaction: this.baseObject
      })
      openFillForm()
    },
    enterFolder () {
      let foldInfo = this.baseObject.folders.find(f => f.name === this.customFolder?.name)
      const newFoldInfo = { folderInfo: { ...foldInfo }, tabIndex: 1 }
      this.$emit('viewFileFolder', newFoldInfo)
    },
  }
}
</script>

<style lang="scss" scoped>
.required-assignee {
  color: $mx-color-var-text-secondary;
}
</style>
