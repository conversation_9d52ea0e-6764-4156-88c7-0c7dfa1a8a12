<template>
  <div class="wait-object">
    <ActionBasicInfo
      :customStyle="actionInfoCustomStyle"
      :imgSrc="actionImg"
      :title="baseObject.title"
      :subTitle="$t('Wait')"
      :supplement="expirationDate" />

    <!-- description -->
    <ActionDescription :content="baseObject.subTitle"
                       :class="{'no-wait-info': !isInternalUser}" />

    <template v-if="isInternalUser">
      <div class="divided" />
      <div class="permission-tip">
        <i class="micon-lock-badge" />
        {{ $t('displayed_to_internal_user') }}
      </div>

      <div v-if="resultIcon"
           class="automation-in-preview"
           :class="resultIcon.style">
        <i :class="resultIcon.icon" />
        <span>{{ tips }}</span>
      </div>
  
      <ActionSceneHeader
        class="_without-background"
        :title="$t('wait_until')"
        :titleUppercase="false" />
  
      <div class="app-info">
        <div class="app-info_row">
          <div class="app-info_row-label">
            {{ $t('App') }} 
          </div>
          <div class="app-info_row-value">
            {{ appName }} 
          </div>
        </div>
        <div class="app-info_row">
          <div class="app-info_row-label">
            {{ $t('Event') }}
          </div>
          <div 
            class="app-info_row-value"
            :class="[{'is-deleted': shadowFlowEventError}]" 
            :title="appEvent">
            {{ appEvent }}
            <!-- <MxDDRTextDisplay
              :text="appEvent"
              :options="ddrOptions" /> -->
          </div>
        </div>
      </div>
      
      <template v-if="showAccountInfo || showInputsInfo">
        <div v-if="showAccountInfo">
          <ActionSceneHeader
          class="_without-background"
          :title="$t('Account')"
          :titleUppercase="false" />
    
        <div class="account-wrap">
          <!-- <div class="mx-text-c1 item-label"> {{ $t('Account') }}</div> -->
          <div class="account-content flex-box flex-align-items-center">
            <div class="user-icon">
              <i class="micon-host" />
            </div>
            <div class="account-info">
              <div class="mx-text-c2 flex-box flex-align-items-center">
                <span class="mx-ellipsis">
                  {{ appName }}
                </span> 
              </div>
              <div class="mx-text-c4 subtitle">
                {{ appAccount }}
              </div>
            </div>
          </div>
        </div>
        </div>
        
        <div v-if="showInputsInfo">
          <ActionSceneHeader
          class="_without-background"
          :title="$t('Inputs')"
          :titleUppercase="false" />
    
        <div class="inputs-wrap">
          <div class="input-box">
            <WaitAppExtURLInputSection
              v-if="isExternalURL"
              :stepModel="stepModel"
              :awaitOptionAction="awaitOptionAction"/>
            <AmIntegrationInputSection
              v-else
              :amAction="appActionData" />
          </div>
        </div>
        </div>
      </template>
    </template>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import BaseAction from '@views/thread/src/component/baseObjects/BaseAction'
import MxDDRTextDisplay from '@views/ddr/MxDDRTextDisplay.vue'

import actionImages from '@views/theme/src/images/base_action/index'
import AmIntegrationInputSection from '@views/automationBuilder/components/integration/AmIntegrationInputSection'
import WaitAppExtURLInputSection from '@views/workflows/components/flowStepCreator/components/wait/WaitAppExtURLInputSection'
import { transformAutomationResult, matchServiceName } from '@views/automationBuilder/common/utils'
import { AmServiceType } from '@model/automation/defines'


export default {
  name: 'WaitAction',
  components: {
    MxDDRTextDisplay,
    AmIntegrationInputSection,
    WaitAppExtURLInputSection
  },
  extends: BaseAction,
  props: {
    needIndication: {
      type: Boolean,
      default: false
    },
    showTransferOption: {
      type: Boolean,
      default: false
    },
    attachmentClickable: {
      type: Boolean,
      default: true
    }
  },
  data () {
    return {
      actionImg: actionImages.Wait,
      processingAction: null,
      submitStep: null
    }
  },
  computed: {
    ...mapGetters('user', ['currentUser']),
    ddrOptions () {
      return {
        parseLink: false
      }
    },
    awaitOptionAction () {
      const { awaitOption } = this.baseObject
      return awaitOption?.actions?.[0] || {}
    },
    isInternalUser () {
      return this.currentUser?.isInternalUser
    },
    isShadowFlow () {
      const { service } = this.awaitOptionAction || {}
      if(service === AmServiceType.waitShadowflow) {
        return true
      }
      return false
    },
    isExternalURL () {
      const { service } = this.awaitOptionAction || {}
      if(service === AmServiceType.externalurl) {
        return true
      }
      return false
    },
    hasSourceStep () {
      const { awaitOption } = this.baseObject
      return !!awaitOption?.sourceStep
    },
    appName () {
      if(this.isShadowFlow) {
        return matchServiceName[AmServiceType.waitShadowflow]
      }
      if(this.isExternalURL) {
        return matchServiceName[AmServiceType.externalurl]
      }

      const { data } = this.awaitOptionAction
      const { app_name } = data || {}
      return app_name
    },
    appEvent () {
      if(this.isShadowFlow) {
        const { awaitOption } = this.baseObject
        if(!awaitOption?.sourceStep) {
          // in flow template, can not get "isWorkflowTemplate" field.
          if(this.boardBriefViewModel?.hasOwnProperty('isWorkflowTemplate')) {
            const { isWorkflowTemplate } = this.boardBriefViewModel
            if(!isWorkflowTemplate) {
              //because in flow workspace runtime, can not edit wait step with SF case.
              return this.$t('referenced_event_removed')
            }
          }
          return this.$t('no_event_assigned')
        }
        
        const { sourceStep } = awaitOption 
        // const { data: sfData } = sourceStep?.automations?.[0]?.actions?.[0] || {}
        const { data: sfData } = sourceStep?.shadowFlowOption.actions?.[0] || {}
        const { board } = sfData || {}

        return `${this.$t('Shadow_Flow')}: ${board?.templateName}`
      }

      const { data } = this.awaitOptionAction
      const { action_name } = data || {}
      return action_name
    },
    shadowFlowEventError () {
      if(this.isShadowFlow) {
        const { isSourceStepInvalid, isSourceStepDeleted, sourceStep } = this.baseObject?.awaitOption || {}
  
        //"!sourceStep": for audit case.
        return isSourceStepInvalid || isSourceStepDeleted || !sourceStep
      }
      return false
    },
    appAccount () {
      return this.awaitOptionAction?.login_account || ''
    },
    appActionData () {
      const { data } = this.awaitOptionAction
      const { preview_desc } = data || {}
      return {
        data: {
          preview_desc
        }
      }
    },
    amResult (){
      return transformAutomationResult(this.awaitOptionAction, this.$t)
    },
    tips (){
      const amResult = this.amResult
      if (amResult.message) {
        return amResult.message
      } 
      return ''
    },
    resultIcon (){
      const amResult = this.amResult
      switch (amResult.status) {
        case 'invalid':
        case 'failed':
          return {
            icon: 'micon-cancel',
            style: 'failed',
          }
        default:
          return null
      }
    },
    showAccountInfo () {
      if(this.isShadowFlow || this.isExternalURL) {
        return false
      }
      return true
    },
    showInputsInfo () {
      if(this.isShadowFlow) {
        return false
      }
      return true
    }
  },
  methods: {
  }
}
</script>

<style lang="scss" scoped>
.wait-object {
  .no-wait-info {
    margin-bottom: 20px;
  }
  .divided {
    border-top: 1px solid $mx-color-var-fill-tertiary;
    margin: 20px 0px 0px;
  }
  .permission-tip {
    display: flex;
    align-items: center;
    padding: 10px 15px 4px;
    font-size: 12px;
    font-weight: 400;
    line-height: 16px;
    color: $mx-color-var-text-secondary;
    i {
      margin-right: 4px;
      font-size: 16px;
    }
  }

  .automation-in-preview {
    display: flex;
    align-items: center;
    border-radius: 6px;
    margin: 12px 16px;

    padding: 8px;
    span {
      font-style: normal;
      font-weight: 600;
      font-size: 12px;
      line-height: 16px;
      color: $mx-color-var-text-primary;
      margin-left: 12px;
    }
    &.failed {
      background: rgba(178, 36, 36, 0.1);
      border: 1px solid $mx-color-var-negative;
      box-shadow: 0px 0.3px 0.8px rgba(0, 0, 0, 0.02), 0px 0.9px 2.7px rgba(0, 0, 0, 0.03),
        0px 1px 5px rgba(0, 0, 0, 0.05);
      i {
        font-size: 16px;
        color: $mx-color-var-negative;
      }
    }
  }

  .app-info {
    background-color: #ffffff;
    font-size: 14px;
    line-height: 1.4;

    display: grid;
    grid-template-columns: minmax(auto, 70px) 1fr;
    padding: 10px 20px 18px;
    row-gap: 13px;
    column-gap: 5px;

    &_row {
      display: contents;
      &-label {
        // overflow: hidden;
        // text-overflow: ellipsis;
        // // white-space: nowrap;

        font-size: 14px;
        font-weight: 400;
        line-height: 20px;
        text-align: left;
        color: $mx-color-var-label-secondary;
      }
  
      &-value {
        font-size: 14px;
        font-weight: 400;
        line-height: 20px;
        text-align: left;
        color: $mx-color-var-text-primary;
        
        overflow: hidden;
        word-break: break-word;
        min-width: 0;

        &.is-deleted{
          color: $mx-color-var-negative;
          font-weight: 600;
        }
      }
    }
  }

  .account-wrap {
    padding: 0 20px;
    margin-bottom: 21px;

    .account-content {
      padding-top: 6px;

      .user-icon {
        color: $mx-color-var-fill-secondary;
        .micon-host {
          color: $mx-color-var-label-secondary;
          font-size: 24px;
        }
      }
    }

    .account-info {
      margin-left: 12px;
      overflow: hidden;

      .subtitle {
        color: $mx-color-var-text-secondary;
      }
    }
  }

  .inputs-wrap {
    margin-bottom: 30px;
    .item-label {
      margin: 0 20px;
    }

    .input-box {
      margin: 4px 16px 0px;
      padding: 16px;
      border: 1px solid $mx-color-var-card-border;
      border-radius: 4px;

      .input-item {
        display: flex;
        align-items: center;
        margin-bottom: 12px;

        .input-label {
          color: $mx-color-var-fill-secondary;
          width: 100px;
          margin-right: 8px;
          flex-shrink: 0;
        }
      }

      ::v-deep {
        .input-value {
          width: calc(100% - 108px);
          flex: 1;
        }
      }
    }
  }
}
</style>
