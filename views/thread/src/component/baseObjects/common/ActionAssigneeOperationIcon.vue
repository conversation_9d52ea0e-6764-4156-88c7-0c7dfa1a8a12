<template>
  <el-dropdown
    class="transfer-option"
    :class="[isMobile ? '' : 'mx-hover-hide']"
    trigger="click"
    @command="handleCommand">
    <i
      tabindex="0"
      class="micon-more mx-clickable"
      :aria-label="$t('more_options')"
      aria-haspopup="true"
      @keyup.enter.stop />
    <el-dropdown-menu slot="dropdown">
      <el-dropdown-item
        v-mx-ta="{ page: 'actionDetail', id: `reassign` }"
        command="reassign">
        <div class="flex align-center">
          <span>{{ $t('reassign') }}</span>
        </div>
      </el-dropdown-item>
      <el-dropdown-item
        v-if="isShowRemindSender"
        v-mx-ta="{ page: 'actionDetail', id: `remindSend` }"
        command="remindSend"
        divided>
        <div
          class="remind-box flex align-center">
          <template v-if="!reminderLoading">
            <div>
              <div class="mx-text-c2">
                {{ $t('send_reminder') }}
              </div>
              <div class="mx-text-c4 mx-color-secondary">
                <span v-if="displayTime">{{ $t('last_sent_time', {time: displayTime}) }}</span>
              </div> 
            </div>
          </template>
          <template v-else>
            <img
              :src="require('@views/theme/src/images/button-spinner.gif').default"
              alt="loading"
              width="20">
            {{ $t('sending_reminder') }}
          </template>
        </div>  
      </el-dropdown-item>
    </el-dropdown-menu>
  </el-dropdown>
</template>

<script>
import {BrowserUtils} from '@commonUtils';
import util from '@views/common/utils/utils'
import { mapState } from 'vuex'
import { useBoardCommonActionsStore } from '@views/stores/boardCommonActions'
import {
  mapActions as piniaMapActions
} from 'pinia'


import { Defines } from '@commonUtils'

const WorkflowStepType = Defines.WorkflowStepType

export default {
  name: 'ActionAssigneeOperationIcon',
  components: {
  },
  props: {
    isShowRemindSender: {
      type: Boolean,
      default: false
    },
    assignee: {
      type: Object,
      default: () => ({})
    }
  },
  events: ['command'],
  data () {
    return {
      isMobile: BrowserUtils.isMobile,
      reminderLoading: false
    }
  },
  computed: {
     ...mapState('thread', ['currentActivities']),
    displayTime () {
      const time = this.getLastSendTypeFeed?.created_time
      return time ? util.formatDisplayedTime(time, {displayToday: true}) : ''
    },
    getLastSendTypeFeed () {
      const currentActivities = this.currentActivities.filter(item => item.Action === 'RESEND_REMINDER') || []
      for (let i = currentActivities.length -1; i >= 0; i--) {
        const item = currentActivities[i]
        // for transction parse substep sequence in steps?.[0]
        // for signature parse substep sequence in signees?.[0]
        let activityAssignee = item?.baseObject?.steps?.[0] || item?.baseObject?.signees?.[0]
        
        const matchUser = (activityAssignee) => {
           const {id, teamId, userId} = this.assignee?.source || {}
          if ([id, teamId, userId].includes(activityAssignee?.id)) {
            return true
          } 
          return false
        }

        // for prepare
        if (this.assignee.isPreparer) {
          activityAssignee = item?.baseObject?.editor
          if (matchUser(activityAssignee)) {
            return item
          }
        }
        
        if (activityAssignee?.sequence === this.assignee.stepSequence && matchUser(activityAssignee)) {
          return item
        }
      }
      
      return null
    }
  },
  methods: {

    ...piniaMapActions(useBoardCommonActionsStore,[
      'resendActionReminder',
    ]),
    handleCommand (commandName) {
      if (commandName === 'reassign') {
        this.$emit('command', commandName)
      }
      if (commandName === 'remindSend') {
        this.resendReminder()
      }
    },
   

    async resendReminder () {
      
      const baseObjectSequence = this.assignee.baseObjectSequence
      const stepSequence = this.assignee.stepSequence
      const boardId = this.assignee.boardId
      const isEsignAction = this.assignee.step.type === WorkflowStepType.WORKFLOW_STEP_TYPE_SIGNATURE
      const payload = {
        isEsignAction: isEsignAction,
        baseObjSeq: baseObjectSequence,
        stepSeq: stepSequence,
        isPreparer: this.assignee.isPreparer,
      }
      try {
        this.reminderLoading = true
        await this.resendActionReminder(boardId, payload)
      } catch (error) {
        console.error(error)
      } finally {
        this.reminderLoading = false
      }
     
    },
   
  }
}
</script>

<style scoped lang="scss">
  .remind-box{
    gap: 8px;
    height: 44px;
  }
</style>
