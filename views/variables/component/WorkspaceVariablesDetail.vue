<script>
import { mapActions as piniaMapActions, mapState as piniaMapState } from 'pinia'
import EditWorkspaceVariablesDialog from '../EditWorkspaceVariablesDialog.vue'
import { useFlowWorkSpaceDetailStore } from '@views/stores/flowWorkspaceDetail'
import { popupFactory } from '@views/common/useComponent';
import { EWorkspaceVariableType } from '@model/workflow';
import templateFormat from '@views/common/utils/formatter/templateFormat'

export default {
  name: 'WorkspaceVariablesDetail',
  props: {
    showHeader: {
      type: Boolean,
      default: true
    }
  },
  data () {
    return {
      EWorkspaceVariableType: EWorkspaceVariableType
    }
  },
  computed: {
    ...piniaMapState(useFlowWorkSpaceDetailStore, ['workspaceVariables']),
  },
  methods: {
    close () {
      this.$emit('close')
    },
    textItemValue (value){
      if(!value) return '-'
      return templateFormat.linkMode(value)
    },
    moreOptionDropdownSelect () {
      const [show, hide] = popupFactory(EditWorkspaceVariablesDialog)(null, {})
      show()
      return hide    
    }
  }
}
</script>

<template>
  <div
    class="flow-detail">
    <header
      v-if="showHeader"
      class="mx-flex-container mx-thread-header">
      <div class="left-area">
        <div class="title mx-text-c1 mx-ellipsis mx-capitalize">
          {{ $t('workspace_variables') }}
        </div>
      </div>
      <div class="right-area">
        <el-dropdown
          trigger="click"
          class="mx-hover-hide more-info-dropdown"
          @command="moreOptionDropdownSelect">
          <span
            v-mx-ta="{ page: 'variables', id: `more_options` }"
            :title="$t('more_options')"
            :data-original-title="$t('more_options')"
            class="mx-clickable el-dropdown-link">
            <i class="micon-more" />
          </span>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item
              data-ta="edit_variable"
              command="editVariable">
              <span>{{ $t('edit_workspace_variables') }}</span>
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
        <span
          v-mx-ta="{ page: 'variables', id: `close` }"
          class="mx-clickable right-close-button"
          role="button"
          :aria-label="$t('close')"
          tabindex="0"
          @keydown.enter="close"
          @click="close">
          <i class="micon-close" />
        </span>
      </div>
    </header>
    <div class="variable-content">
      <div
        v-for="item in workspaceVariables"
        :key="item.sequence"
        class="variables-item">
        <div
          v-safe-text="item.label"
          class="variable-label mx-ellipsis mx-text-c2" />
        <el-tooltip 
          v-if="item.workspaceVariableOptions.type === EWorkspaceVariableType.TEXT"
          class="mx-ellipsis"
          popper-class="workspace-variable-item-tip"
          :content="item.value || '-'"
          effect="dark"
          placement="top">
          <div
            v-safe-html="textItemValue(item.value)"
            class="text-variable-value mx-ellipsis-3line variable-value" />  
        </el-tooltip>
        <el-tooltip 
          v-else
          class="mx-ellipsis"
          popper-class="workspace-variable-item-tip"
          :content="item.value || '-'"
          effect="dark"
          placement="top">
          <div
            v-safe-text="item.value || '-'"
            class="variable-value" />  
        </el-tooltip>
      </div>
    </div>
  </div>
</template>

<style lang="scss">
.workspace-variable-item-tip {
  white-space: pre-line;
  border-radius: 4px 6px 6px;
  font-size: 12px;
  font-weight: 600;
  line-height: 16px;
  padding: 8px 12px;
  min-width: 100px;
  background: rgba(32, 32, 32, 0.92) !important;
}
</style>

<style scoped lang="scss">
.text-variable-value {
  cursor: pointer;
}

.text-variable-value a {
  color: #1A69D1;
  text-decoration: underline;
}

.mx-ellipsis-3line {
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}
.flow-detail {
  height: 100%;
}

.flow-detail {
  overflow: hidden;
  height: 100%;
  display: flex;
  flex-direction: column;

  > header,
  .header {
    flex: 0 0 auto;
    border-bottom: 1px solid $mx-color-var-fill-tertiary;
    display: flex;
    align-items: center;
    height: 52px;
    padding-left: 20px;
    padding-right: 16px;

    > :first-child:not(.back-icon) {
      flex: 1 1 auto !important;
    }

    .left-area {
      > span {
        margin-right: $mx-spacing-sm;
      }

      flex: 1 1 auto;
      display: flex;
      flex-direction: column;
      max-width: calc(100% - 74px);

      .time {
        color: $mx-color-var-label-secondary;
        margin-top: -2px;
      }
    }

    .right-area {
      flex: 1 1 auto;
      display: flex;
      justify-content: flex-end;

      .right-close-button {
        color: $mx-color-var-label-secondary;
        display: inline-block;
        width: 28px;
        height: 28px;
        text-align: center;
        border-radius: 6px;

        i {
          line-height: 28px;
          font-size: 16px;
        }
      }

      .right-close-button {
        background-color: $mx-color-var-fill-quaternary;
        margin-left: 12px;

        .close-button {
          font-size: 16px;
          color: $mx-color-var-label-secondary;
        }
      }

      .right-close-button-non-scope {
        padding: 12px;
        color: $mx-color-var-label-secondary;
        display: inline-block;
      }
    }
  }

  .variable-content {
    overflow: auto;
    padding: 20px;
    display: flex;
    flex-direction: column;
    gap: 24px;

    .variables-item {
      pointer-events: auto;
      width: 100%;
      display: block;
      margin-bottom: 20px;

      .variable-label {
        color: $mx-color-var-label-secondary;
        line-height: 16px;
        text-align: left;
      }

      .variable-value {
        white-space: pre-line;
        font-size: 14px;
        font-weight: 400;
        line-height: 20px;
        text-align: left;
      }
    }
  }
}
</style>
