<!-- eslint-disable vue/attribute-hyphenation -->
<template>
  <BaseModal
    :visible="true"
    @close="close">
    <template slot="title">
      <div
        class="flex items-center">
        <div
          class="flex items-center flex-1"
          style="width: 90%;">
          <i
            v-if="showBack"
            tabindex="0"
            class="micon-left-arrow-new"
            @click="$emit('back')" />
          <span 
            v-if="!isEditMode"
            class="micon-automation am-icon" />
          <div class="mx-ellipsis">
            {{ modalTitle }}
          </div>
        </div>
      </div>
    </template>
    <template
      slot="content">
      <div v-if="isLoading">
        <span 
          v-mx-loading-spinner="isLoading" 
          class="loading-spinner" />
      </div>
      <el-tabs
        v-else
        v-model="activeTab"
        class="automation-tab"
        :style="{'--automation-total-tabs': totalTabList.length}">
        <el-tab-pane 
          :label="$t('App_and_Event')" 
          :name="tabList.SELECT_APP">
          <div 
            slot="label"
            class="tab-title">
            <span class="mx-ellipsis">{{ $t('App_and_Event') }}</span>
            <i :class="['head-icon',tabStatusIcon('isApp')]" />
          </div>
          <AutomationAppAndEvent
            ref="appAndEvent"
            @gotoNext="(val) => gotoNext(tabList.SELECT_APP, val)" />
        </el-tab-pane>
        <el-tab-pane
          v-if="canShowAccountTab"
          :name="tabList.ACCOUNT"
          :lazy="true"
          :disabled="!isAccountTabEnabled">
          <div 
            slot="label" 
            class="tab-title">
            <span class="mx-ellipsis">{{ $t('Account') }}</span>
            <i :class="['head-icon',tabStatusIcon('isAccount')]" />
          </div>
          <AutomationAccount
            ref="appAccount"
            :submitLoading="submitLoading"
            @gotoNext="(refName, cb) => gotoNext(tabList.ACCOUNT, refName, cb)" />
        </el-tab-pane>
        <el-tab-pane
          :name="tabList.INPUT"
          :lazy="true"
          :disabled="!isInputTabEnabled"
          :class="{'form-tab-pane': isIntegrationApp}">
          <div 
            slot="label"
            class="tab-title">
            <span class="mx-ellipsis">{{ $t('Inputs') }}</span>
            <i :class="['head-icon',tabStatusIcon('isInput')]" />
          </div>
          <AutomationAppData
            ref="appData"
            :isLastTab="isLastTab"
            :isEditMode="isEditMode"
            :activeTabPane="activeTab"
            :disableRoleAssignee="disableRoleAssignee"
            :template="template"
            :isSending="isSending"
            @gotoNext="(refName) => gotoNext(tabList.INPUT, refName)" />
        </el-tab-pane>
      </el-tabs>
    </template>
  </BaseModal>
</template>

<script>
import AutomationAppAndEvent from '@views/automationBuilder/components/common/AutomationAppAndEvent.vue'
import AutomationAccount from '@views/automationBuilder/components/common/AutomationAccount.vue'
import AutomationAppData from '@views/automationBuilder/components/common/AutomationAppData.vue'
import {useAutomationBuilderStore} from '@views/stores/automationBuilder'
import {mapState, mapActions} from 'pinia';
import { mapActions as vueMapActions } from 'vuex'
import {AmServiceType} from '@model/automation/defines'
import useAttachmentsStore from '@views/stores/attachments'
import {AttachmentsStoreKey} from '@views/stores/attachments'


const tabList = {
  SELECT_APP: 'SELECT_APP',
  ACCOUNT: 'ACCOUNT',
  INPUT: 'INPUT'
}
const TabStatus = {
  Error: 0,
  Init: 1,
  Success: 2
}

export default {
  name: 'CreateAutomationStep',
  components: {
    AutomationAppAndEvent, 
    AutomationAccount, 
    AutomationAppData
  },
  provide () {
    return {
      [AttachmentsStoreKey]: this.attachmentsStore
    }
  },
  props: {
    isEditMode: {
      type: Boolean,
      default: false
    },
    step: {
      type: Object,//IWorkflowStepViewModel
      default: null,//when creating, this isn't necessary
    },
    flowModel: { // IWorkflowViewModel
      type: Object,
      default: () => ({})
    },
    showBack: {
      type: Boolean,
      default: false
    },
    //Why it can't call API directly inside the automationBuilder store
    availableSourceSteps:{
      type: Array,
      default: () => ([])
    },
    isFromTemplate: {
      type: Boolean,
      default: false
    },
    selectUserDirect: {
      type: Boolean,
      default: false
    },
    insideFlowWorkspace: {
      type: Boolean,
      default: false
    },
    insideBinder: {
      type: Boolean,
      default: false
    },
    template: {
      type: Object,
      default: () => ({})
    },
  },
  data () {
    return {
      tabList,
      activeTab: tabList.SELECT_APP,
      showAppSelector: false,
      isLoading: false,
      eventTabStatus: TabStatus.Init,
      accountTabStatus: TabStatus.Init,
      inputTabStatus: TabStatus.Init,
      submitLoading: false,

      //for edit integration case, need to verify the account, if error also can click "Inputs" tab
      originlAppInfo: {
        authId: '',
        appId: '',
        actionKey: ''
      },
      isAppOrAccountOrEventChange: false,
      isSending: false,
      attachmentsStore: useAttachmentsStore()
    }
  },
  computed: {
    ...mapState(useAutomationBuilderStore, [
      'automation', 
      'stepViewModel',
      'emailVariables', 
      'actions',
      'inputsRequirdFields',
      'isAuthValid',
      'getAppConnections', 
      'isEdit',
      'isInputsError',
      'hasAmInvalidDDRVariable'
    ]),
    disableRoleAssignee () {
      return this.insideFlowWorkspace || this.insideBinder || this.selectUserDirect
    },
    action () {
      return this.actions?.[0]
    },
    modalTitle () {
      return this.isEditMode ? this.$t('Edit_Automation') : this.$t('New_Automation')
    },
    isLastTab () {
      return this.totalTabList[this.totalTabList.length - 1] === this.activeTab
    },
    canShowAccountTab () {
      return [AmServiceType.gmail, AmServiceType.slack, AmServiceType.box, AmServiceType.integration].indexOf(this.action?.service) !== -1
    },
    totalTabList () {
      const totalTabs = [tabList.SELECT_APP]
      if (this.canShowAccountTab) {
        totalTabs.push(tabList.ACCOUNT)
      }
      totalTabs.push(tabList.INPUT)
      return totalTabs
    },
    isAccountTabEnabled () {
      if (this.canShowAccountTab) {
        return this.eventTabStatus === TabStatus.Success
      }
      return false
    },
    isInputTabEnabled () {
      if (this.canShowAccountTab) {
        const { auth_id, data, service } = this.action || {}
        if(service === AmServiceType.integration) {
          const { app_id, action_key } = data || {}
          const { authId, appId, actionKey } = this.originlAppInfo
          if(auth_id === authId && app_id === appId && action_key === actionKey) {
            //app info was not re-selected when editing
            return this.eventTabStatus !== TabStatus.Init && this.accountTabStatus !== TabStatus.Init
          }
        } else if(service === AmServiceType.slack && !this.isAuthValid) {
          // 1. for slack case, by now(2025-04-14), 
          //    slack supports select private channel, central oauth needs add oauth scope to support it, 
          //    so the old token is invalid to call get channels api
          //2. for add slace case, When the user enters the Input Tab and clicks "Add Automation" without making any changes, 
          //   required field errors will be shown and the Input Tab will display an error icon. 
          //   If the user then click the Channels and load channel list, the request fails due to an auth error / token expired, 
          //   need to remind the user to reconnect their auth account. In this case, the Input tab must retain its error state and should not be reset.
          return this.eventTabStatus === TabStatus.Success && [TabStatus.Success, TabStatus.Error].includes(this.accountTabStatus)
        }
        
        return this.eventTabStatus === TabStatus.Success && this.accountTabStatus === TabStatus.Success
      } else {
        return this.eventTabStatus === TabStatus.Success
      }
    },
    isIntegrationApp () {
      return this.action?.service === AmServiceType.integration
    },
    computedTabStatus () {
      const action = this.action
      
      //1.app tab
      const hasSelectedApp = this.vaildRequiredFields(action, ['service', 'data.action_key']) 
      
      //2. account      
      const hasSelectedAccount = this.vaildRequiredFields(action, 'auth_id')
      
      //use "validateFields.js" for edit case(can show 'Inputs' tab status normal)
      const hasFillAllInputs = this.vaildRequiredFields(action)
      const isAuthValid = this.isAuthValid

      //in 'Inputs' page, call buildFunc error case
      const isInputsError = this.isInputsError
      const hasAmInvalidDDRVariable = this.hasAmInvalidDDRVariable

      return {
        [tabList.SELECT_APP]: !!hasSelectedApp,
        [tabList.ACCOUNT]: !!hasSelectedAccount,
        [tabList.INPUT]: !!hasFillAllInputs,
        isAuthValid,
        isInputsError,
        hasAmInvalidDDRVariable
      }
    },
    manageInputsStatusSelf () {
      //gmail and slack in input tab, message needn't clear when switch account or account error
      return [AmServiceType.gmail, AmServiceType.slack] 
    } 
  },
  watch: {
    computedTabStatus (nVal) {
      Object.keys(nVal).forEach(name => {
        switch(name) {
          case tabList.SELECT_APP:
            if(nVal[name]) {
              //selected app 
              // this.isAppTabDone = true
              this.eventTabStatus = TabStatus.Success
              this.accountTabStatus = TabStatus.Init
              if(!this.manageInputsStatusSelf.includes(this.action?.service)) {
                this.inputTabStatus = TabStatus.Init
              } 
            } else {
              this.eventTabStatus = TabStatus.Init
              this.accountTabStatus = TabStatus.Init
            }
            break
          case tabList.ACCOUNT:
            if(!this.canShowAccountTab) return

            if(nVal[name]) {
              //selected account
              this.accountTabStatus = TabStatus.Success
              //slack, gmail case switch account case, need to keep the input state(such as error)
              if(!this.isAuthValid) {
                //edit case, data has authId but the authId is invalid
                this.accountTabStatus = TabStatus.Error
              }
            } else {
              //un-select account
              if(!this.isAuthValid) {
                this.accountTabStatus = TabStatus.Error
              } else {
                if(!nVal[tabList.SELECT_APP]) {
                  
                } else {
                  this.accountTabStatus = TabStatus.Init
                }
              }
              this.inputTabStatus = TabStatus.Init
            } 
            break
          case tabList.INPUT:
            if(this.isInputsError || this.hasAmInvalidDDRVariable) {
              this.inputTabStatus = TabStatus.Error
            } else {
              if(nVal[name]) {
                this.inputTabStatus = TabStatus.Success
  
                if(!this.isAuthValid) {
                  const { service } = this.action || {}
                  // 1. for integration app, verify the account failed case, need to show the error status
                  if(service === AmServiceType.integration) {
                    //edit case, data has authId but the authId is invalid
                    this.inputTabStatus = TabStatus.Error
                  }
                }
              } 
            }
            break
        } 
      })
    }
  },
  created () {
    this.initAMStepStore(this.flowModel,this.step,this.isEditMode, this.isFromTemplate);
    this.setAvailableSourceSteps(this.availableSourceSteps)

    if (this.isEdit) {
      this.eventTabStatus = TabStatus.Success
      if(this.action.service === AmServiceType.integration) {
        this.originlAppInfo = {
          authId: this.action?.auth_id,
          appId: this.action?.data?.app_id,
          actionKey: this.action?.data?.action_key
        }
        this.isLoading = true
        this.validateAccount(this.action.auth_id, this.action.auth_owner).then(() => {
          this.setAuthValid(true)
        }).catch(() => {
          this.setAuthValid(false)
        }).finally(() => {
          this.isLoading = false
        })
      } else {
        this.accountTabStatus = TabStatus.Success
        this.inputTabStatus = TabStatus.Success
      }
    }
  },
  beforeDestroy () {
    this.destroyStore();
    this.attachmentsStore.destroyStore()
    this.attachmentsStore = null
  },
  methods: {
    ...vueMapActions('request', ['createTodoTransactionObject']),
    ...mapActions(useAutomationBuilderStore, [
      'initAMStepStore',
      'destroyStore',
      'setAvailableSourceSteps',
      'addAutomationStep',
      'updateAutomationStep',
      'validateAccount',
      'validate',
      'vaildRequiredFields',
      'updateAction',
      'setAuthValid',
    ]),
    async addAmStep () {
      try {
        this.isSending = true
        await this.addAutomationStep(this.attachmentsStore);
        //TODO:  how and when to add emailVariable to flowBuilder
        this.$emit('created',this.stepViewModel);
        this.close()
      } catch (error) {
        this.isSending = false
      }
    },
    async editAmStep () {
      try {
        this.isSending = true
        await this.updateAutomationStep(this.attachmentsStore);
        //TODO: it can't wait here, but show the message in the flow workspace
        this.$emit('created',this.stepViewModel)
        this.close()
      } catch (error) {
        this.isSending = false
      }
    },
    close () {
      this.destroyStore()
      this.$emit('close');
    },
    resetTabStatus (tabName, isValid) {
      if (tabName === tabList.SELECT_APP) {
        this.eventTabStatus = isValid? TabStatus.Success: TabStatus.Error
      } else if (tabName === tabList.ACCOUNT) {
        const isAuthValid = this.isAuthValid && isValid
        this.accountTabStatus = isAuthValid? TabStatus.Success: TabStatus.Error
      } else if (tabName === tabList.INPUT) {
        this.inputTabStatus = isValid? TabStatus.Success: TabStatus.Error
      }
    },
    gotoNext (currentTab, formId, callback){
      this.validate((res) => {
        callback && callback(res)
        
        this.resetTabStatus(currentTab, res)
        if(res) {
          // the last tab
          if(currentTab === tabList.INPUT) {
            this.isEditMode? this.editAmStep() : this.addAmStep()
          } else {
            this.gotoNextTab()
          }
        }
      }, formId)
    },

    tabStatusIcon (tabType) {
      let status;
      if (tabType === 'isApp') {
        status = this.eventTabStatus
      } else if (tabType === 'isAccount') {
        status = this.accountTabStatus
      } else if (tabType === 'isInput') {
        status = this.inputTabStatus
      }
      if (status === TabStatus.Success) {
        return 'micon-mep-read done-status'
      } else if (status === TabStatus.Error) {
        return 'micon-warning-s error-status'
      } else {
        return 'micon-reminder-s normal-status'
      }
    },

    // TODO: Expose
    async gotoNextTab () {
      const currentTabIndex = this.totalTabList.findIndex(tabName => {
        return tabName === this.activeTab
      })
      // Verify data will be handled at beforeTabChange
      this.activeTab = this.totalTabList[currentTabIndex + 1]
    },
  },
}
</script>

<style lang="scss" scoped>
.am-icon{
  color: #8a8a8a;
  border: 1px dashed #8a8a8a;
  padding: 3px;
  border-radius: 3px;
  background-color: #f4f4f4;
  font-size: 13px;
  margin-right: 10px;
}
.errorTip {
  display: flex;
  color: #000;
  border: 1px solid $mx-color-var-caution;
  background: rgba(189, 64, 0, 0.1);
  padding: 8px 16px 8px;
  border-radius: 6px;
  margin-bottom: 17px;
  align-items: flex-start;

  i {
    color: $mx-color-var-caution;
    margin-right: 14px;
    font-size: 16px;
  }
}


.automation-tab {
  margin-top: 0px !important;
  overflow: hidden;

  .tab-title {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;

    .head-icon {
      margin-left: 6px;
      font-size: 18px;
      font-weight: 600;
      &.micon-reminder-s {
        font-size: 19px;
        font-weight: 500;
      }
      &.micon-warning-s {
        font-size: 20px;
        font-weight: 500;
      }
    }
    span {
      max-width: 80%;
      display: inline-block;
    }
  }

  ::v-deep {
    .el-tabs__nav {
      display: flex;
      width: 100%;

      .el-tabs__item {
        width: calc(100% / var(--automation-total-tabs));
        text-align: center;
        height: 48px;
        line-height: 48px;
        box-shadow: none !important;
        padding: 0px;
        font-weight: 600;
        &:not(.is-active) {
          color: $mx-color-var-text-primary;
        }
        &.is-disabled {
          color: $mx-color-var-label-quaternary;
          cursor: not-allowed;
        }
      }
      &-wrap::after {
        height: 1px;
        background-color: $mx-color-navigation-divider;
      }
    }

    .el-tab-pane {
      // max-height: calc(100vh - 302px);
      // overflow: auto;
      overflow-x: hidden;
      // padding: 10px 28px 24px;

      &.form-tab-pane {
        margin: 0;
        height: calc(100vh - 302px);
        min-height: 360px;
        overflow: hidden;
        // border-radius: 16px;
      }
    }

    .el-tabs__header {
      margin-bottom: 0;
    }

  }

  ::v-deep .el-tabs__active-bar {
    border-radius: 2px 2px 0px 0px;
    position: absolute;
    display: block;
    height: 2px;
    // width: 179px !important;
    bottom: 0px;
    left: 0;
    z-index: 1;
    transition: transform .3s cubic-bezier(.645, .045, .355, 1);
    list-style: none;
  }

  .done-status {
    color: $mx-color-var-positive;
  }

  .error-status {
    color: $mx-color-var-negative;
  }

  .normal-status {
    color: $mx-color-var-text-quaternary;
  }
}

::v-deep {
  .el-dialog {
    width: 540px;
    padding: 0;
  }

  .el-dialog__header {
    .left {
      width: 90%;
    }
    .micon-left-arrow-new {
      cursor: pointer;
      font-size: 16px;
      margin-left: 2px;
      margin-right: 5px;
      color: $mx-color-var-label-secondary;
    }
  }

  .el-dialog__body {
    max-height: calc(100vh - 262px);
    padding: 0;
    min-height: 403px;
  }

  .el-dialog__footer {
    padding-left: 24px;
    padding-right: 24px;

    .el-button + .el-button {
      margin-left: 0;
    }
  }
}
</style>