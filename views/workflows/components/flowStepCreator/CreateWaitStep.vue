<template>
  <BaseModal
    :visible.sync="visibleProxy"
    :modal-option="{
      width: '540px',
      beforeClose: beforeClose
    }"
    class="mobile-fullscreen-dialog wait-dialog"
    :class="{'dialog-auto-height':hasVisitedForm && !isPreviewNow}"
    @close="onClose"
  >
    <template slot="title">
      <div class="flex-center" style="width: 100%; justify-content: space-between;">
        <div style="display: flex; align-items: center; flex: 1; width: 90%;">
          <i
            v-if="showGoback"
            class="micon-left-arrow-new go-back-nav-icon"
            @click="handleGoback"
          />
          <IntegrationIcon v-show="!isPreviewNow && !isEditMode" :imgSrc="headerImg" />
          <div class="mx-ellipsis">
            {{ headerTitle }}
          </div>
        </div>
      </div>
    </template>
    <template slot="content">
      <template v-if="loading">
        <span v-mx-loading-spinner="loading" class="loding-spinner" />
      </template>
      <template v-else>
        <WaitBasicForm
          v-show="currentRenderView === allViews.basic"
          :showAdditionalOption="showAdditionalOption"
          :showSequentialOrder="isSequential"
          :disableSequentialOrder="disableSequentialOrder"
          :enableDDR="!hideDDR"
          :isEditMode="isEditMode"
          @handleEmit="handleEmit"
          @next="handleBasicNext"
          @close="onClose"
        />

        <WaitAppAndAccount v-show="currentRenderView === allViews.app"
                           :isEditMode="isEditMode"
                           @next="handleAccountNext" />

        <WaitAppExtURLForm v-show="currentRenderView === allViews.externalURL"
                           :isEditMode="isEditMode"
                           @next="handleExternalURLNext" />

        <IntegrationFormView
          v-if="hasVisitedForm"
          v-show="currentRenderView === allViews.waitForm"
          ref="formRef"
          class="integration-form-view"
          :integration-options="integrationOptions"
          :cachedValues.sync="cachedFormValues"
          :hideBackButton="true"
          @success="handleFormNext"
        />

        <WaitPreview
          v-if="currentRenderView === allViews.preview"
          :isEditMode="isEditMode"
          :isSending="isSending"
          @handleEmit="handleSubmit"
          @close="onClose"
        />
      </template>
    </template>
  </BaseModal>
</template>

<script>
import { visibleMixin } from '@views/common/components/modals/mixins'
import focusToErrorItem from '@views/workflows/utils/focusToErrorItem'
import IntegrationIcon from '@views/integrations/integrationCenter/components/IntegrationIcon'
import WaitPreview from './components/wait/WaitPreview'
import WaitBasicForm from './components/wait/WaitBasicForm'
import WaitAppExtURLForm from './components/wait/WaitAppExtURLForm'
import WaitAppAndAccount from './components/wait/WaitAppAndAccount'
import { useWaitBuilderStore } from '@views/stores/waitBuilder'
import { mapActions, mapState } from 'pinia'
import editActionMixin from '@views/requests/mixins/editActionMixin'
import { mapGetters as vuexMapGetters, mapActions as vuexMapActions } from 'vuex'
import actionImages from '@views/theme/src/images/base_action/index'
import ActionCreateBase from './ActionCreateBase'
import { AmServiceType } from '@model/automation/defines'
import IntegrationFormView from '@views/integrations/integrationCenter/views/IntegrationFormView.vue'

export default {
  name: 'CreateWaitStep',
  components: {
    IntegrationFormView,
    IntegrationIcon,
    WaitBasicForm,
    WaitAppExtURLForm,
    WaitAppAndAccount,
    WaitPreview
  },
  extends: ActionCreateBase,
  mixins: [visibleMixin, focusToErrorItem, editActionMixin],
  props: {
    showBack: {
      type: Boolean,
      default: false
    },
    step: {
      type: Object,
      default: () => ({})
    },
    presetObject: {
      type: Object,
      default: () => ({})
    },
    hostBoard: {
      type: Object,
      default: () => ({})
    },
    isSequential: {
      type: Boolean,
      default: true
    },
    supportNoAssignee: {
      type: Boolean,
      default: false
    },
    //Use this to disable DDR Autofill feature for some case(like milestone template)
    hideDDR: {
      type: Boolean,
      default: false
    },
    availableSourceSteps: {
      type: Array,
      default: () => []
    },
    flowModel: {
      // IWorkflowViewModel
      type: Object,
      default: () => ({})
    },
    prevStepClientUuid: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      headerImg: actionImages.SmallWait,
      allViews: {
        basic: 'BASIC',
        app: 'APP',
        waitForm: 'WAIT_FORM',
        externalURL: 'EXTERNAL_URL',
        preview: 'PREVIEW'
      },
      hasVisitedForm: false,
      viewHistory: [],

      loading: false,
      connector: null,
      isSending: false,
      cachedFormValues: {}
    }
  },
  computed: {
    ...vuexMapGetters('user', ['currentUser']),
    ...mapState(useWaitBuilderStore, [
      'action',
      'basicStepEntity',
      'stepViewModel',
      'awaitOption',
      'ddrSourceFields'
    ]),
    integrationOptions(){

      const action = this.action || {}
      return {
        actionKey: action?.data?.action_key,
        enableDDR: !this.hideDDR,
        //todo: for edit case need get form values from action steps.0.actions.0
        formDefaultValues:{},
        appId: action?.data?.app_id,
        authId: action.auth_id,
        appName: action.data?.app_name,
        // assignees: transformIntegrationAssignees(this.basicBoardInfo?.assignee),
        steps:this.availableSourceSteps || [],
        title: this.basicStepEntity?.title,
        description: this.basicStepEntity?.description
      }
    },
    currentRenderView() {
      return this.viewHistory.at(-1)
    },
    showGoback() {
      return this.showBack || this.viewHistory.length > 1
    },
    isPreviewNow() {
      return this.currentRenderView === this.allViews.preview // || !this.isMainView
    },
    headerTitle() {
      if (this.currentRenderView === this.allViews.preview) {
        return this.$t('preview')
      } else if (this.isEditMode) {
        // For step, it use step.displayOrde, for the actual object, use StepIndex
        let title = this.$t('edit_wait')
        const stepIndex = this.step?.displayOrder //|| this.index
        if (stepIndex && this.isSequential) {
          title += ` (${this.$t('Step_of_number', { number: stepIndex })})`
        }
        return title
      } else {
        return this.$t('new_wait')
      }
    },
    disableSequentialOrder() {
      const { service } = this.action || {}
      if (service === AmServiceType.waitShadowflow) {
        return true
      }
      return false
    }
  },
  watch: {
    action(nVal,oVal) {
      const { auth_id, data } = nVal || {}
      const { app_id } = data || {}
      if(auth_id && auth_id !== oVal.auth_id){
        this.cachedFormValues = {}
      }
      if (auth_id && app_id) {
        this.connector = {
          app_id,
          auth_id
        }
      }
    },
    viewHistory(nVal) {
      const historyStr = nVal?.toString()
      if (historyStr.includes(this.allViews.waitForm)) {
        this.hasVisitedForm = true
      } else {
        this.hasVisitedForm = false
      }
    }
  },
  created() {
    this.viewHistory.push(this.allViews.basic)

    //clear the localStorage
    const LOCALSTORAGEKEY = 'mx-integrations-forms'
    const id = this.currentUser?.id || 'anonymous'
    const name = LOCALSTORAGEKEY + '_' + id
    localStorage.removeItem(name)
  },
  async mounted() {
    this.setAvailableSourceSteps(this.availableSourceSteps)
    this.initWaitStore(this.flowModel, this.step, this.isEditMode, this.prevStepClientUuid)
  },
  beforeDestroy() {
    this.destroyStore()
  },
  methods: {
    ...mapActions(useWaitBuilderStore, [
      'setAvailableSourceSteps',
      'initWaitStore',
      'updateWaitAction',
      'updateBasicStepEntity',
      'setDDRSourceFields',
      'addWaitStep',
      'editWaitStep',
      'destroyStore'
    ]),
    ...vuexMapActions('request', ['createWaitObject']),
    goback() {
      if(this.currentRenderView === this.allViews.waitForm) {
        if(this.$refs.formRef.goBack()){
          // in form has multi-page, and current is not first page
          return
        }
      }
      this.viewHistory.pop()
    },
    pushHistory(history) {
      if (!this.viewHistory.includes(history)) {
        this.viewHistory.push(history)
      }
    },
    handleGoback() {
      this.viewHistory.length > 1 ? this.goback() : this.$emit('back')
    },
    handleBasicNext(data, showPreview) {
      const {
        title,
        description,
        dueDateTimestamp,
        dueInTimeframe,
        excludeWeekends
        // additionalOptions
      } = data
      //for edit wait step start
      // this.additionalOptions.skipSequentialOrder = additionalOptions?.skipSequentialOrder
      //for edit wait step end

      this.updateBasicStepEntity({
        title,
        description,
        dueDateTimestamp,
        dueInTimeframe,
        excludeWeekends
        // additionalOptions
      })

      // this.basicBoardInfo = data
      this.pushHistory(this.allViews.app)
    },
    handleAccountNext() {
      const { service } = this.action || {}
      if (service === AmServiceType.waitShadowflow) {
        this.pushHistory(this.allViews.preview)
      } else if (service === AmServiceType.externalurl) {
        this.pushHistory(this.allViews.externalURL)
      } else {
        this.pushHistory(this.allViews.waitForm)
      }
    },
    handleExternalURLNext (payload) {
      this.updateWaitAction(
        {
          data: payload
        },
        {
          data: {
            custom_request_body: { replace: true }
          }
        }
      )
      this.pushHistory(this.allViews.preview)
    },
    async handleFormNext({ formArray, invokedData: _invokedData }) {
        const preview_desc = _invokedData?.await?.description
        this.updateWaitAction({
          data: {
            user_input: formArray,
            preview_desc
          }
        })

        const ddrSourceFields = _invokedData?.variables?.map((v) => {
          return {
            key: v.key,
            type: v.type,
            label: v.label
          }
        })
        // //The fields will be used as DDR Source
        this.setDDRSourceFields(ddrSourceFields || [])

      this.pushHistory(this.allViews.preview)

    },
    onClose(e) {
      this.$emit('close', e)
    },
    beforeClose(hide) {
      this.$emit('closeOnClick')
      hide()
    },
    scrollToError() {
      this.scrollToErrorField('.el-dialog__body')
    },
    handleEmit({ action, data }) {
      this.$emit(action, data)
    },

    async addStep() {
      this.isSending = true
      const {
        title,
        description,
        dueDateTimestamp,
        dueInTimeframe,
        excludeWeekends
        // additionalOptions
      } = this.basicStepEntity

      const requestPayload = {
        noDestBoard: true,
        transactionTitle: title.trim(),
        transactionDescription: description.trim(),
        transactionDueDate: dueDateTimestamp,
        excludeWeekends
      }

      this.createWaitObject(requestPayload)
        .then(({ boardId, viewToken, transaction, board }) => {
          const addOrEditStep = this.isEditMode ? this.editWaitStep : this.addWaitStep
          addOrEditStep({
            dueDate: dueDateTimestamp || 0,
            dueInTimeframe,
            excludeWeekends,
            title,
            baseObjectSequence: transaction.sequence,
            description,
            inputBoardId: boardId,
            inputBoardViewToken: viewToken,
            steps: [],
            inputBoard: board,
            // skipSequentialOrder: additionalOptions?.skipSequentialOrder,
            automationDDRSources: this.ddrSourceFields,
            awaitOption: this.awaitOption
          })

          this.$emit('created', this.stepViewModel, {
            events: { onError: () => (this.isSending = false) }
          })
        })
        .catch((e) => {
          console.error(e)
          this.$mxMessage.error(this.$t('system_exceed_limit_error'))
        })
        .finally(() => {
          this.isSending = false
        })
    },

    async editStep() {
      await this.addStep()
    },

    getAllFormData(noNeedVerify) {
      return new Promise((resolve) => {
        const result = [
          this.basicStepEntity,
          {},
          {},
          {},
          {
            automationDDRSources: this.ddrSourceFields,
            awaitOption: this.awaitOption
          }
        ]
        resolve(result)
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.wait-dialog {
  ::v-deep .el-dialog {
    .el-dialog__header .left {
      width: 100%;
    }

    .el-dialog__footer {
      box-shadow: 0px -1px 0px #e0e0e0;
    }

    .el-dialog__body {
      padding-bottom: 0;
      padding: 0px;
      min-height: 322px;
    }
  }

  &.in-preview {
    ::v-deep .el-dialog {
      .el-dialog__body {
        padding: 24px;
        background-color: $mx-color-var-fill-quaternary;
      }

      .el-dialog__footer {
        display: none;
      }
    }
  }
}
.dialog-auto-height {
  ::v-deep {
    .el-dialog__body {
      overflow: auto;
      max-height: calc(100vh - 262px);
      display: flex;
      flex-direction: column;
    }
  }
}
.integration-form-view{
  ::v-deep{
    .integration-form-action{
      .el-button{
        width: 100%;
      }
    }
  }
}
.flex-center {
  display: flex;
  align-items: center;
}

.object-icon {
  margin-right: 10px;
  width: 24px;
}

.title-icon {
  width: 24px;
  height: 24px;
  margin-right: 8px;
}

.go-back-nav-icon {
  font-size: 16px;
  margin-right: 4px;
  cursor: pointer;
  color: $mx-color-var-text-secondary;

  &.disabled {
    cursor: default !important;
  }
}

@media (max-width: 768px) {
  .mobile-fullscreen-dialog {
    ::v-deep .el-dialog {
      .el-dialog__body {
        flex: 1;
      }
    }
  }
}
</style>
