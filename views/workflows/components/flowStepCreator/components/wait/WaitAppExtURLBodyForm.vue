<template>
  <el-form
    :model="form"
    :hide-required-asterisk="true"
    ref="form"
    class="new-form">
    <div
      class="mx-text-c1 mx-margin-left-xxs">
      {{ $t('Properties') }}*
    </div>
    <div
      class="mx-text-c4 mx-color-secondary mx-margin-left-xxs mx-margin-top-xxs">
      {{ $t('Add_the_required_properties_to_the_external_url') }}
    </div>
      <div
        v-for="(item, index) in form.customRequestBody"
        :key="item.id"
        class="key-value-item mx-flex-container"
        :style="{height: index === 0 || index === 1 ? '36px' : 'auto'}">
        <el-form-item
          :prop="`customRequestBody.${index}.key`"
          :rules="{validator: validateKeyValue, trigger: 'blur'}">
          <el-input
            v-model="item.key"
            :readonly="index === 0 || index === 1"
            :placeholder="$t('Parameter_Key')"/>
        </el-form-item>
        <el-form-item
          :prop="`customRequestBody.${index}.value`"
          :rules="{validator: validateKeyValue, trigger: 'blur'}"
          :ref="`value${index}FormItem`"
          class="mx-margin-left-xs">
          <input v-if="index === 0 || index === 1"
            class="el-input__inner static-tagify-input"
            ref="tagifyInput"
            v-model="item.value" 
            :placeholder="$t('Parameter_Value')"/>
          <MxDDRInput
            v-else
            v-model="item.value"
            :placeholder="$t('Parameter_Value')"
            popoverPlacement="bottom-end"
            @blur="$refs[`value${index}FormItem`][0].onFieldBlur()"/>
        </el-form-item>
        <i
          :tabindex="form.customRequestBody.length > 1 ? 0 : -1"
          :class="{'disabled':  index === 0 || index === 1, 'mx-clickable': index !== 0 && index !== 1}"
          class="micon-close font-icon-sm mx-color-secondary mx-margin-left-xs"
          @click="removeProperty(index)"
          @keydown.enter="removeProperty(index)"/>
      </div>
      <el-button
        v-if="form.customRequestBody.length < 100"
        type="text"
        class="mx-margin-top-xs"
        style="margin-left: 4px;"
        @click="addProperty">
        {{ $t('Add_Property') }}
      </el-button>
      <el-tooltip
        v-else
        :content="$t('max_key_value_pairs_added')"
        placement="top"
        popper-class="client-group-tooltip">
        <el-button
          :disabled="true"
          type="text"
          class="mx-margin-top-xs"
          style="margin-left: 4px;"
          >
          {{ $t('Add_Property') }}
        </el-button>
      </el-tooltip>
  </el-form>
</template>

<script>
import Tagify from '@vendor/tagify/tagify.js'
import '@vendor/tagify/tagify.css'
import { StringUtils } from '@commonUtils'
import MxDDRInput from '@views/ddr/MxDDRInput.vue'

export default {
  name: "WaitAppExtURLBodyForm",
  components: {
    MxDDRInput
  },
  data () {
    return {
      workspaceTagify: null,
      actionTagify: null,
    }
  },
  props: {
    form: {
      type: Object,
      default: () => ({})
    }
  },
  methods: {
    initComponent () {
      const settings = this._tagifySettings()
      this.workspaceTagify = new Tagify(this.$refs['tagifyInput'][0], settings)
      this.actionTagify = new Tagify(this.$refs['tagifyInput'][1], settings)
      this.workspaceTagify?.loadOriginalValues(this.form.customRequestBody[0].value)
      this.actionTagify?.loadOriginalValues(this.form.customRequestBody[1].value)
    },
    addProperty () {
      this.form.customRequestBody.push({
        key: '',
        value: ''
      })
      this.$nextTick(() => {
        document.querySelector('.key-value-item:last-of-type').scrollIntoView({
          behavior: 'smooth',
          block: 'center'
        })
      })
    },
    removeProperty (index) {
      if (this.form.customRequestBody.length > 2) {
        this.customRequestBody.splice(index, 1)
      }
    },
    getFormattedRequestBody () {
      return this.form.customRequestBody
      .reduce((acc, item) => {
        acc[item.key] = item.value  
        return acc
      }, {})
    },
    getFormData () {
      return new Promise((resolve, reject) => {
        this.$refs.form.validate(valid => {
          if (valid) {
            let data = {
              custom_request_body: this.getFormattedRequestBody()
            }
            resolve(data)
          } else {
            reject(new Error('validate failed'))
          }
        })
      })
    },
    validateKeyValue (rule, value, callback) {
      const fieldArr = rule.field.split('.')
      const index = fieldArr[1]
      let hasError = false
      if (index >=2) {
        //No need to validate hardcode workspace_id and action_id
        const fieldLabel = fieldArr[2]
        const mapItemLabel = fieldLabel === 'key' ? 'value' : 'key'
        const mapItemValue = this.form[fieldArr[0]][index][mapItemLabel].trim()
        if (value === '') {
          const noValidKeyValue = this.form.customRequestBody.every(item => !item.key && !item.value)
          if (noValidKeyValue || mapItemValue) {
            hasError = true
            callback(new Error(this.$t('Required_field')))
          }
        } else if (fieldLabel === 'key') {
          const regex = /[^a-zA-Z0-9_]/g
          if (regex.test(value)) {
            hasError = true
            callback(new Error(this.$t('variable_key_has_invalid_character_error')))
          } else if (this.form.customRequestBody.length > 1) {
            let sameKeyItem = this.form.customRequestBody.find((item, index) => item.key === value && (index + '' !== fieldArr[1]))
            //Check if the key is duplicated with jwt.jwt_key
            if (this.form.enableJWT && this.form.method === 'POST' && this.form.jwt.jwt_key === value) {
              hasError = true
              callback(new Error(this.$t('error_not_unique')))
            }
            if (sameKeyItem) {
              hasError = true
              callback(new Error(this.$t('error_not_unique')))
            }
          }
        } else if (value.trim() === '') {
          hasError = true
          callback(new Error(this.$t('Required_field')))
        }
      }

      if (!hasError) {
        callback()
      }
    },
    _tagifySettings () {
      return {
        readonly: true,
        mode: 'mix',
        mixMode: { insertAfterTag: '\u200B' },
        dropdown: { enabled: false },
        templates: {
          tag(tagData) {
            const { source, prop } = tagData
            let safeSourceName = source ? StringUtils.escapeHTML(source) : ''
            let safePropName = prop ? StringUtils.escapeHTML(prop) : ''
            let safeTitleTxt = ''
            if (safeSourceName) { safeTitleTxt += safeSourceName + ' / ' }
            if (safePropName) { safeTitleTxt += safePropName }
            safeTitleTxt = safeTitleTxt.replace(/"/g, '&quot;')
            return `
              <tag class="tagify__tag mx-ddr-tag-wrapper">
                <span class="mx-ddr-tag" title="${safeTitleTxt}">
                  <span class="brace-char">{</span>
                  ${safeSourceName ? `<span class="mx-ellipsis tag-txt-lf">${safeSourceName}</span><span>/</span>` : ''}
                  ${safePropName ? `<span class="mx-ellipsis tag-txt-lf">${safePropName}</span>` : ''}
                  <span class="brace-char">}</span>
                </span>
              </tag>`
          }
        }
      }
    }
  },
  mounted () {
    this.initComponent()
  },
}
</script>

<style lang="scss" scoped>
::v-deep .static-tagify-input {
  padding: 0px 0px !important;
}
.key-value-item {
  margin-top: 8px;
  > div {
    width: calc(50% - 16px);
    margin-bottom: 0;
  }
  i {
    margin-top: 10px;
  }
  i.disabled {
    cursor: not-allowed;
    color: $mx-color-var-label-quaternary;
  }
}

::v-deep {
  .el-form-item__error {
    margin-left: 4px;
    padding-top: 1px;
    padding-bottom: 1px;
    margin-top: 4px;
  }
}
</style>