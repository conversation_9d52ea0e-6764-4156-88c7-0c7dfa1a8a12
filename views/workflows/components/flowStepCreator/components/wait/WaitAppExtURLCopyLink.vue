<template>
  <div class="copy-link-container">
    <div class="description-container"> 
      <span class="copy-link-header">{{ $t('Completing_this_action') }}</span>
      <span class="mx-text-c4 mx-color-secondary">{{ $t('This_action_can_be_completed_by_making_a_POST_request') }}</span>
      <el-popover
        placement="right-start"
        popper-class="dark-mode-pop"
        role="tooltip"
        width="500"
        trigger="click"
        :visible-arrow="false">
        <a
          slot="reference"
          class="info-link" 
          style="position: initial; text-decoration: underline;"
          @click.prevent>{{ $t('See_instructions') }}</a>
        <div class="field-desc-content">
          <p v-safe-html="$t('wait_external_url_app_auto_complete_desc', { currentOrigin })" />
          <div>
            <b>{{ $t('Sample_call') }}:</b>
            <div class="code-block-dark">
              curl -X POST -H 'x-mx-token: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9' '{{currentOrigin}}/integration/action/complete' 
            </div>
          </div>
          <p style="margin-top: 20px;">{{ $t('As_an_optional_setting_you_can_add_Basic_Authentication_to_the_request') }}</p>
          <div>
            <b>{{ $t('Sample_call') }}:</b>
            <div class="code-block-dark">
              curl -X POST -H 'x-mx-token: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9' '{{currentOrigin}}/integration/action/complete' -u "username:password"
            </div>
          </div>
        </div>
      </el-popover>
    </div>
    <div class="copy-link-box">
      <span class="copy-link-url">{{ link }}</span>
      <el-button
        class="copy-link-btn"
        size="small"
        type="gray-branding"
        :disabled="false"
        @click="copyWebhookUrl">
        {{ isCopied ? $t('copied') : $t('share_copy_link') }}
      </el-button>
    </div>
    <BasicAuthForm
      ref="basicAuthForm"
      style="margin-top: 20px;"
      :presetWebhook="form"
      :userNamePlaceholder="`${$t('user_name')}*`"
      :passwordPlaceholder="`${$t('password')}*`"
      @passwordUpdated="passwordUpdated" />
  </div>
</template>

<script>
import { copyText } from '@views/common/utils/copyText'
import BasicAuthForm from '@views/automationBuilder/components/webhook/BasicAuthForm.vue'
export default {
  name: 'WaitAppExtURLCopyLink',
  components: {
    BasicAuthForm
  },
  props: {
    link: {
      type: String,
      default: 'https://moxtra.moxo.com/v1/completeTransobject......'
    },
    form: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      currentOrigin: window.origin,
      isCopied: false
    }
  },
  methods: {
    getFormData() {
      return new Promise((resolve, reject) => {
        this.$refs.basicAuthForm.getFormData().then(data => {
          this.form.auth = data.auth
          this.form.enableBasicAuth = data.enableBasicAuth
          resolve(data)
        }).catch(() => {
          reject(new Error('user_name or password validate failed'))
        })
      })
    },
    passwordUpdated(passwordUpdated) {
      this.$emit('passwordUpdated', passwordUpdated)
    },
    copyWebhookUrl() {
      copyText(this.link)
      this.isCopied = true
    }
  }
}
</script>

<style lang="scss" scoped>
.description-container {
  display: flex;
  flex-direction: column;
}

.copy-link-container {
  align-items: center;
  .copy-link-header {
    font-size: 19px;
    line-height: 28px;
    font-weight: 600;
    color: $mx-color-var-text-primary;
  }

  .copy-link-box {
    border: 1px solid $mx-color-var-fill-primary;
    border-radius: 6px;
    display: flex;
    align-items: center;
    height: 36px;
    margin-top: 20px;
    .copy-link-url {
      margin-left: 8px;
      font-size: 14px;
      line-height: 20px;
      font-weight: 400;
      color: $mx-color-var-text-primary;
    }
    .copy-link-btn {
      margin-left: auto;
      margin-right: 4px;
    }
  }
}
.code-block-dark {
    border-radius: 6px;
    margin-top: 4px;
    background-color: $mx-color-var-label-secondary;
    text-align: left;
    font-size: 12px;
    color: $mx-color-var-white;
    padding:10px;
}
</style>