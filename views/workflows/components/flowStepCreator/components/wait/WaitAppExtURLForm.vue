<template>
  <ContentWrapper class="wait-exturlform">
    <template slot="content">
      <div 
        class="external-url-form"
        ref="webhookRef">
      <WebhookForm
        ref="webhookForm"
        style="margin-top: 0px;"
        :labelText="'URL'"
        :enableHeaders="false"
        :urlPlaceholder="'https://www.example.com/'"
        :presetWebhook="form"
        @update:webhook_url="updateWebhookUrl" />
      <WaitAppExtURLBodyForm
        ref="requestBodyForm"
        :form="form" />
      <WaitAppExtURLMethodForm
        ref="methodForm"
        style="margin-top: 20px;"
        @testRequest="testRequest"
        :form="form"
        :isEdit="isEditMode" />
      <div class="divider"></div>
      <WaitAppExtURLCopyLink
        ref="copyLinkForm"
        :link="inboundUrl"
        :form="form"/>
    </div>
    </template>
    <template slot="footer">
      <el-button type="primary"
            :debounce-click="300"
            :data-ta="'next_btn'"
            size="medium"
            :title="$t('next')"
            @button-click="gotoNext">
        {{ $t('next') }}
      </el-button>
    </template>
  </ContentWrapper>
</template>
<script>
import { mapState, mapActions } from 'pinia'
import { useWaitBuilderStore } from '@views/stores/waitBuilder'
import ContentWrapper from '@views/integrations/integrationCenter/components/ContentWrapper'
import WebhookForm from '@views/automationBuilder/components/webhook/WebhookForm'
import WaitAppExtURLBodyForm from './WaitAppExtURLBodyForm'
import WaitAppExtURLMethodForm from './WaitAppExtURLMethodForm'
import WaitAppExtURLCopyLink from './WaitAppExtURLCopyLink'
import { MxLogger } from '../../../../../../commonUtils'

export default {
  name: 'WaitAppExtURLForm',
  components: {
    ContentWrapper,
    WebhookForm,
    WaitAppExtURLBodyForm,
    WaitAppExtURLMethodForm,
    WaitAppExtURLCopyLink
  },
  props: {
    isEditMode: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    ...mapState(useWaitBuilderStore, [
      'awaitOption',
    ]),
    inboundUrl() {
      const currentOrigin = window.origin
      return `${currentOrigin}/integration/action/complete`
    },
    passwordUpdated() {
      const action = this.awaitOption.actions?.[0]?.data
      if (this.form.auth?.password && action.auth?.password) {
        return this.form.auth?.password !== action.auth.password
      }
      return false
    },
    jwtSecretUpdated() {
      const action = this.awaitOption.actions?.[0]?.data
      if (this.form.jwt?.jwt_secret && action.jwt?.jwt_secret) {
        return this.form.jwt?.jwt_secret !== action.jwt.jwt_secret
      }
      return false
    }
  },
  data() {
    return {
      form: {
        //WebhookForm.vue is reading webhook_url, so keep using this prop name
        webhook_url: '',
        method: 'GET',
        headers: '["content-type: application/x-www-form-urlencoded"]',
        enableBasicAuth: false,
        auth: {
          user_name: '',
          password: '',
        },
        enableJWT: false,
        jwt: {
          minLength: 11,
          jwt_key: 'jwt_key',
          jwt_secret: this._generateRandomSecret(11)
        },
        //Key-value object
        custom_request_body: this.getObjectCustomRequestBody(this._defaultBodyFields()),

        //Array which includes key-value object, for better form data manage,
        //will be dropped when store
        customRequestBody: [...this._defaultBodyFields()]
      }
    }
  },
  created () {
    if(this.isEditMode) {
      this._formRestoreFromAwaitOption()
    }
  },
  methods: {
    ...mapActions(useWaitBuilderStore, [
      'testExternalURLRequest',
      'getEncryptedWebhookPassword',
      'getArrayCustomRequestBody',
      'getObjectCustomRequestBody'
    ]),
    updateWebhookUrl(webhook_url) {
      this.form.webhook_url = webhook_url
    },
    async gotoNext() {
      try {
        if (this.passwordUpdated) {
          const encryptePassword = await this.getEncryptedWebhookPassword(this.form.auth.password)
          this.form.auth.password = encryptePassword
        }
        if (this.jwtSecretUpdated) {
          const encrypteJwtSecret = await this.getEncryptedWebhookPassword(this.form.jwt.jwt_secret)
          this.form.jwt.jwt_secret = encrypteJwtSecret
        }
        const payload = this._toRequestPayload(this.form)
        this.$emit('next', payload)
      } catch (error) {
        MxLogger.error(error.message)
      }
    },
    //Form 
    getAllFormData (){
      const getFormDataPromiseFns = [
        //trigger validatations
        this.$refs.webhookForm.getFormData(),
        this.$refs.requestBodyForm.getFormData(),
        this.$refs.methodForm.getFormData(),
        this.$refs.copyLinkForm.getFormData()
      ]
      return Promise.all(getFormDataPromiseFns)
    },
    async getExtURLFormData(){
      try {
        await this.getAllFormData()
        return this._toRequestPayload(this.form)
      } catch (error) {
        return Promise.reject(error)
      }
    },
    async testRequest() {
      try {
        await this.getAllFormData()
        const payload = this._toRequestPayload(this.form, true)
        this.testExternalURLRequest(payload).then(succeed => {
          if (succeed) {
            this.$mxMessage.success(this.$t('Test_request_successfully_sent'));
          } else {
            this.$mxMessage.error(this.$t('test_request_failed'));
            }
        })
      } catch (error) {
        this.$mxMessage.error(this.$t('test_request_failed'));
      }
    },
    _toRequestPayload(formData, forTest = false) {   
      const { 
        webhook_url, 
        method, 
        headers, 
        auth, 
        customRequestBody,
        enableJWT, 
        enableBasicAuth, 
        jwt 
      } = formData
      
      const new_custom_request_body = this.getObjectCustomRequestBody(customRequestBody)
      const ret = { 
        webhook_url, 
        method, 
        headers, 
        auth, 
        enableJWT, 
        enableBasicAuth,
        custom_request_body: new_custom_request_body,
        jwt,
        auth
      }
      
      if (forTest) {
        ret.headers = JSON.parse(headers)
      }
      return ret
    },
    _defaultBodyFields () {
      return [{
        key: "workspace_id",
        value: `[[{"source":"Workspace","prop":"ID"}]]`
      },{
        key: "action_id",
        value: `[[{"source":"Wait Action","prop":"ID"}]]`
      }]
    },
    _generateRandomSecret (len) {
      let secret = ''
      const charset = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
      for (let i = 0; i < len; i++) {
        secret += charset.charAt(Math.floor(Math.random() * charset.length));
      }
      return secret
    },
    _formRestoreFromAwaitOption() {
      const action = this.awaitOption.actions?.[0]?.data
      //For keep reactivity
      this.form.webhook_url = action.webhook_url
      this.form.method = action.method
      this.form.headers = action.headers
      this.form.enableBasicAuth = action.enableBasicAuth
      this.form.auth = action.auth
      this.form.enableJWT = action.enableJWT
      this.form.jwt = action.jwt
      this.form.custom_request_body = action.custom_request_body
      this.form.customRequestBody = this.getArrayCustomRequestBody(action.custom_request_body)
    },
  }
}
</script>

<style lang="scss" scoped>
.wait-exturlform {
  padding: 0px;
}

.divider {
  width: calc(100% + 56px);
  height: 1px;
  background-color: $mx-color-var-fill-tertiary;
  margin: 24px 24px 24px -28px;
}
</style>