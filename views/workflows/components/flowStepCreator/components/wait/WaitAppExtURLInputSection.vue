<template>
  <ul>
    <li class="input-field">
      <div class="input-label">{{ $t('URL')}}</div>
      <div class="input-value">
        {{webhookUrl}}
      </div>
    </li>
    <li v-if='methodData' 
        class="input-field">
      <div class="input-label"> {{ $t('HTTP_Method')}} </div>
      <div class="input-value">
        {{methodData}}
      </div>
    </li>
    <li v-if='showAuthentication' 
        class="input-field">
      <div class="input-label"> {{ $t('Basic_Authentication')}} </div>
      <div class="input-value">
        <el-form class="authentication_form">
          <MxFloatingInput
            v-model="authNameAndPassword.user_name"
            :label="$t('user_name')"
            :readonly="true" />
          <MxFloatingPassword
            v-model="authNameAndPassword.password"
            :label="$t('password')"
            :showHideButton="false"
            :readonly="true" />
        </el-form>

      </div>
    </li>
    <li
      class="input-field">
      <div class="input-label">{{ $t('Properties')}}</div>
      <div class="input-value">
        <template
          v-if="customRequestBody">
          <el-button
            size="small"
            type="text"
            @click="viewRequestBody">
            {{ $t('View_Detail') }}
          </el-button>
          <i
            v-if="hasInvalidDDRVariable"
            class="micon-mep-warning font-icon-sm danger" />
        </template>
        <div
          v-else>
          {{ $t('From_an_action') }}
        </div>
      </div>
    </li>
  </ul>
</template>
<script>
  import MxFloatingInput from '@views/common/components/input/MxFloatingInput.vue'
  import MxFloatingPassword from '@views/identity/components/MxFloatingPassword'
  import WaitAppExtURLRequestBodyDialog from './WaitAppExtURLRequestBodyDialog.vue'
  import { popupFactory } from '@views/common/useComponent'

  export default {
    name: 'WaitAppExtURLInputSection',
    components: {
      MxFloatingInput,
      MxFloatingPassword,
      WaitAppExtURLRequestBodyDialog
    },
    props:{
      stepModel:{ 
        type:Object
      },
      awaitOptionAction:{
        type: Object,
        default: () => ({})
      },
      sourceStepStatus: {
        type: Object,
        default: () => ({
          isDeleted: false, 
          isInvalid: false
        })
      },
      stepIsCompleted: {
        type: Boolean,
        default: false
      },
    },
    data (){
      return {
       
      }
    },
    computed:{
      hasInvalidDDRVariable (){
        return this.stepModel?.hasInvalidDDRVariable
      },
      stepInfo (){
        return this.awaitOptionAction?.data?.sourceStepInfo || {}
      },
      methodData (){
        return this.awaitOptionAction?.data?.method || 'POST'
      },
      authNameAndPassword (){
        return this.awaitOptionAction?.data?.auth
      },
      showAuthentication () {
        if(this.awaitOptionAction?.data?.enableBasicAuth && this.authNameAndPassword) {
          const { password, user_name } = this.authNameAndPassword
          if(password && user_name) {
            return true
          }
        }
        return false
      },
      webhookUrl (){
        return this.awaitOptionAction?.data?.webhook_url
      },
      customRequestBody () {
        return this.awaitOptionAction?.data?.custom_request_body
      }
    },
    methods: {
      viewRequestBody () {
        const [show] = popupFactory(WaitAppExtURLRequestBodyDialog)({}, {
          stepModel: this.stepModel,
          awaitOptionAction: this.awaitOptionAction
        })
        show()
      }
    }
  }
</script>
<style scoped lang="scss">
  .input-field{
    display: flex;
    margin-bottom: 12px;
    .input-label{
      color: $mx-color-var-label-secondary;
      width: 100px;
      margin-right: 8px;
      flex-shrink: 0;
    }
    .input-value{
      &.is-deleted{
        color: $mx-color-var-negative;
        font-weight: 600;
      }

      .authentication_form {
        ::v-deep {
          .el-form-item {
            outline: 0px;
            pointer-events: none;
            input {
              border: 1px solid #E0E0E0 !important;
            }
          }

          & > .el-form-item {
            min-height: 44px;
          }
  
          & > .el-form-item:first-child {
            margin-bottom: 12px;
          }

          & > .el-form-item:last-child {
            input {
              text-overflow: unset !important;
            }
          }
        }
      }
    }
  }
</style>