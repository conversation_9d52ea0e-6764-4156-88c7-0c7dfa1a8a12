<template>
  <el-form
    :model="form"
    ref="form">
    <div style="display: flex;">
      <div class="mx-text-c1 mx-margin-left-xxs">
        {{ $t('HTTP_Method') }}
      </div>
      <el-button 
        style="margin-left: auto;"
        type="primary" 
        size="mini"
        @click="testRequest">{{$t('Test_request')}}</el-button>
    </div>
    <div class="radio-panel-container">
    <div :class="['radio-item', {'is-active': form.method === 'GET'}]">
      <el-radio 
        label="GET"
        v-model="form.method">
        <div class="radio-label-con">
          <span class="mx-text-c1">GET</span>
          <span class="mx-text-c2 mx-color-secondary">{{$t('Submit_the_data_as_a_query_string_into_your_URL')}}</span>
        </div>
      </el-radio>
      <div>
        <span class="sample-text">{{$t('ie')}}: </span>
        <span class="sample-text">{{queryFormDataStr}} </span>
      </div>
    </div>
    <div :class="['radio-item', {'is-active': form.method === 'POST'}]">
      <el-radio 
        label="POST"
        v-model="form.method">
        <div class="radio-label-con">
          <span class="mx-text-c1">POST</span>
          <span class="mx-text-c2 mx-color-secondary">{{$t('Submit_the_data_as_form-data_(key_value)_to_your_app')}}</span>
        </div>
      </el-radio>
      <div 
        v-show="form.method === 'POST'">
        <span class="sample-text">{{$t('ie')}}: </span>
        <span class="sample-text">{{bodyFormDataStr}} </span>
      </div>
      <div 
      v-show="form.method === 'POST'"
      class="switch-block">
      <div class="divider" />
      <div class="switch-header">
        <div>
          <span>{{$t('Securely_submit_data_with_JWT')}}</span>
          <a
          target="_blank"
          class="info-link underline-link"
          href="https://jwt.io/introduction">{{$t('what_is_jwt')}}</a>
          <el-switch 
            size="small"
            v-model="form.enableJWT" />
        </div>
      </div>
      <div 
        v-if="form.enableJWT"
        class="switch-content">
        <div class="switch-desc">{{$t('Securely_submit_data_with_JWT_desc')}}</div>
        <div class="switch-form">
          <div class="switch-form-item">
            <label>
              {{$t('Form_Key')}}
              <el-tooltip
                popper-class="binder-btns-tooltip"
                :content="$t('Form_Key_info')"
                placement="top">
                <i class="micon-info" />
              </el-tooltip>
            </label>
            <el-form-item
              prop="jwt.jwt_key"
              :rules="{validator: validateKeyValue, trigger: 'blur'}">
            <el-input
              v-model="form.jwt.jwt_key"
              size="small"
              maxLength="50"
              :disabled="!form.enableJWT"" />
            </el-form-item>
          </div>
          <div class="switch-form-item">
            <label>
              {{$t('JWT_Secret')}}
              <el-tooltip
                popper-class="binder-btns-tooltip"
                :content="$t('JWT_Secret_info')"
                placement="top">
                <i class="micon-info" />
              </el-tooltip>
            </label>
            <el-form-item
              prop="jwt.jwt_secret"
              :rules="{validator: validateKeyValue, trigger: 'blur'}">
              <el-input
                v-model="form.jwt.jwt_secret"
                :type="isEdit ? 'password' : 'text'"
                size="small"
                maxLength="64"
                minLength="11"
                :disabled="!form.enableJWT"" />
            </el-form-item>
          </div>
        </div>
        <span 
          v-show="form.enableJWT"
          class="sample-text">{{$t('ie')}}: {{jwtFormDataStr}}</span>
      </div>
    </div>
    </div>
  </div>
</el-form>
</template>

<script>
export default {
  name: 'WaitAppExternalURLMethodForm',
  props: {
    isEdit: {
      type: Boolean,
      default: false
    },
    form: {
      type: Object,
      default: () => ({
        customRequestBody: []
      })
    }
  },
  computed: {
    queryFormDataStr() {
      const endpoint = this.form.webhook_url || 'https://www.example.com'
      const bodyFormData = this.form.customRequestBody.filter(item => item.key !== 'workspace_id' && item.key !== 'action_id')
      if (bodyFormData.length > 0) {
        return `${endpoint}?workspace_id=<workspace_id>&action_id=<action_id>&${bodyFormData.map(item => `${item.key}=${item.value}`).join('&')}`
      } else {
        return `${endpoint}?workspace_id=<workspace_id>&action_id=<action_id>`
      }
    },
    bodyFormDataStr() {
      const bodyFormData = this.form.customRequestBody.filter(item => item.key !== 'workspace_id' && item.key !== 'action_id')
      const staticPrefix = 'header: "content-type": "application/x-www-form-urlencoded" form-data: ["workspace_id", "<workspace_id>"],["action_id", "<action_id>"]'
      if (bodyFormData.length > 0) {
        return `${staticPrefix}, ${bodyFormData.map(item => `["${item.key}", "${item.value}"]`).join(', ')}`
      } else {
        return staticPrefix
      }
    },
    jwtFormDataStr() {
      return `form-data: ["${this.form.jwt.jwt_key}", "${this.isEdit ? 'aFX15TIlcjH...' : this.form.jwt.jwt_secret}"]`
    },
  },
  methods: {
    getFormData () {
      return new Promise((resolve, reject) => {
        this.$refs.form.validate(valid => {
          if (valid) {
            let data = {
              method: this.form.method,
              enableJWT: this.form.enableJWT,
              jwt: this.form.jwt
            }
            resolve(data)
          } else {
            reject(new Error('validate failed'))
          }
        })
      })
    },
    validateKeyValue (rule, value, callback) {
      if (this.form.enableJWT && this.form.method === 'POST') {
        const trimValue = value?.trim() || ''
        if (!trimValue) {
          callback(new Error(this.$t('Required_field')))
        } else {
          if (rule.field === 'jwt.jwt_key') {
            //Check if the formKey is already in the custom_request_body
            if (this.form.customRequestBody.some(item => item.key === trimValue)) {
              callback(new Error(this.$t('error_not_unique')))
            } else {
              callback()
            }
          }
          if (rule.field === 'jwt.jwt_secret') {
            const minLength = this.form.jwt.minLength
            if (trimValue.length < minLength) {
              callback(new Error(this.$t('Use_at_least_length_characters_warn', {minLength})))
            } else {
              callback()
            }
          }
          callback()
        }
      } else {
        callback()
      }
    },
    testRequest() {
      this.$emit('testRequest')
    }
  }
}
</script>

<style lang="scss" scoped>
.radio-panel-container {
  margin-top: 15px;
  border-radius: 6px;
  border: 1px solid $mx-color-var-card-border;
  .radio-item {
    padding: 12px;
    + .radio-item {
      border-top: 1px solid $mx-color-var-fill-tertiary;
    }
    &:first-child {
      border-top-left-radius: 6px;
      border-top-right-radius: 6px;
    }
    &:last-child {
      border-bottom-left-radius: 6px;
      border-bottom-right-radius: 6px;
    }
    &.is-active {
      background-color: $mx-color-var-bg-tertiary;
    }
    .el-radio {
      ::v-deep .el-radio__label {
        padding-left: 8px;
      }
      + div {
        margin-left: 24px;
      }
    }
    ::v-deep .prefix-step-img {
      margin-top: -4px;
    }
    ::v-deep .el-form-item__label {
      color: $mx-color-var-black;
      margin-left: 4px;
      margin-bottom: 7px;
    }

    ::v-deep .el-form-item__error {
        position: static;
        margin-left: 4px;
        padding-top: 0px;
        padding-bottom: 1px;
        margin-top: -8px;
    }
  }
  .switch-block {
    padding: 8px 22px 8px 0px;
    border-radius: 6px;
    margin-left: 22px;
    margin-bottom: 10px;
  .switch-header {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    > div {
      gap: 10px;
      display: flex;
      align-items: center;
      span {
        font-size: 14px;
        font-weight: 400;
        line-height: 20px;
        color: $mx-color-var-text-primary;
      }
      .switch-wrap {
        line-height: 16px;
        margin-left: auto;
      }
    }
    .info-link {
      position: initial;
    }
  }

  .switch-content {
    .switch-desc {
      margin-top: 8px;
      font-size: 12px;
      color: $mx-color-var-label-secondary;
    }
    .switch-form {
      display: flex;
      gap: 50px;
      margin-top: 12px;
      .switch-form-item {
        font-size: 13px;
        > label {
          font-weight: 600;
        }
        i.micon-info {
          font-size: 14px;
          font-weight: normal;
          margin-left: 10px;
        }
        .basic-auth-pwd {
          ::v-deep .el-input__suffix-inner {
            line-height: 24px;
          }
        }
      }
    }
    .sample-text {
      font-size: 12px;
      line-height: 16px;
      margin-top: 10px;
      color: $mx-color-var-text-secondary;
    }
  }
  ::v-deep .new-form {
    > .el-form-item {
      .formHeader {
        font-size: 13px;
      }
    }
  }
}
}
.sample-text {
  font-size: 12px;
  line-height: 16px;
  margin-top: 10px;
  color: $mx-color-var-text-secondary;
}
.divider {
  width: calc(100% - 28px);
  height: 1px;
  margin-bottom: 12px;
  background-color: $mx-color-var-fill-tertiary;
}
.underline-link {
  text-decoration: underline;
}
</style>
