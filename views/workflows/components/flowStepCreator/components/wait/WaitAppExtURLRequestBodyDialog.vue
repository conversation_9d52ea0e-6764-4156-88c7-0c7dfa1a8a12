<template>
  <BaseModal
    :visible="true"
    :title="$t('Properties')"
    :modalOption="{
      width: '540px'
    }"
    @close="$emit('close')">
    <div class="payload-table" slot="content">
      <table style="width: 100%;">
        <thead>
          <tr>
            <th style="text-align: left; padding: 8px;">{{ $t('Key') }}</th>
            <th style="text-align: left; padding: 8px;">{{ $t('Value') }}</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="(item, index) in customRequestBody" :key="index">
            <td >{{ item.key }}</td>
            <input v-if="index === 0 || index === 1"
              :value="item.value"
              readonly
              class="el-input__inner static-tagify-input"
              ref="tagifyInput"/>
            <td v-else style="padding: 8px; vertical-align: center;">
              <MxDDRTextDisplay :text="item.value" />
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </BaseModal>
</template>
  
<script>
import Tagify from '@vendor/tagify/tagify.js'
import '@vendor/tagify/tagify.css'
import { StringUtils } from '@commonUtils'
import { mapActions } from 'pinia'
import { visibleMixin } from '@views/common/components/modals/mixins'
import { useWaitBuilderStore } from '@views/stores/waitBuilder'
import MxDDRTextDisplay from '@views/ddr/MxDDRTextDisplay.vue'
export default {
  name: "WaitAppExtURLRequestBodyDialog",
  components: {MxDDRTextDisplay},
  mixins: [visibleMixin],
  props: {
    stepModel: {
      type: Object,
      required: false
    },
    awaitOptionAction: {
      type: Object,
      required: true,
      default: () => ({})
    }
  },
  computed: {
    customRequestBody () {
      return this.getArrayCustomRequestBody(this.awaitOptionAction.data.custom_request_body)
    },
    ddrResolved (){
      if (this.stepModel) {
        return this.stepModel?.isNotStarted ? false : true
      }
      return false
    }
  },
  mounted () {
    this.$nextTick(() => {
      this.initComponent()
    })
  },
  methods: {
    ...mapActions(useWaitBuilderStore, [
      'getArrayCustomRequestBody',
    ]),
    initComponent () {
      const settings = this._tagifySettings()
      if(this.ddrResolved){
        this.workspaceTagify = this.$refs['tagifyInput'][0]
        this.workspaceTagify.value = this.stepModel.outputBoardId
        this.actionTagify = this.$refs['tagifyInput'][1]
        this.actionTagify.value = this.stepModel.sequence
        //apply margin-left: 8px to workspaceTagify
        this.workspaceTagify.style.marginLeft = '8px'
        this.actionTagify.style.marginLeft = '8px'
      } else {
        this.workspaceTagify = new Tagify(this.$refs['tagifyInput'][0], settings)
        this.actionTagify = new Tagify(this.$refs['tagifyInput'][1], settings)
        this.workspaceTagify?.loadOriginalValues(this.customRequestBody[0].value)
        this.actionTagify?.loadOriginalValues(this.customRequestBody[1].value)
      }
    },
    _tagifySettings () {
      return {
        readonly: true,
        mode: 'mix',
        mixMode: { insertAfterTag: '\u200B' },
        dropdown: { enabled: false },
        templates: {
          tag(tagData) {
            const { source, prop } = tagData
            let safeSourceName = source ? StringUtils.escapeHTML(source) : ''
            let safePropName = prop ? StringUtils.escapeHTML(prop) : ''
            let safeTitleTxt = ''
            if (safeSourceName) { safeTitleTxt += safeSourceName + ' / ' }
            if (safePropName) { safeTitleTxt += safePropName }
            safeTitleTxt = safeTitleTxt.replace(/"/g, '&quot;')
            return `
              <tag class="tagify__tag mx-ddr-tag-wrapper">
                <span class="mx-ddr-tag" title="${safeTitleTxt}">
                  <span class="brace-char">{</span>
                  ${safeSourceName ? `<span class="mx-ellipsis tag-txt-lf">${safeSourceName}</span><span>/</span>` : ''}
                  ${safePropName ? `<span class="mx-ellipsis tag-txt-lf">${safePropName}</span>` : ''}
                  <span class="brace-char">}</span>
                </span>
              </tag>`
          }
        }
      }
    }
  }
}
</script>
  
<style lang="scss" scoped>
::v-deep .tagify__input {
  white-space: nowrap;
  overflow-x: auto;
  min-height: 24px;
  line-height: 24px;
  height: 30px;
  scrollbar-width: none;
  display: inline-block;
  &::-webkit-scrollbar {
    display: none;
  }
  .mx-ddr-tag-wrapper {
    vertical-align: bottom;
    &:last-of-type {
      margin-right: 2px;
    }
  }
}
::v-deep .static-tagify-input {
  border: none !important;
  padding: 0px 0px 0px 0px;
  margin-top: 5px;

  &:focus,
  &:hover {
    border: none !important;
    outline: none !important;
    box-shadow: none !important;
    background: transparent !important;
    transition: none !important;
}
}


::v-deep .el-dialog {
  min-height: 440px;
  .el-dialog__body {
    min-height: 596px;
    max-height: calc(100vh - 196px);
  }
  .key-value-container {
    font-size: 12px;
    padding: 8px;
    margin: 0 4px;
    border-radius: 6px;
    overflow: auto;
    min-height: 340px;
    max-height: calc(100vh - 244px);
    color: $mx-color-var-text-secondary;
    background-color: $mx-color-var-bg-tertiary;
    border: 1px solid $mx-color-var-card-border;
    .key-value-item {
      display: flex;
      padding-left: 8px;
      white-space: nowrap;
      line-height: 20px;
      align-items: center;
      &:last-child .comma {
        display: none;
      }
      .mx-ddr-text-disp {
        > tag:not(.is-invalid) {
          padding: 2px;
          color: $mx-color-var-black;
        }
      }
    }
  }
  .payload-table {
    width: 100%;
    border: 1px solid $mx-color-var-card-border;
    border-radius: 8px;
    border-collapse: collapse;
    border-spacing: 0;
    overflow-y: auto;
    max-height: 360px;
    td {
      padding: 12px;
      min-width: 179px;
    }
    tr {
      border-bottom: 1px solid $mx-color-var-card-border;
    }
    tr:last-child {
      border-bottom: none;
    }
    thead tr {
      background-color: $mx-color-var-bg-tertiary;
    }
  }
}
  </style>